/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.lookup.util;

import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.util.Preconditions;

import org.apache.commons.lang3.tuple.Pair;

public class LookupUtil {

    /**
     * According to the key index, get fieldGetters from rowType.
     *
     * @param rowType row type.
     * @param keyIndex the key index.
     * @return field getters.
     */
    public static RowData.FieldGetter[] createFieldGetters(RowType rowType, int[] keyIndex) {
        RowData.FieldGetter[] fieldGetters = new RowData.FieldGetter[keyIndex.length];
        for (int i = 0; i < keyIndex.length; i++) {
            LogicalType type = rowType.getTypeAt(keyIndex[i]);
            fieldGetters[i] = RowData.createFieldGetter(type, i);
        }
        return fieldGetters;
    }

    public static Pair<String[], int[]> getKeyNamesAndIndexes(
            int[][] keys, DataType physicalRowDataType) {
        String[] keyNames = new String[keys.length];
        int[] keyIndexes = new int[keys.length];
        for (int i = 0; i < keyNames.length; i++) {
            int[] innerKeyArr = keys[i];
            Preconditions.checkArgument(
                    innerKeyArr.length == 1, "Lookup only support non-nested look up keys");
            keyNames[i] = DataType.getFieldNames(physicalRowDataType).get(innerKeyArr[0]);
            keyIndexes[i] = innerKeyArr[0];
        }
        return Pair.of(keyNames, keyIndexes);
    }
}
