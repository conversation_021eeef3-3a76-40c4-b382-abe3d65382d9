/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.util;

import org.apache.flink.annotation.Public;
import org.apache.flink.table.factories.FactoryUtil;
import org.apache.flink.table.utils.ChunJunFactoryHelper;

/**
 * Sets a context class loader in a "try-with-resources" pattern.
 *
 * <pre>{@code
 * try (FlinkSQLContext ignored = FlinkSQLContext.of(classloader)) {
 *     // code that needs the context flink sql
 * }
 * }</pre>
 *
 * <p>This is conceptually the same as the code below.
 *
 * <pre>{@code
 * try {
 *      FactoryUtil.setFactoryUtilHelp(factoryHelper);
 *      TableFactoryService.setFactoryUtilHelp(factoryHelper);
 *     // code that needs the context flink sql
 * } finally {
 *     FactoryUtil.getFactoryHelperThreadLocal().remove();
 *     TableFactoryService.getChunJunFactoryHelperThreadLocal().remove();
 * }
 * }</pre>
 */
@Public
public final class FlinkSQLContext implements AutoCloseable {

    /**
     * Sets the context class loader to the given ClassLoader and returns a resource that sets it
     * back to the current context ClassLoader when the resource is closed.
     *
     * <pre>{@code
     * try (FlinkSQLContext ignored = FlinkSQLContext.of(classloader)) {
     *     // code that needs the context class loader
     * }
     * }</pre>
     */
    public static FlinkSQLContext of(ChunJunFactoryHelper factoryHelper) {
        FactoryUtil.setFactoryUtilHelp(factoryHelper);
        // TODO
        // TableFactoryService.setFactoryUtilHelp(factoryHelper);

        return new FlinkSQLContext();
    }

    // ------------------------------------------------------------------------
    private FlinkSQLContext() {}

    @Override
    public void close() {
        FactoryUtil.getFactoryHelperThreadLocal().remove();
        // TODO
        // TableFactoryService.getChunJunFactoryHelperThreadLocal().remove();
    }
}
