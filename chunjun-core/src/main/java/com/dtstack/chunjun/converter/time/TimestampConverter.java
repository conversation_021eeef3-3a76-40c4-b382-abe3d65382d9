/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.converter.time;

import com.dtstack.chunjun.common.util.DateUtil;

import org.apache.flink.table.utils.ThreadLocalCache;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.TimeZone;

public class TimestampConverter implements ITimeTypeConverter<Timestamp> {

    private static final TimeZone LOCAL_TZ = TimeZone.getDefault();

    private static final String[] DEFAULT_DATETIME_FORMATS =
            new String[] {
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-dd HH:mm:ss.S",
                "yyyy-MM-dd HH:mm:ss.SS",
                "yyyy-MM-dd HH:mm:ss.SSS",
                "yyyy-MM-dd HH:mm:ss.SSSS",
                "yyyy-MM-dd HH:mm:ss.SSSSS",
                "yyyy-MM-dd HH:mm:ss.SSSSSS",
                "yyyy-MM-dd HH:mm:ss.SSSSSSS",
                "yyyy-MM-dd HH:mm:ss.SSSSSSSS",
                "yyyy-MM-dd HH:mm:ss.SSSSSSSSS"
            };

    private static final String DATE_FORMAT_STRING = "yyyy-MM-dd";

    /** The UTC time zone. */
    public static final TimeZone UTC_ZONE = TimeZone.getTimeZone("UTC");

    /**
     * A ThreadLocal cache map for SimpleDateFormat, because SimpleDateFormat is not thread-safe.
     * (string_format) => formatter
     */
    private static final ThreadLocalCache<String, SimpleDateFormat> FORMATTER_CACHE =
            new ThreadLocalCache<String, SimpleDateFormat>() {
                @Override
                public SimpleDateFormat getNewInstance(String key) {
                    return new SimpleDateFormat(key);
                }
            };

    @Override
    public Timestamp convert(Object in) {
        if (null == in) {
            return null;
        }

        if (in instanceof Timestamp) {
            return fromTimestamp((Timestamp) in);
        }

        if (in instanceof String) {
            return fromString(String.valueOf(in));
        }

        throw new UnsupportedOperationException(
                String.format(
                        "TimestampConverter can not convert val [%s], type [%s]",
                        in, in.getClass()));
    }

    @Override
    public Timestamp fromTimestamp(Timestamp in) {
        return in;
    }

    @Override
    public Timestamp fromInteger(Integer in) {
        return new Timestamp(in - LOCAL_TZ.getOffset(in));
    }

    @Override
    public Timestamp fromString(String in) {
        return DateUtil.columnToTimestamp(in, null);
    }
}
