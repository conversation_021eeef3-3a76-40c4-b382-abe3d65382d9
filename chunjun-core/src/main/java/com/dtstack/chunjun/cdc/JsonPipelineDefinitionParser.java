package com.dtstack.chunjun.cdc;

import org.apache.flink.cdc.common.configuration.Configuration;
import org.apache.flink.cdc.common.event.SchemaChangeEventType;
import org.apache.flink.cdc.common.event.SchemaChangeEventTypeFamily;
import org.apache.flink.cdc.common.pipeline.SchemaChangeBehavior;
import org.apache.flink.cdc.composer.definition.ModelDef;
import org.apache.flink.cdc.composer.definition.PipelineDef;
import org.apache.flink.cdc.composer.definition.RouteDef;
import org.apache.flink.cdc.composer.definition.SinkDef;
import org.apache.flink.cdc.composer.definition.SourceDef;
import org.apache.flink.cdc.composer.definition.TransformDef;
import org.apache.flink.cdc.composer.definition.UdfDef;
import org.apache.flink.core.fs.FSDataInputStream;
import org.apache.flink.core.fs.FileSystem;
import org.apache.flink.core.fs.Path;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.type.TypeReference;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;

import static org.apache.flink.cdc.common.pipeline.PipelineOptions.PIPELINE_SCHEMA_CHANGE_BEHAVIOR;
import static org.apache.flink.cdc.common.utils.ChangeEventUtils.resolveSchemaEvolutionOptions;

public class JsonPipelineDefinitionParser extends AbstractPipelineDefinitionParser {
    @Override
    public PipelineDef parse(Path pipelineDefPath, Configuration globalPipelineConfig)
            throws Exception {
        FileSystem fileSystem = FileSystem.get(pipelineDefPath.toUri());
        FSDataInputStream pipelineInStream = fileSystem.open(pipelineDefPath);
        return parse(mapper.readTree(pipelineInStream), globalPipelineConfig);
    }

    private PipelineDef parse(JsonNode jsonNode, Configuration globalPipelineConfig) {
        JsonNode content = jsonNode.get("job").get("content").get(0);
        // json格式的cdc任务
        // Pipeline configs are optional
        List<UdfDef> udfDefs = new ArrayList<>();
        final List<ModelDef> modelDefs = new ArrayList<>();
        JsonNode pipelineConfNode = jsonNode.get("job").get("setting").get("pipelineConf");
        if (pipelineConfNode != null) {
            Optional.ofNullable(((ObjectNode) pipelineConfNode).remove(UDF_KEY))
                    .ifPresent(node -> node.forEach(udf -> udfDefs.add(toUdfDef(udf))));

            Optional.ofNullable(((ObjectNode) pipelineConfNode).remove(MODEL_KEY))
                    .ifPresent(node -> modelDefs.addAll(parseModels(node)));

            // chunjun 通过pipelineConf里的type字段值为cdc来区分是否是cdc任务，chunjun自己定义的参数需要去除
            ((ObjectNode) pipelineConfNode).remove(TYPE_KEY);
        }

        Configuration userPipelineConfig = toPipelineConfig(pipelineConfNode);
        SchemaChangeBehavior schemaChangeBehavior =
                userPipelineConfig.get(PIPELINE_SCHEMA_CHANGE_BEHAVIOR);

        JsonNode parameter = content.get(READER_KEY).get("parameter");

        boolean split = false;
        JsonNode splitNode = ((ObjectNode) parameter).remove(SPLIT_KEY);
        if (splitNode != null) {
            split = Boolean.parseBoolean(splitNode.asText());
        }
        boolean pavingData = true;
        JsonNode pavingDataNode = ((ObjectNode) parameter).remove(PAVING_KEY);
        if (pavingDataNode != null) {
            pavingData = Boolean.parseBoolean(pavingDataNode.asText());
        }

        SourceDef sourceDef = toSourceDef(content.get(READER_KEY));

        SinkDef sinkDef =
                toSinkDef(content.get(WRITER_KEY), schemaChangeBehavior, split, pavingData);

        // Transforms are optional
        List<TransformDef> transformDefs = new ArrayList<>();
        Optional.ofNullable(content.get(TRANSFORM_KEY))
                .ifPresent(
                        node ->
                                node.forEach(
                                        transform -> transformDefs.add(toTransformDef(transform))));

        // Routes are optional
        List<RouteDef> routeDefs = new ArrayList<>();
        Optional.ofNullable(content.get(ROUTE_KEY))
                .ifPresent(node -> node.forEach(route -> routeDefs.add(toRouteDef(route))));

        // Merge user config into global config
        Configuration pipelineConfig = new Configuration();
        pipelineConfig.addAll(globalPipelineConfig);
        pipelineConfig.addAll(userPipelineConfig);

        return new PipelineDef(
                sourceDef, sinkDef, routeDefs, transformDefs, udfDefs, modelDefs, pipelineConfig);
    }

    @Override
    public PipelineDef parse(String pipelineDefText, Configuration globalPipelineConfig)
            throws Exception {
        return parse(mapper.readTree(pipelineDefText), globalPipelineConfig);
    }

    protected Configuration toPipelineConfig(JsonNode pipelineConfigNode) {
        if (pipelineConfigNode == null || pipelineConfigNode.isNull()) {
            return new Configuration();
        }
        Map<String, String> pipelineConfigMap =
                mapper.convertValue(
                        pipelineConfigNode, new TypeReference<Map<String, String>>() {});
        return Configuration.fromMap(pipelineConfigMap);
    }

    private SourceDef toSourceDef(JsonNode sourceNode) {
        String type = sourceNode.get("name").asText();
        JsonNode parameter = sourceNode.get("parameter");
        Map<String, String> sourceMap =
                mapper.convertValue(parameter, new TypeReference<Map<String, String>>() {});

        if (type.equals("binlogreader") || type.equals("binlogsource")) {
            sourceMap = new MysqlConvert().convert(sourceMap);
        }

        return new SourceDef(type, type + "-source", Configuration.fromMap(sourceMap));
    }

    private SinkDef toSinkDef(
            JsonNode jsonNode,
            SchemaChangeBehavior schemaChangeBehavior,
            boolean split,
            boolean pavingData) {
        List<String> includedSETypes = new ArrayList<>();
        List<String> excludedSETypes = new ArrayList<>();

        JsonNode sinkNode = jsonNode.get("parameter");

        boolean excludedFieldNotPresent = sinkNode.get(EXCLUDE_SCHEMA_EVOLUTION_TYPES) == null;

        Optional.ofNullable(sinkNode.get(INCLUDE_SCHEMA_EVOLUTION_TYPES))
                .ifPresent(e -> e.forEach(tag -> includedSETypes.add(tag.asText())));

        Optional.ofNullable(sinkNode.get(EXCLUDE_SCHEMA_EVOLUTION_TYPES))
                .ifPresent(e -> e.forEach(tag -> excludedSETypes.add(tag.asText())));

        if (includedSETypes.isEmpty()) {
            // If no schema evolution types are specified, include all schema evolution types by
            // default.
            Arrays.stream(SchemaChangeEventTypeFamily.ALL)
                    .map(SchemaChangeEventType::getTag)
                    .forEach(includedSETypes::add);
        }

        if (excludedFieldNotPresent && SchemaChangeBehavior.LENIENT.equals(schemaChangeBehavior)) {
            // In lenient mode, we exclude DROP_TABLE and TRUNCATE_TABLE by default. This could be
            // overridden by manually specifying excluded types.
            Stream.of(SchemaChangeEventType.DROP_TABLE, SchemaChangeEventType.TRUNCATE_TABLE)
                    .map(SchemaChangeEventType::getTag)
                    .forEach(excludedSETypes::add);
        }

        Set<SchemaChangeEventType> declaredSETypes =
                resolveSchemaEvolutionOptions(includedSETypes, excludedSETypes);

        if (sinkNode instanceof ObjectNode) {
            ((ObjectNode) sinkNode).remove(INCLUDE_SCHEMA_EVOLUTION_TYPES);
            ((ObjectNode) sinkNode).remove(EXCLUDE_SCHEMA_EVOLUTION_TYPES);
        }

        Map<String, String> sinkMap =
                mapper.convertValue(sinkNode, new TypeReference<Map<String, String>>() {});

        String type = jsonNode.get("name").asText();
        if (type.equals("kafkawriter") || type.equals("kafkasink")) {
            sinkMap = new KafkaConvert().convert(sinkMap);
            sinkMap.put(SPLIT_KEY, String.valueOf(split));
            sinkMap.put(PAVING_KEY, String.valueOf(pavingData));
        }

        return new SinkDef(type, type + "-sink", Configuration.fromMap(sinkMap), declaredSETypes);
    }
}
