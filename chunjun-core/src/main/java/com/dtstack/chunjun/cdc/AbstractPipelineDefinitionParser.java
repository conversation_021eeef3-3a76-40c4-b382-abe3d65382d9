package com.dtstack.chunjun.cdc;

import org.apache.flink.cdc.common.configuration.Configuration;
import org.apache.flink.cdc.common.utils.Preconditions;
import org.apache.flink.cdc.common.utils.StringUtils;
import org.apache.flink.cdc.composer.definition.ModelDef;
import org.apache.flink.cdc.composer.definition.RouteDef;
import org.apache.flink.cdc.composer.definition.TransformDef;
import org.apache.flink.cdc.composer.definition.UdfDef;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.type.TypeReference;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.dataformat.yaml.YAMLFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.apache.flink.cdc.common.utils.Preconditions.checkNotNull;

public abstract class AbstractPipelineDefinitionParser implements PipelineDefinitionParser {
    // Parent node keys
    protected static final String SOURCE_KEY = "source";
    protected static final String READER_KEY = "reader";
    protected static final String SINK_KEY = "sink";
    protected static final String WRITER_KEY = "writer";
    protected static final String ROUTE_KEY = "route";
    protected static final String TRANSFORM_KEY = "transform";
    protected static final String PIPELINE_KEY = "pipeline";
    protected static final String MODEL_KEY = "model";

    // Source / sink keys
    protected static final String TYPE_KEY = "type";
    protected static final String NAME_KEY = "name";
    protected static final String INCLUDE_SCHEMA_EVOLUTION_TYPES = "include.schema.changes";
    protected static final String EXCLUDE_SCHEMA_EVOLUTION_TYPES = "exclude.schema.changes";
    protected static final String SPLIT_KEY = "split";
    protected static final String PAVING_KEY = "pavingData";

    // Route keys
    protected static final String ROUTE_SOURCE_TABLE_KEY = "source-table";
    protected static final String ROUTE_SINK_TABLE_KEY = "sink-table";
    protected static final String ROUTE_REPLACE_SYMBOL = "replace-symbol";
    protected static final String ROUTE_DESCRIPTION_KEY = "description";

    // Transform keys
    protected static final String TRANSFORM_SOURCE_TABLE_KEY = "source-table";
    protected static final String TRANSFORM_PROJECTION_KEY = "projection";
    protected static final String TRANSFORM_FILTER_KEY = "filter";
    protected static final String TRANSFORM_DESCRIPTION_KEY = "description";
    protected static final String TRANSFORM_CONVERTER_AFTER_TRANSFORM_KEY =
            "converter-after-transform";

    // UDF related keys
    protected static final String UDF_KEY = "user-defined-function";
    protected static final String UDF_FUNCTION_NAME_KEY = "name";
    protected static final String UDF_CLASSPATH_KEY = "classpath";

    // Model related keys
    protected static final String MODEL_NAME_KEY = "model-name";

    protected static final String MODEL_CLASS_NAME_KEY = "class-name";

    public static final String TRANSFORM_PRIMARY_KEY_KEY = "primary-keys";

    public static final String TRANSFORM_PARTITION_KEY_KEY = "partition-keys";

    public static final String TRANSFORM_TABLE_OPTION_KEY = "table-options";

    protected final ObjectMapper mapper = new ObjectMapper(new YAMLFactory());

    protected UdfDef toUdfDef(JsonNode udfNode) {
        String functionName =
                checkNotNull(
                                udfNode.get(UDF_FUNCTION_NAME_KEY),
                                "Missing required field \"%s\" in UDF configuration",
                                UDF_FUNCTION_NAME_KEY)
                        .asText();
        String classpath =
                checkNotNull(
                                udfNode.get(UDF_CLASSPATH_KEY),
                                "Missing required field \"%s\" in UDF configuration",
                                UDF_CLASSPATH_KEY)
                        .asText();

        return new UdfDef(functionName, classpath);
    }

    protected List<ModelDef> parseModels(JsonNode modelsNode) {
        List<ModelDef> modelDefs = new ArrayList<>();
        Preconditions.checkNotNull(modelsNode, "`model` in `pipeline` should not be empty.");
        if (modelsNode.isArray()) {
            for (JsonNode modelNode : modelsNode) {
                modelDefs.add(convertJsonNodeToModelDef(modelNode));
            }
        } else {
            modelDefs.add(convertJsonNodeToModelDef(modelsNode));
        }
        return modelDefs;
    }

    protected Configuration toPipelineConfig(JsonNode pipelineConfigNode) {
        if (pipelineConfigNode == null || pipelineConfigNode.isNull()) {
            return new Configuration();
        }
        Map<String, String> pipelineConfigMap =
                mapper.convertValue(
                        pipelineConfigNode, new TypeReference<Map<String, String>>() {});
        return Configuration.fromMap(pipelineConfigMap);
    }

    protected RouteDef toRouteDef(JsonNode routeNode) {
        String sourceTable =
                checkNotNull(
                                routeNode.get(ROUTE_SOURCE_TABLE_KEY),
                                "Missing required field \"%s\" in route configuration",
                                ROUTE_SOURCE_TABLE_KEY)
                        .asText();
        String sinkTable =
                checkNotNull(
                                routeNode.get(ROUTE_SINK_TABLE_KEY),
                                "Missing required field \"%s\" in route configuration",
                                ROUTE_SINK_TABLE_KEY)
                        .asText();
        String replaceSymbol =
                Optional.ofNullable(routeNode.get(ROUTE_REPLACE_SYMBOL))
                        .map(JsonNode::asText)
                        .orElse(null);
        String description =
                Optional.ofNullable(routeNode.get(ROUTE_DESCRIPTION_KEY))
                        .map(JsonNode::asText)
                        .orElse(null);
        return new RouteDef(sourceTable, sinkTable, replaceSymbol, description);
    }

    protected TransformDef toTransformDef(JsonNode transformNode) {
        String sourceTable =
                checkNotNull(
                                transformNode.get(TRANSFORM_SOURCE_TABLE_KEY),
                                "Missing required field \"%s\" in transform configuration",
                                TRANSFORM_SOURCE_TABLE_KEY)
                        .asText();
        String projection =
                Optional.ofNullable(transformNode.get(TRANSFORM_PROJECTION_KEY))
                        .map(JsonNode::asText)
                        .orElse(null);
        // When the star is in the first place, a backslash needs to be added for escape.
        if (!StringUtils.isNullOrWhitespaceOnly(projection) && projection.contains("\\*")) {
            projection = projection.replace("\\*", "*");
        }
        String filter =
                Optional.ofNullable(transformNode.get(TRANSFORM_FILTER_KEY))
                        .map(JsonNode::asText)
                        .orElse(null);
        String primaryKeys =
                Optional.ofNullable(transformNode.get(TRANSFORM_PRIMARY_KEY_KEY))
                        .map(JsonNode::asText)
                        .orElse(null);
        String partitionKeys =
                Optional.ofNullable(transformNode.get(TRANSFORM_PARTITION_KEY_KEY))
                        .map(JsonNode::asText)
                        .orElse(null);
        String tableOptions =
                Optional.ofNullable(transformNode.get(TRANSFORM_TABLE_OPTION_KEY))
                        .map(JsonNode::asText)
                        .orElse(null);
        String description =
                Optional.ofNullable(transformNode.get(TRANSFORM_DESCRIPTION_KEY))
                        .map(JsonNode::asText)
                        .orElse(null);
        String postTransformConverter =
                Optional.ofNullable(transformNode.get(TRANSFORM_CONVERTER_AFTER_TRANSFORM_KEY))
                        .map(JsonNode::asText)
                        .orElse(null);

        return new TransformDef(
                sourceTable,
                projection,
                filter,
                primaryKeys,
                partitionKeys,
                tableOptions,
                description,
                postTransformConverter);
    }

    private ModelDef convertJsonNodeToModelDef(JsonNode modelNode) {
        String name =
                checkNotNull(
                                modelNode.get(MODEL_NAME_KEY),
                                "Missing required field \"%s\" in `model`",
                                MODEL_NAME_KEY)
                        .asText();
        String model =
                checkNotNull(
                                modelNode.get(MODEL_CLASS_NAME_KEY),
                                "Missing required field \"%s\" in `model`",
                                MODEL_CLASS_NAME_KEY)
                        .asText();
        Map<String, String> properties = mapper.convertValue(modelNode, Map.class);
        return new ModelDef(name, model, properties);
    }
}
