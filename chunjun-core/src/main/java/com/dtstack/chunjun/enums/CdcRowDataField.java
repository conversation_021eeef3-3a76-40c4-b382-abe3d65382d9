package com.dtstack.chunjun.enums;

public enum CdcRowDataField {
    DATABASE("database"),
    SCHEMA("schema"),
    TABLE("table"),
    TYPE("type"),
    OP_TYPE("op_type"),
    OP_TIME("op_time"),
    OP_TIME2("opTime"),
    OP_TS("opTs"),
    TS("ts"),
    CURRENT_TS("current_ts"),
    LSN("lsn"),
    SCN("scn"),
    POS("pos"),
    PRIMARY_KEYS("primary_keys"),
    TOKENS("tokens"),
    BEFORE("before"),
    AFTER("after");

    private final String key;

    CdcRowDataField(String key) {
        this.key = key;
    }

    public static int getOrder(String key) {
        for (CdcRowDataField field : CdcRowDataField.values()) {
            if (key.equals(field.key)) {
                return field.ordinal();
            }
        }
        return Integer.MAX_VALUE;
    }
}
