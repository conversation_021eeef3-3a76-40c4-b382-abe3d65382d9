/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dtstack.chunjun.throwable;

import java.util.ArrayList;
import java.util.List;

/**
 * This Class defined several types of errors when writing record
 *
 * <p>Company: www.dtstack.com
 *
 * <AUTHOR>
 */
public class WriteErrorTypes {

    public static final String ERR_NULL_POINTER = "npe";

    public static final String ERR_PRIMARY_CONFLICT = "duplicate";

    public static final String ERR_FORMAT_TRANSFORM = "conversion";

    public static final String ERR_OTHERS = "other";

    public static final List<String> PRIMARY_CONFLICT_KEYWORDS = new ArrayList<String>(3);

    static {
        PRIMARY_CONFLICT_KEYWORDS.add("duplicate entry");
        PRIMARY_CONFLICT_KEYWORDS.add("unique constraint");
        PRIMARY_CONFLICT_KEYWORDS.add("primary key constraint");
    }
}
