/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.e2e.kudu;

import com.dtstack.chunjun.client.SubmitResult;
import com.dtstack.chunjun.connector.BaseTestContext;
import com.dtstack.chunjun.connector.datasource.DataSource;
import com.dtstack.chunjun.connector.datasource.DataSourceCenter;
import com.dtstack.chunjun.connector.entity.Constant;
import com.dtstack.chunjun.connector.utils.FileUtil;
import com.dtstack.chunjun.enums.ClusterMode;

import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runners.MethodSorters;

@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class KuduSyncTest extends BaseTestContext {

    private final ClusterMode clusterMode = ClusterMode.yarnSession;

    @BeforeClass
    public static void init() throws Exception {
        // 初始化建表
        DataSource kuduDataSource = DataSourceCenter.getDataSourceByName("kudu").get();
        try (KuduProxy kuduProxy = new KuduProxy((KuduDataSource) kuduDataSource)) {
            kuduProxy.createSyncTable("e2e_sync_source", "e2e_sync_sink");
        }
    }

    @AfterClass
    public static void clean() throws Exception {
        DataSource kuduDataSource = DataSourceCenter.getDataSourceByName("kudu").get();
        try (KuduProxy kuduProxy = new KuduProxy((KuduDataSource) kuduDataSource)) {
            kuduProxy.deleteTableIfExists("e2e_sync_source", "e2e_sync_sink");
        }
    }

    @Test
    public void test_1_stream_source() throws Exception {

        String syncPath = TMP_PATH + "/kudu/stream_kudu.json";
        FileUtil.write(
                syncPath, FileUtil.read(Constant.CHUNJUN_SCRIPT_JSON + "/kudu/stream_kudu.json"));

        // 提交任务
        SubmitResult result = submit(syncPath, clusterMode);
        // 等待任务结束
        waitFinished(result, clusterMode);
        // 检验

        // 删除临时文件
        FileUtil.delteFile(syncPath);
    }

    @Test
    public void test_2_source_stream() throws Exception {
        String syncPath = TMP_PATH + "/kudu/kudu_stream.json";
        FileUtil.write(
                syncPath, FileUtil.read(Constant.CHUNJUN_SCRIPT_JSON + "/kudu/kudu_stream.json"));

        // 提交任务
        SubmitResult result = submit(syncPath, clusterMode);
        // 等待任务结束
        waitFinished(result, clusterMode);

        // 检验

        // 删除临时文件
        FileUtil.delteFile(syncPath);
    }

    @Test
    public void test_3_source_sink() throws Exception {
        String syncPath = TMP_PATH + "/kudu/kudu_kudu.json";
        FileUtil.write(
                syncPath, FileUtil.read(Constant.CHUNJUN_SCRIPT_JSON + "/kudu/kudu_kudu.json"));

        // 提交任务
        SubmitResult result = submit(syncPath, clusterMode);
        // 等待任务结束
        waitFinished(result, clusterMode);

        // 检验

        // 删除临时文件
        FileUtil.delteFile(syncPath);
    }
}
