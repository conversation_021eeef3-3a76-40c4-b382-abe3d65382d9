/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.entity;

import com.dtstack.chunjun.connector.datasource.JdbcDataSource;
import com.dtstack.chunjun.connector.utils.SqlFormatUtil;
import com.dtstack.chunjun.common.util.RetryUtil;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;

public class JdbcProxy implements AutoCloseable {
    private final JdbcDataSource jdbcDataSource;
    private Connection connection;

    public JdbcProxy(JdbcDataSource jdbcDataSource) {
        this.jdbcDataSource = jdbcDataSource;
        connection =
                RetryUtil.executeWithRetry(
                        () -> {
                            try {
                                return DriverManager.getConnection(
                                        jdbcDataSource
                                                .getValue(jdbcDataSource.KEY_JDBCURL)
                                                .toString(),
                                        jdbcDataSource
                                                .getValue(jdbcDataSource.KEY_USERNAME)
                                                .toString(),
                                        jdbcDataSource
                                                .getValue(jdbcDataSource.KEY_PASSWORD)
                                                .toString());
                            } catch (SQLException e) {
                                throw new RuntimeException(e);
                            }
                        },
                        3,
                        2000,
                        false);
    }

    public Connection getConnection() {
        return connection;
    }

    public void executeSql(String sql) throws SQLException {
        Statement statement = connection.createStatement();
        statement.execute(sql);
        statement.close();
    }

    public void executeBatchSql(String sql) throws SQLException {
        List<String> strings = SqlFormatUtil.splitSqlText(sql);
        executeWithTransaction(strings);
    }

    private void executeWithTransaction(List<String> sqlList) throws SQLException {
        connection.setAutoCommit(false);
        Statement statement = connection.createStatement();
        for (int i = 0; i < sqlList.size(); i++) {
            String sql = sqlList.get(i);
            statement.execute(sql);
        }
        connection.commit();
        statement.close();
    }

    public ResultSet executeQuerySql(String sql) throws SQLException {
        PreparedStatement statement = connection.prepareStatement(sql);
        return statement.executeQuery(sql);
    }

    public ResultSet executeQuery(String sql) throws SQLException {
        Statement statement = connection.createStatement();
        return statement.executeQuery(sql);
    }

    @Override
    public void close() throws Exception {
        if (connection != null) {
            connection.close();
            connection = null;
        }
    }
}
