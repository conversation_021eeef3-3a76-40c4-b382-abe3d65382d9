package com.dtstack.chunjun.connector.e2e.hbase;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class HBaseData {
    protected static final String ROWKEY_NAME = "rowkey";
    protected static final String SPLIT = ":";
    protected static final String FAMILY_NAME = "info";
    protected static final String INT_NAME = "_int_";
    protected static final String STRING_NAME = "_string_";
    protected static final String DOUBLE_NAME = "_double_";
    protected static final String FLOAT_NAME = "_float_";
    protected static final String DECIMAL_NAME = "_decimal_";
    protected static final String BOOLEAN_NAME = "_boolean_";
    protected static final String LONG_NAME = "_long_";
    protected static final String SHORT_NAME = "_short_";
    protected static final String BYTES_NAME = "_bytes_";

    public static final List<Map<String, Optional<Object>>> DATA =
            ImmutableList.<Map<String, Optional<Object>>>builder()
                    .add(
                            ImmutableMap.<String, Optional<Object>>builder()
                                    .put(ROWKEY_NAME, Optional.of(2))
                                    .put(FAMILY_NAME + SPLIT + INT_NAME, Optional.of(2))
                                    .put(FAMILY_NAME + SPLIT + STRING_NAME, Optional.of("US"))
                                    .put(
                                            FAMILY_NAME + SPLIT + DOUBLE_NAME,
                                            Optional.of(5.01582947d))
                                    .put(FAMILY_NAME + SPLIT + FLOAT_NAME, Optional.of(3413.98f))
                                    .put(
                                            FAMILY_NAME + SPLIT + DECIMAL_NAME,
                                            Optional.of(new BigDecimal("230934.124199499")))
                                    .put(FAMILY_NAME + SPLIT + BOOLEAN_NAME, Optional.of(true))
                                    .put(
                                            FAMILY_NAME + SPLIT + LONG_NAME,
                                            Optional.of(1685412044000L)) // 2023-05-30 10:00:44
                                    .put(FAMILY_NAME + SPLIT + SHORT_NAME, Optional.of((short) 12))
                                    .put(
                                            FAMILY_NAME + SPLIT + BYTES_NAME,
                                            Optional.of(new byte[] {1, 2, 3, 4}))
                                    .build())
                    .build();
}
