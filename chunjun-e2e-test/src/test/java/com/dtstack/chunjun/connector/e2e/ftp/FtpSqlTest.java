/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.e2e.ftp;

import com.dtstack.chunjun.client.SubmitResult;
import com.dtstack.chunjun.connector.BaseTestContext;
import com.dtstack.chunjun.connector.datasource.DataSourceCenter;
import com.dtstack.chunjun.connector.entity.Constant;
import com.dtstack.chunjun.connector.utils.FileUtil;
import com.dtstack.chunjun.enums.ClusterMode;

import org.junit.BeforeClass;
import org.junit.Test;

public class FtpSqlTest extends BaseTestContext {
    ClusterMode clusterMode = ClusterMode.yarnPerJob;

    static FtpDataSource sftp;

    @BeforeClass
    public static void init() {
        sftp = (FtpDataSource) DataSourceCenter.getDataSourceByName("sftp").get();
    }

    @Test
    public void ftp_stream_csv() throws Exception {

        String syncPath = TMP_PATH + "/ftp/ftp_stream.sql";
        FileUtil.write(
                syncPath,
                String.format(
                        FileUtil.read(Constant.CHUNJUN_SCRIPT_SQL + "/ftp/ftp_stream.sql"),
                        sftp.getHost(),
                        sftp.getUsername(),
                        sftp.getPassword()));

        SubmitResult submit = submit(syncPath, clusterMode);
        waitFinished(submit, clusterMode);

        // 删除临时文件
        FileUtil.delteFile(syncPath);
    }

    @Test
    public void stream_ftp_csv() throws Exception {

        String syncPath = TMP_PATH + "/ftp/stream_ftp.sql";
        FileUtil.write(
                syncPath,
                String.format(
                        FileUtil.read(Constant.CHUNJUN_SCRIPT_SQL + "/ftp/stream_ftp.sql"),
                        sftp.getHost(),
                        sftp.getUsername(),
                        sftp.getPassword()));

        SubmitResult submit = submit(syncPath, clusterMode);
        waitFinished(submit, clusterMode);

        // 删除临时文件
        FileUtil.delteFile(syncPath);
    }
}
