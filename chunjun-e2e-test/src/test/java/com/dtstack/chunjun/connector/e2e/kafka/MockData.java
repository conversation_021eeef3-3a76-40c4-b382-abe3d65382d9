/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.e2e.kafka;

import com.google.common.collect.Lists;

import java.util.List;

public class MockData {
    public static List<String> generateOggData() {
        String t1 =
                "{\n"
                        + "    \"table\":\"dujie.test\",\n"
                        + "    \"op_type\":\"I\",\n"
                        + "    \"op_ts\":\"2015-11-05 18:45:36.000000\",\n"
                        + "    \"current_ts\":\"2016-10-05T10:34:47.956000\",\n"
                        + "    \"pos\":\"00000000000000002928\",\n"
                        + "    \"after.id\":\"1\",\n"
                        + "    \"after.name\":\"zhangsan\",\n"
                        + "    \"after.age\":12\n"
                        + "}";
        String t2 =
                "{\n"
                        + "    \"table\":\"dujie.test\",\n"
                        + "    \"op_type\":\"U\",\n"
                        + "    \"op_ts\":\"2015-11-05 18:45:39.000000\",\n"
                        + "    \"current_ts\":\"2016-10-05T10:34:48.192000\",\n"
                        + "    \"pos\":\"00000000000000004300\",\n"
                        + "    \"before.id\":\"1\",\n"
                        + "    \"before.name\":\"zhangsan\",\n"
                        + "    \"before.age\":12,\n"
                        + "    \"after.id\":\"1\",\n"
                        + "    \"after.name\":\"lisia\",\n"
                        + "    \"after.age\":18\n"
                        + "}";
        String t3 =
                "{\n"
                        + "    \"table\":\"dujie.test\",\n"
                        + "    \"op_type\":\"D\",\n"
                        + "    \"op_ts\":\"2015-11-05 18:45:39.000000\",\n"
                        + "    \"current_ts\":\"2016-10-05T10:34:48.193000\",\n"
                        + "    \"pos\":\"00000000000000005272\",\n"
                        + "    \"before.id\":\"1\",\n"
                        + "    \"before.name\":\"lisia\",\n"
                        + "    \"before.age\":\"18\"\n"
                        + "}";

        return Lists.newArrayList(t1, t2, t3);
    }

    public static List<String> generateJsonData() {
        String t1 =
                "{\"id\":1,\"name\":\"lb james阿道夫\",\"money\":292333,\"dateone\":\"2020-07-30 10:08:22\",\"age\":\"12\",\"datethree\":\"2022-07-30 10:08:22.123\",\"datesix\":\"2020-07-30 10:08:22.123456\",\"datenigth\":\"2020-07-30 10:08:22.123456789\",\"dtdate\":\"2020-07-30\",\"dttime\":\"10:08:22\"}";
        String t2 =
                "{\"id\":2,\"name\":\"zhansan\",\"money\":1293.1,\"dateone\":\"2023-07-12 10:08:22\",\"age\":\"33\",\"datethree\":\"2024-07-30 10:08:22.123\",\"datesix\":\"2020-07-30 10:08:22.123456\",\"datenigth\":\"2020-07-30 10:08:22.123456789\",\"dtdate\":\"2020-07-30\",\"dttime\":\"10:08:22\"}";
        String t3 =
                "{\"id\":3,\"name\":\"lisi\",\"money\":123.2,\"dateone\":\"2022-08-30 11:08:22\",\"age\":\"65\",\"datethree\":\"2021-07-30 10:08:22.123\",\"datesix\":\"2020-07-30 10:08:22.123456\",\"datenigth\":\"2020-07-30 10:08:22.123456789\",\"dtdate\":\"2020-07-30\",\"dttime\":\"10:08:22\"}";

        return Lists.newArrayList(t1, t2, t3);
    }

    public static List<String> generateChunjunCdcData() {
        String t1 =
                "{\"schema\":\"dujie\",\"database\":null,\"after_name\":\"scooter\",\"opTime\":\"1663224098000\",\"lsn\":\"binlog.000030/000000000000080937\",\"after_weight\":5.25,\"after_id\":1,\"after_description\":\"Big 2-wheel scooter\",\"type\":\"INSERT\",\"table\":\"products\",\"ts\":6976067492900900864}";
        String t2 =
                "{\"schema\":\"dujie\",\"database\":null,\"after_name\":\"scooter\",\"opTime\":\"1663224098000\",\"lsn\":\"binlog.000030/000000000000081267\",\"after_weight\":10.50,\"after_id\":2,\"after_description\":\"Big 4-wheel scooter\",\"type\":\"INSERT\",\"table\":\"products\",\"ts\":6976067492947038208}";
        String t3 =
                "{\"schema\":\"dujie\",\"after_name\":\"scooter\",\"lsn\":\"binlog.000030/000000000000081606\",\"after_weight\":2.25,\"before_description\":\"Big 2-wheel scooter\",\"type\":\"UPDATE\",\"before_name\":\"scooter\",\"database\":null,\"opTime\":\"1663224098000\",\"after_id\":1,\"after_description\":\"Big 2-wheel scooter\",\"table\":\"products\",\"ts\":6976067492955426816,\"before_id\":1,\"before_weight\":5.25}";

        String t4 =
                "{\"message\":{\"schema\":\"dujie\",\"database\":null,\"opTime\":\"1663224170000\",\"before\":{},\"lsn\":\"binlog.000030/000000000000083225\",\"after\":{\"id\":\"3\",\"name\":\"scooter\",\"description\":\"Big 2-wheel scooter\",\"weight\":\"5.25\"},\"type\":\"INSERT\",\"table\":\"products\",\"ts\":6976067792294514688}}";
        String t5 =
                "{\"message\":{\"schema\":\"dujie\",\"database\":null,\"opTime\":\"1663224170000\",\"before\":{},\"lsn\":\"binlog.000030/000000000000083555\",\"after\":{\"id\":\"4\",\"name\":\"scooter\",\"description\":\"Big 4-wheel scooter\",\"weight\":\"10.50\"},\"type\":\"INSERT\",\"table\":\"products\",\"ts\":6976067792311291904}}";
        String t6 =
                "{\"schema\":\"dujie\",\"database\":null,\"opTime\":\"1663224279000\",\"lsn\":\"binlog.000030/000000000000090089\",\"name\":\"scooter\",\"description\":\"Big 2-wheel scooter\",\"weight\":5.25,\"id\":4,\"type\":\"INSERT\",\"table\":\"products\",\"ts\":6976068248655761408}";

        return Lists.newArrayList(t1, t2, t3, t4, t5, t6);
    }
}
