/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.e2e.oracle;

import com.dtstack.chunjun.client.SubmitResult;
import com.dtstack.chunjun.connector.utils.FileUtil;
import com.dtstack.chunjun.enums.ClusterMode;

import org.junit.Test;

public class OracleSqlTest extends OracleBaseTest {

    @Test
    public void test() throws Exception {
        FileUtil.write(
                tempSqlPath,
                String.format(
                        FileUtil.read(sqlPath),
                        jdbcDataSource.getValue(jdbcDataSource.KEY_JDBCURL),
                        sourceTable,
                        jdbcDataSource.getValue(jdbcDataSource.KEY_USERNAME),
                        jdbcDataSource.getValue(jdbcDataSource.KEY_PASSWORD),
                        jdbcDataSource.getValue(jdbcDataSource.KEY_JDBCURL),
                        sinkTable,
                        jdbcDataSource.getValue(jdbcDataSource.KEY_USERNAME),
                        jdbcDataSource.getValue(jdbcDataSource.KEY_PASSWORD)));

        // 提交任务
        SubmitResult result = submit(tempSqlPath, ClusterMode.local);
        // 等待任务结束
        waitFinished(result, ClusterMode.local);

        // 检验
        check();

        // 删除临时文件
        FileUtil.delteFile(tempSqlPath);
    }
}
