package com.dtstack.chunjun.connector.e2e.es7;

import com.dtstack.chunjun.constants.ConstantValue;
import com.dtstack.chunjun.common.util.GsonUtil;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.rest.RestStatus;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> linchen
 * @Date: 2023/5/30 19:20
 */
public class ElasticsearchProxy implements AutoCloseable {
    private final ElasticsearchDataSource dataSource;
    private RestHighLevelClient rhlClient;

    public ElasticsearchProxy(ElasticsearchDataSource dataSource) {
        this.dataSource = dataSource;
        getClient();
    }

    public void indexData(String index, List<String> dataList) throws Exception {
        if(rhlClient == null) {
            getClient();
        }
        if(dataList == null || dataList.isEmpty()) {
            return ;
        }

        IndexRequest request = new IndexRequest(index);
        BulkRequest bulkRequest = new BulkRequest();
        dataList.forEach(data -> request.source(data, XContentType.JSON));
        bulkRequest.add(request);
        BulkResponse response = rhlClient.bulk(bulkRequest, RequestOptions.DEFAULT);
        if(response.hasFailures()) {
            //process
        }
    }

    public RestHighLevelClient getClient() {
        List<String> hostList = Arrays.asList(StringUtils.split(dataSource.getHosts(), ','));
        List<HttpHost> httpAddresses = getHosts(hostList,false);
        RestClientBuilder restClientBuilder =
                RestClient.builder(httpAddresses.toArray(new HttpHost[httpAddresses.size()]));
        return new RestHighLevelClient(restClientBuilder);
    }

    private List<HttpHost> getHosts(List<String> hosts, boolean ssl) {
        if (ssl) {
            hosts =
                    hosts.stream()
                            .map(
                                    i -> {
                                        if (!i.startsWith("https://")) {
                                            return "https://" + i;
                                        }
                                        return i;
                                    })
                            .collect(Collectors.toList());
        }
        return hosts.stream()
                .map(host -> HttpHost.create(host))
                .collect(Collectors.toList());
    }

    @Override
    public void close() throws Exception {

    }
}
