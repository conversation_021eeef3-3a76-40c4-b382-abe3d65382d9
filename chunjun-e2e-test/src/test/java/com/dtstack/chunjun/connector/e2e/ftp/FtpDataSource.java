/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.e2e.ftp;

import com.dtstack.chunjun.connector.datasource.DataSource;

public class FtpDataSource extends DataSource {
    public FtpDataSource(String type, String host, int port, String username, String password) {
        super(type);
        this.properties.put("host", host);
        this.properties.put("port", port);
        this.properties.put("username", username);
        this.properties.put("password", password);
    }

    public String getHost() {
        return getValue("host").toString();
    }

    public int getPort() {
        return Integer.parseInt(getValue("port").toString());
    }

    public String getUsername() {
        return getValue("username").toString();
    }

    public String getPassword() {
        return getValue("password").toString();
    }
}
