/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.entity;

import java.io.File;

public class Constant {
    public static final String CHUNJUN_HOME =
            new File(System.getProperty("user.dir")).getParentFile().getAbsolutePath();

    public static final String CHUNJUN_DIST = CHUNJUN_HOME + "/chunjun-dist";

    public static final String CHUNJUN_DDL =
            Constant.class.getClassLoader().getResource("ddl").getPath();

    public static final String CHUNJUN_SCRIPT_JSON =
            Constant.class.getClassLoader().getResource("script/json").getPath();

    public static final String CHUNJUN_SCRIPT_SQL =
            Constant.class.getClassLoader().getResource("script/sql").getPath();
}
