package com.dtstack.chunjun.connector.e2e.hdfs;

import com.dtstack.chunjun.client.SubmitResult;
import com.dtstack.chunjun.connector.BaseTestContext;
import com.dtstack.chunjun.connector.datasource.DataSourceCenter;
import com.dtstack.chunjun.connector.datasource.JdbcDataSource;
import com.dtstack.chunjun.connector.entity.Constant;
import com.dtstack.chunjun.connector.entity.JdbcProxy;
import com.dtstack.chunjun.connector.utils.FileUtil;
import com.dtstack.chunjun.enums.ClusterMode;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import java.sql.ResultSet;

public class HdfsSyncTest extends BaseTestContext {
    protected static final String ORC_SOURCE_TABLE_NAME = "orc_source_table_" + ID;
    protected static final String ORC_SINK_TABLE_NAME = "orc_sink_table_" + ID;
    protected static final String PARQUET_SOURCE_TABLE_NAME = "parquet_source_table_" + ID;
    protected static final String PARQUET_SINK_TABLE_NAME = "parquet_sink_table_" + ID;
    protected static final String TEXT_SOURCE_TABLE_NAME = "text_source_table_" + ID;
    protected static final String TEXT_SINK_TABLE_NAME = "text_sink_table_" + ID;

    @BeforeClass
    public static void init() throws Exception {
        JdbcDataSource hive = (JdbcDataSource) DataSourceCenter.getDataSourceByName("hive").get();
        try (JdbcProxy jdbcProxy = new JdbcProxy(hive)) {
            String read = FileUtil.read(Constant.CHUNJUN_DDL + "/hdfs/hive_table_init.sql");
            String[] orcSql = read.split(";");
            jdbcProxy.executeSql(
                    String.format(
                            orcSql[0],
                            hive.getValue(hive.KEY_SCHEMA),
                            ORC_SOURCE_TABLE_NAME,
                            HdfsE2EConstants.ORC_MODE));
            jdbcProxy.executeSql(
                    String.format(
                            orcSql[1],
                            hive.getValue(hive.KEY_SCHEMA),
                            ORC_SINK_TABLE_NAME,
                            HdfsE2EConstants.ORC_MODE));
            jdbcProxy.executeSql(
                    String.format(
                            orcSql[2],
                            hive.getValue(hive.KEY_SCHEMA),
                            ORC_SOURCE_TABLE_NAME,
                            HdfsE2EConstants.DATE));

            read = FileUtil.read(Constant.CHUNJUN_DDL + "/hdfs/hive_table_init.sql");
            String[] parquetSql = read.split(";");
            jdbcProxy.executeSql(
                    String.format(
                            parquetSql[0],
                            hive.getValue(hive.KEY_SCHEMA),
                            PARQUET_SOURCE_TABLE_NAME,
                            HdfsE2EConstants.PARQUET_MODE));
            jdbcProxy.executeSql(
                    String.format(
                            parquetSql[1],
                            hive.getValue(hive.KEY_SCHEMA),
                            PARQUET_SINK_TABLE_NAME,
                            HdfsE2EConstants.PARQUET_MODE));
            jdbcProxy.executeSql(
                    String.format(
                            parquetSql[2],
                            hive.getValue(hive.KEY_SCHEMA),
                            PARQUET_SOURCE_TABLE_NAME,
                            HdfsE2EConstants.DATE));

            read = FileUtil.read(Constant.CHUNJUN_DDL + "/hdfs/hive_table_init.sql");
            String[] txtSql = read.split(";");
            jdbcProxy.executeSql(
                    String.format(
                            txtSql[0],
                            hive.getValue(hive.KEY_SCHEMA),
                            TEXT_SOURCE_TABLE_NAME,
                            HdfsE2EConstants.TEXT_MODE));
            jdbcProxy.executeSql(
                    String.format(
                            txtSql[1],
                            hive.getValue(hive.KEY_SCHEMA),
                            TEXT_SINK_TABLE_NAME,
                            HdfsE2EConstants.TEXT_MODE));
            jdbcProxy.executeSql(
                    String.format(
                            txtSql[2],
                            hive.getValue(hive.KEY_SCHEMA),
                            TEXT_SOURCE_TABLE_NAME,
                            HdfsE2EConstants.DATE));
        }
    }

    @Test
    public void testSessionOrc() throws Exception {
        String scriptPath = "/hdfs/reader_writer_orc.json";
        String syncPath = TMP_PATH + scriptPath;
        JdbcDataSource hive = (JdbcDataSource) DataSourceCenter.getDataSourceByName("hive").get();
        FileUtil.write(
                syncPath,
                String.format(
                        FileUtil.read(Constant.CHUNJUN_SCRIPT_JSON + scriptPath),
                        hive.getValue(hive.KEY_SCHEMA),
                        ORC_SOURCE_TABLE_NAME,
                        HdfsE2EConstants.DATE,
                        hive.getValue(hive.KEY_JDBCURL),
                        ORC_SOURCE_TABLE_NAME,
                        ORC_SOURCE_TABLE_NAME,
                        hive.getValue(hive.KEY_SCHEMA),
                        HdfsE2EConstants.DATE,
                        ORC_SINK_TABLE_NAME,
                        hive.getValue(hive.KEY_JDBCURL),
                        ORC_SINK_TABLE_NAME,
                        ORC_SINK_TABLE_NAME));
        SubmitResult result = submit(syncPath, ClusterMode.yarnSession);
        waitFinished(result, ClusterMode.yarnSession);
        try (JdbcProxy hiveClient = new JdbcProxy(hive)) {
            ResultSet sourceResultSet =
                    hiveClient.executeQuerySql(
                            String.format(
                                    "select * from %s.%s where pt = '%s'",
                                    hive.getValue(hive.KEY_SCHEMA),
                                    ORC_SOURCE_TABLE_NAME,
                                    HdfsE2EConstants.DATE));
            // To avoid metadata not being refreshed, refresh the metadata first
            hiveClient.executeSql(
                    String.format(
                            HdfsE2EConstants.REFRESH_TABLE,
                            hive.getValue(hive.KEY_SCHEMA),
                            ORC_SINK_TABLE_NAME));
            ResultSet sinkResultSet =
                    hiveClient.executeQuerySql(
                            String.format(
                                    "select * from %s.%s where pt = '%s'",
                                    hive.getValue(hive.KEY_SCHEMA),
                                    ORC_SINK_TABLE_NAME,
                                    HdfsE2EConstants.DATE));
            while (sourceResultSet.next() && sinkResultSet.next()) {
                Assert.assertEquals(sourceResultSet.getInt(1), sinkResultSet.getInt(1));
                Assert.assertEquals(sourceResultSet.getString(2), sinkResultSet.getString(2));
            }
            Assert.assertEquals(sourceResultSet.next(), sinkResultSet.next());
            sourceResultSet.close();
            sinkResultSet.close();

            FileUtil.delteFile(syncPath);
        }
    }

    @Test
    public void testSessionParquet() throws Exception {
        String scriptPath = "/hdfs/reader_writer_parquet.json";
        String syncPath = TMP_PATH + scriptPath;
        JdbcDataSource hive = (JdbcDataSource) DataSourceCenter.getDataSourceByName("hive").get();
        FileUtil.write(
                syncPath,
                String.format(
                        FileUtil.read(Constant.CHUNJUN_SCRIPT_JSON + scriptPath),
                        hive.getValue(hive.KEY_SCHEMA),
                        PARQUET_SOURCE_TABLE_NAME,
                        HdfsE2EConstants.DATE,
                        hive.getValue(hive.KEY_JDBCURL),
                        PARQUET_SOURCE_TABLE_NAME,
                        PARQUET_SOURCE_TABLE_NAME,
                        hive.getValue(hive.KEY_SCHEMA),
                        HdfsE2EConstants.DATE,
                        PARQUET_SINK_TABLE_NAME,
                        hive.getValue(hive.KEY_JDBCURL),
                        PARQUET_SINK_TABLE_NAME,
                        PARQUET_SINK_TABLE_NAME));
        SubmitResult result = submit(syncPath, ClusterMode.yarnSession);
        waitFinished(result, ClusterMode.yarnSession);
        try (JdbcProxy hiveClient = new JdbcProxy(hive)) {
            ResultSet sourceResultSet =
                    hiveClient.executeQuerySql(
                            String.format(
                                    "select * from %s.%s where pt = '%s'",
                                    hive.getValue(hive.KEY_SCHEMA),
                                    PARQUET_SOURCE_TABLE_NAME,
                                    HdfsE2EConstants.DATE));
            // To avoid metadata not being refreshed, refresh the metadata first
            hiveClient.executeSql(
                    String.format(
                            HdfsE2EConstants.REFRESH_TABLE,
                            hive.getValue(hive.KEY_SCHEMA),
                            PARQUET_SINK_TABLE_NAME));
            ResultSet sinkResultSet =
                    hiveClient.executeQuerySql(
                            String.format(
                                    "select * from %s.%s where pt = '%s'",
                                    hive.getValue(hive.KEY_SCHEMA),
                                    PARQUET_SINK_TABLE_NAME,
                                    HdfsE2EConstants.DATE));
            while (sourceResultSet.next() && sinkResultSet.next()) {
                Assert.assertEquals(sourceResultSet.getInt(1), sinkResultSet.getInt(1));
                Assert.assertEquals(sourceResultSet.getString(2), sinkResultSet.getString(2));
            }
            Assert.assertEquals(sourceResultSet.next(), sinkResultSet.next());
            sourceResultSet.close();
            sinkResultSet.close();

            FileUtil.delteFile(syncPath);
        }
    }

    @Test
    public void testSessionText() throws Exception {
        String scriptPath = "/hdfs/reader_writer_txt.json";
        String syncPath = TMP_PATH + scriptPath;
        JdbcDataSource hive = (JdbcDataSource) DataSourceCenter.getDataSourceByName("hive").get();
        FileUtil.write(
                syncPath,
                String.format(
                        FileUtil.read(Constant.CHUNJUN_SCRIPT_JSON + scriptPath),
                        hive.getValue(hive.KEY_SCHEMA),
                        TEXT_SOURCE_TABLE_NAME,
                        HdfsE2EConstants.DATE,
                        hive.getValue(hive.KEY_JDBCURL),
                        TEXT_SOURCE_TABLE_NAME,
                        TEXT_SOURCE_TABLE_NAME,
                        hive.getValue(hive.KEY_SCHEMA),
                        HdfsE2EConstants.DATE,
                        TEXT_SINK_TABLE_NAME,
                        hive.getValue(hive.KEY_JDBCURL),
                        TEXT_SINK_TABLE_NAME,
                        TEXT_SINK_TABLE_NAME));
        SubmitResult result = submit(syncPath, ClusterMode.yarnSession);
        waitFinished(result, ClusterMode.yarnSession);
        try (JdbcProxy hiveClient = new JdbcProxy(hive)) {
            ResultSet sourceResultSet =
                    hiveClient.executeQuerySql(
                            String.format(
                                    "select * from %s.%s where pt = '%s'",
                                    hive.getValue(hive.KEY_SCHEMA),
                                    TEXT_SOURCE_TABLE_NAME,
                                    HdfsE2EConstants.DATE));
            // To avoid metadata not being refreshed, refresh the metadata first
            hiveClient.executeSql(
                    String.format(
                            HdfsE2EConstants.REFRESH_TABLE,
                            hive.getValue(hive.KEY_SCHEMA),
                            TEXT_SINK_TABLE_NAME));
            ResultSet sinkResultSet =
                    hiveClient.executeQuerySql(
                            String.format(
                                    "select * from %s.%s where pt = '%s'",
                                    hive.getValue(hive.KEY_SCHEMA),
                                    TEXT_SINK_TABLE_NAME,
                                    HdfsE2EConstants.DATE));
            while (sourceResultSet.next() && sinkResultSet.next()) {
                Assert.assertEquals(sourceResultSet.getInt(1), sinkResultSet.getInt(1));
                Assert.assertEquals(sourceResultSet.getString(2), sinkResultSet.getString(2));
            }
            Assert.assertEquals(sourceResultSet.next(), sinkResultSet.next());
            sourceResultSet.close();
            sinkResultSet.close();

            FileUtil.delteFile(syncPath);
        }
    }

    @AfterClass
    public static void clean() throws Exception {
        JdbcDataSource hive = (JdbcDataSource) DataSourceCenter.getDataSourceByName("hive").get();
        try (JdbcProxy jdbcProxy = new JdbcProxy(hive)) {
            jdbcProxy.executeSql(
                    String.format(
                            "drop table %s.%s",
                            hive.getValue(hive.KEY_SCHEMA), ORC_SOURCE_TABLE_NAME));
            jdbcProxy.executeSql(
                    String.format(
                            "drop table %s.%s",
                            hive.getValue(hive.KEY_SCHEMA), ORC_SINK_TABLE_NAME));
            jdbcProxy.executeSql(
                    String.format(
                            "drop table %s.%s",
                            hive.getValue(hive.KEY_SCHEMA), PARQUET_SOURCE_TABLE_NAME));
            jdbcProxy.executeSql(
                    String.format(
                            "drop table %s.%s",
                            hive.getValue(hive.KEY_SCHEMA), PARQUET_SINK_TABLE_NAME));
            jdbcProxy.executeSql(
                    String.format(
                            "drop table %s.%s",
                            hive.getValue(hive.KEY_SCHEMA), TEXT_SOURCE_TABLE_NAME));
            jdbcProxy.executeSql(
                    String.format(
                            "drop table %s.%s",
                            hive.getValue(hive.KEY_SCHEMA), TEXT_SINK_TABLE_NAME));
        }
    }
}
