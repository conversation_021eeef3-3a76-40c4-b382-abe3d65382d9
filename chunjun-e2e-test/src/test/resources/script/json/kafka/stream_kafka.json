{"job": {"content": [{"reader": {"parameter": {"column": [{"name": "id", "type": "string"}, {"name": "name", "type": "string"}, {"name": "content", "type": "string"}], "sliceRecordCount": ["30"], "permitsPerSecond": 1}, "table": {"tableName": "sourceTable"}, "name": "streamreader"}, "writer": {"name": "ka<PERSON><PERSON><PERSON>", "parameter": {"tableFields": ["id", "name", "content"], "topic": "%s", "producerSettings": {"auto.commit.enable": "false", "bootstrap.servers": "%s"}}}}], "setting": {"restore": {"isRestore": true, "isStream": true}, "errorLimit": {"record": 0}, "speed": {"readerChannel": 1, "writerChannel": 1}}}}