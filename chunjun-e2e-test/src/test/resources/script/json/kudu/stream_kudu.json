{
  "job": {
    "content": [
      {
        "reader": {
          "parameter": {
            "sliceRecordCount": [
              "100"
            ],
            "column": [
              {
                "name": "id",
                "type": "id"
              },
              {
                "name": "int8_data",
                "type": "tinyint"
              },
              {
                "name": "int16_data",
                "type": "smallint"
              },
              {
                "name": "int32_data",
                "type": "int"
              },
              {
                "name": "int64_data",
                "type": "bigint"
              },
              {
                "name": "binary_data",
                "type": "binary"
              },
              {
                "name": "string_data",
                "type": "string"
              },
              {
                "name": "boolean_data",
                "type": "boolean"
              },
              {
                "name": "float_data",
                "type": "float"
              },
              {
                "name": "double_data",
                "type": "double"
              },
              {
                "name": "unixtime_micros_data",
                "type": "bigint"
              },
              {
                "name": "decimal_data",
                "type": "decimal"
//              },
//              {
//                "name": "varchar_data",
//                "type": "varchar"
//              },
//              {
//                "name": "date_data",
//                "type": "date"
              }
            ]
          },
          "name": "streamreader"
        },
        "writer": {
          "parameter": {
            "flushMode": "manual_flush",
            "masterAddresses": "**************:7051",
            "workerCount": 2,
            "bossCount": 1,
            "column": [
              {
                "name": "id",
                "type": "bigint"
              },
              {
                "name": "int8_data",
                "type": "tinyint"
              },
              {
                "name": "int16_data",
                "type": "smallint"
              },
              {
                "name": "int32_data",
                "type": "int"
              },
              {
                "name": "int64_data",
                "type": "bigint"
              },
              {
                "name": "binary_data",
                "type": "binary"
              },
              {
                "name": "string_data",
                "type": "string"
              },
              {
                "name": "bool_data",
                "type": "bool"
              },
              {
                "name": "float_data",
                "type": "float"
              },
              {
                "name": "double_data",
                "type": "double"
              },
              {
                "name": "unixtime_micros_data",
                "type": "bigint"
              },
              {
                "name": "decimal_data",
                "type": "decimal"
//              },
//              {
//                "name": "varchar_data",
//                "type": "varchar"
//              },
//              {
//                "name": "date_data",
//                "type": "date"
              }
            ],
            "batchInterval": 10000,
            "writeMode": "insert",
            "table": "e2e_sync_source"
          },
          "name": "kuduwriter"
        }
      }
    ],
    "setting": {
      "restore": {
        "isRestore": false
      },
      "errorLimit": {
        "record": 0
      },
      "speed": {
        "channel": 1
      }
    }
  }
}
