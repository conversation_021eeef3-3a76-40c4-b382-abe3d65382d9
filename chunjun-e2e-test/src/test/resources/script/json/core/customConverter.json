{"job": {"content": [{"reader": {"parameter": {"customSql": "", "where": "1=1 limit 10", "column": [{"customConverterType": "INT", "name": "r_reason_sk", "isPart": false, "type": "INT"}, {"customConverterClass": "com.dtstack.chunjun.connector.e2e.core.ICustomConverterTest", "customConverterType": "<PERSON><PERSON><PERSON>", "name": "r_reason_desc", "isPart": false, "type": "VARCHAR"}], "connection": [{"schema": "chun<PERSON>", "username": "%s", "password": "%s", "jdbcUrl": ["%s"], "table": ["%s"]}], "username": "root"}, "name": "mysqlreader"}, "writer": {"parameter": {}, "name": "streamwriter"}}], "setting": {"restore": {"maxRowNumForCheckpoint": 0, "isRestore": false, "restoreColumnName": "", "restoreColumnIndex": 0}, "errorLimit": {"record": 0}, "speed": {"readerChannel": 1, "writerChannel": 1, "bytes": 0, "channel": 1}}}}