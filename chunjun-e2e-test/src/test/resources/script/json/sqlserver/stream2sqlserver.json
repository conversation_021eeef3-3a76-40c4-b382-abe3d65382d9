{"job": {"content": [{"reader": {"parameter": {"sliceRecordCount": ["100"], "column": [{"customConverterType": "int", "name": "id", "isPart": false, "type": "int", "key": "id"}, {"customConverterType": "smallint", "name": "id2", "isPart": false, "type": "smallint", "key": "id2"}, {"customConverterType": "bigint", "name": "id3", "isPart": false, "type": "bigint", "key": "id3"}, {"customConverterType": "BIGINT", "name": "id4", "isPart": false, "type": "BIGINT", "key": "id4"}, {"customConverterType": "float", "name": "id5", "isPart": false, "type": "float", "key": "id5"}, {"customConverterType": "decimal", "name": "id6", "isPart": false, "type": "decimal", "key": "id6"}, {"customConverterType": "decimal", "name": "id7", "isPart": false, "type": "decimal", "key": "id7"}, {"customConverterType": "datetime", "name": "id8", "isPart": false, "type": "datetime", "key": "id8"}, {"customConverterType": "datetime", "name": "id9", "isPart": false, "type": "datetime", "key": "id9"}, {"customConverterType": "timestamp", "name": "id10", "isPart": false, "type": "timestamp", "key": "id10"}, {"customConverterType": "TIMESTAMP", "name": "id11", "isPart": false, "type": "TIMESTAMP", "key": "id11"}]}, "name": "streamreader"}, "writer": {"parameter": {"postSql": [], "session": [], "column": [{"customConverterType": "int", "name": "id", "isPart": false, "type": "int", "key": "id"}, {"customConverterType": "smallint", "name": "id2", "isPart": false, "type": "smallint", "key": "id2"}, {"customConverterType": "bigint", "name": "id3", "isPart": false, "type": "bigint", "key": "id3"}, {"customConverterType": "real", "name": "id4", "isPart": false, "type": "real", "key": "id4"}, {"customConverterType": "float", "name": "id5", "isPart": false, "type": "float", "key": "id5"}, {"customConverterType": "decimal", "name": "id6", "isPart": false, "type": "decimal", "key": "id6"}, {"customConverterType": "numeric", "name": "id7", "isPart": false, "type": "numeric", "key": "id7"}, {"customConverterType": "datetime", "name": "id8", "isPart": false, "type": "datetime", "key": "id8"}, {"customConverterType": "datetime2", "name": "id9", "isPart": false, "type": "datetime2", "key": "id9"}, {"customConverterType": "datetimeoffset", "name": "id10", "isPart": false, "type": "datetimeoffset", "key": "id10"}], "connection": [{"password": "%s", "jdbcUrl": "%s", "table": ["%s"], "username": "%s"}], "writeMode": "insert", "sourceIds": [1728], "username": "sa", "preSql": []}, "name": "sqlserverwriter"}}], "setting": {"restore": {"maxRowNumForCheckpoint": 0, "isRestore": false, "restoreColumnName": "", "restoreColumnIndex": 0}, "errorLimit": {"record": 0}, "speed": {"readerChannel": 1, "writerChannel": 1, "bytes": 0, "channel": 1}}}}