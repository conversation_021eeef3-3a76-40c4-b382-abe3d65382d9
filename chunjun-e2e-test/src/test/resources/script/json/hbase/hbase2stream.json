{"job": {"content": [{"reader": {"name": "hbasereader", "parameter": {"table": "%s", "hbaseConfig": {"hbase.zookeeper.quorum": "%s", "hbase.zookeeper.property.clientPort": "%s", "hbase.rootdir": "%s", "zookeeper.znode.parent": "%s"}, "encoding": "utf-8", "column": [{"name": "rowkey", "type": "int"}, {"name": "info:_int_", "type": "int"}, {"name": "info:_string_", "type": "string"}, {"name": "info:_double_", "type": "double"}, {"name": "info:_float_", "type": "float"}, {"name": "info:_decimal_", "type": "decimal"}, {"name": "info:_boolean_", "type": "boolean"}, {"name": "info:_long_", "type": "long"}, {"name": "info:_short_", "type": "short"}, {"name": "info:_bytes_", "type": "bytes"}], "startRowkey": "", "endRowkey": "", "isBinaryRowkey": true}}, "writer": {"parameter": {"print": true}, "name": "streamwriter"}}], "setting": {"speed": {"channel": 1, "bytes": 0}, "errorLimit": {"record": 0}, "restore": {"maxRowNumForCheckpoint": 0, "isRestore": false, "isStream": false, "restoreColumnName": "", "restoreColumnIndex": 0}, "log": {"isLogger": false, "level": "debug", "path": "", "pattern": ""}}}}