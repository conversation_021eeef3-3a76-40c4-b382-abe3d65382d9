{"job": {"content": [{"reader": {"parameter": {"url": "%s/test/json", "requestMode": "get", "timeOut": 10000, "dataSubject": "${data}", "decode": "json", "cycles": 3, "column": [{"name": "price", "type": "string"}, {"name": "name", "type": "string"}], "header": [{"key": "Content-Type", "value": "application/json"}]}, "name": "restapireader"}, "writer": {"parameter": {"print": true}, "name": "streamwriter"}}], "setting": {"restore": {"isRestore": false, "isStream": false}, "speed": {"channel": 1}}}}