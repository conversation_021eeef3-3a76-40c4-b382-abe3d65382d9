{"job": {"content": [{"reader": {"parameter": {"column": [{"name": "id", "type": "id"}, {"name": "string", "type": "string"}], "sliceRecordCount": ["100"]}, "name": "streamreader"}, "writer": {"parameter": {"dataSubject": "${data.a}", "batchSize": 5, "url": "%s/test/write/json", "header": [{"Content-Type": "application/json"}], "body": [], "method": "post", "params": {}, "column": ["id", "data"]}, "name": "restapiwriter"}}], "setting": {"speed": {"channel": 1}}}}