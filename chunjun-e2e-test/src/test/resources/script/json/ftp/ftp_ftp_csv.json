{
  "job":{
    "content":[
      {
        "reader":{
          "parameter":{
            "path":"/data/dujie/data.csv",
            "protocol":"sftp",
            "port":%s,
            "isFirstLineHeader":false,
            "host":"%s",
            "column":[
              {
                "index":0,
                "type":"int",
                "name": "c_id"
              },
              {
                "index":1,
                "type":"boolean",
                "name": "c_boolean"
              },
              {
                "index":2,
                "type":"tinyint",
                "name": "c_tinyint"
              },
              {
                "index":3,
                "type":"smallint",
                "name": "c_smallint"
              },
              {
                "index":4,
                "type":"time",
                "name": "c_time"
              },
              {
                "index":5,
                "type":"bigint",
                "name": "c_bigint"
              },
              {
                "index":6,
                "type":"float",
                "name": "c_float"
              },
              {
                "index":7,
                "type":"double",
                "name": "c_double"
              },
              {
                "index":8,
                "type":"decimal",
                "name": "c_decimal"
              },
              {
                "index":9,
                "type":"string",
                "name": "c_string"
              },
              {
                "index":10,
                "type":"varchar",
                "name": "c_varchar"
              },
              {
                "index":11,
                "type":"char",
                "name": "c_char"
              },
              {
                "index":12,
                "type":"timestamp",
                "name": "c_timestamp"
              },
              {
                "index":13,
                "type":"date",
                "name": "c_date"
              }
            ],
            "password":"%s",
            "fieldDelimiter":",",
            "encoding":"utf-8",
            "username":"%s"
          },
          "name":"ftpreader"
        },
        "writer":{
          "parameter": {
            "path": "/tmp",
            "protocol": "sftp",
            "port": %s,
            "writeMode": "append",
            "host": "%s",
            "column":[
              {
                "index":0,
                "type":"int",
                "name": "c_id"
              },
              {
                "index":1,
                "type":"boolean",
                "name": "c_boolean"
              },
              {
                "index":2,
                "type":"tinyint",
                "name": "c_tinyint"
              },
              {
                "index":3,
                "type":"smallint",
                "name": "c_smallint"
              },
              {
                "index":4,
                "type":"time",
                "name": "c_time"
              },
              {
                "index":5,
                "type":"bigint",
                "name": "c_bigint"
              },
              {
                "index":6,
                "type":"float",
                "name": "c_float"
              },
              {
                "index":7,
                "type":"double",
                "name": "c_double"
              },
              {
                "index":8,
                "type":"decimal",
                "name": "c_decimal"
              },
              {
                "index":9,
                "type":"string",
                "name": "c_string"
              },
              {
                "index":10,
                "type":"varchar",
                "name": "c_varchar"
              },
              {
                "index":11,
                "type":"char",
                "name": "c_char"
              },
              {
                "index":12,
                "type":"timestamp",
                "name": "c_timestamp"
              },
              {
                "index":13,
                "type":"date",
                "name": "c_date"
              }
            ],
            "password": "%s",
            "fieldDelimiter": ",",
            "encoding": "utf-8",
            "username": "%s"
          },
          "name":"ftpwriter"
        }
      }
    ],
    "setting":{
      "restore":{
        "maxRowNumForCheckpoint":0,
        "isRestore":false,
        "restoreColumnName":"",
        "restoreColumnIndex":0
      },
      "errorLimit":{
        "record":0
      },
      "speed":{
        "bytes":0,
        "channel":1
      }
    }
  }
}
