{"job": {"content": [{"reader": {"parameter": {"customSql": "", "startLocation": "", "increColumn": "", "column": [{"customConverterType": "NUMBER", "name": "ID", "isPart": false, "type": "NUMBER", "key": "ID"}, {"customConverterType": "VARCHAR2", "name": "VARCHAR2_TYPE", "isPart": false, "type": "VARCHAR2", "key": "VARCHAR2_TYPE"}, {"customConverterType": "NVARCHAR2", "name": "NVARCHAR2_TYPE", "isPart": false, "type": "NVARCHAR2", "key": "NVARCHAR2_TYPE"}, {"customConverterType": "NUMBER(12,2)", "name": "NUMBER_TYPE", "isPart": false, "type": "NUMBER(12,2)", "key": "NUMBER_TYPE"}, {"customConverterType": "FLOAT", "name": "FLOAT_TYPE", "isPart": false, "type": "FLOAT", "key": "FLOAT_TYPE"}, {"customConverterType": "LONG RAW", "name": "LONG_RAW_TYPE", "isPart": false, "type": "LONG RAW", "key": "LONG_RAW_TYPE"}, {"customConverterType": "DATE", "name": "DATE_TYPE", "isPart": false, "type": "DATE", "key": "DATE_TYPE"}, {"customConverterType": "BINARY_FLOAT", "name": "BINARY_FLOAT_TYPE", "isPart": false, "type": "BINARY_FLOAT", "key": "BINARY_FLOAT_TYPE"}, {"customConverterType": "BINARY_DOUBLE", "name": "BINARY_DOUBLE_TYPE", "isPart": false, "type": "BINARY_DOUBLE", "key": "BINARY_DOUBLE_TYPE"}, {"customConverterType": "TIMESTAMP(6)", "name": "TIMESTAMP_TYPE", "isPart": false, "type": "TIMESTAMP(6)", "key": "TIMESTAMP_TYPE"}, {"customConverterType": "TIMESTAMP(9)", "name": "TIMESTAMP9_TYPE", "isPart": false, "type": "TIMESTAMP(9)", "key": "TIMESTAMP9_TYPE"}, {"customConverterType": "TIMESTAMP(9) WITH TIME ZONE", "name": "TIMESTAMP_TIME_ZONE_TYPE", "isPart": false, "type": "TIMESTAMP(9) WITH TIME ZONE", "key": "TIMESTAMP_TIME_ZONE_TYPE"}, {"customConverterType": "TIMESTAMP(9) WITH LOCAL TIME ZONE", "name": "TIMESTAMP_LOCAL_TIME_ZONE_TYPE", "isPart": false, "type": "TIMESTAMP(9) WITH LOCAL TIME ZONE", "key": "TIMESTAMP_LOCAL_TIME_ZONE_TYPE"}, {"customConverterType": "INTERVAL YEAR(2) TO MONTH", "name": "INTERVAL_YEAR_TO_MONTH_TYPE", "isPart": false, "type": "INTERVAL YEAR(2) TO MONTH", "key": "INTERVAL_YEAR_TO_MONTH_TYPE"}, {"customConverterType": "INTERVAL DAY(2) TO SECOND(6)", "name": "INTERVAL_DAY_TO_SECOND_TYPE", "isPart": false, "type": "INTERVAL DAY(2) TO SECOND(6)", "key": "INTERVAL_DAY_TO_SECOND_TYPE"}, {"customConverterType": "RAW", "name": "RAW_TYPE", "isPart": false, "type": "RAW", "key": "RAW_TYPE"}, {"customConverterType": "INTERVAL DAY(6) TO SECOND(6)", "name": "INTERVAL_DAY6_TO_SECOND_TYPE", "isPart": false, "type": "INTERVAL DAY(6) TO SECOND(6)", "key": "INTERVAL_DAY6_TO_SECOND_TYPE"}, {"customConverterType": "INTERVAL YEAR(6) TO MONTH", "name": "INTERVAL_YEAR6_TO_MONTH_TYPE", "isPart": false, "type": "INTERVAL YEAR(6) TO MONTH", "key": "INTERVAL_YEAR6_TO_MONTH_TYPE"}, {"customConverterType": "UROWID", "name": "UROWID_TYPE", "isPart": false, "type": "UROWID", "key": "UROWID_TYPE"}, {"customConverterType": "ROWID", "name": "ROWID_TYPE", "isPart": false, "type": "ROWID", "key": "ROWID_TYPE"}, {"customConverterType": "CHAR", "name": "CHAR_TYPE", "isPart": false, "type": "CHAR", "key": "CHAR_TYPE"}, {"customConverterType": "NCHAR", "name": "NCHAR_TYPE", "isPart": false, "type": "NCHAR", "key": "NCHAR_TYPE"}, {"customConverterType": "CLOB", "name": "CLOB_TYPE", "isPart": false, "type": "CLOB", "key": "CLOB_TYPE"}, {"customConverterType": "NCLOB", "name": "NCLOB_TYPE", "isPart": false, "type": "NCLOB", "key": "NCLOB_TYPE"}, {"customConverterType": "BLOB", "name": "BLOB_TYPE", "isPart": false, "type": "BLOB", "key": "BLOB_TYPE"}], "username": "%s", "connection": [{"schema": "CHUNJUN", "password": "%s", "jdbcUrl": ["%s"], "table": ["%s"]}]}, "name": "oraclereader"}, "writer": {"parameter": {"schema": "CHUNJUN", "postSql": [], "session": [], "column": [{"customConverterType": "NUMBER", "name": "ID", "isPart": false, "type": "NUMBER", "key": "ID"}, {"customConverterType": "VARCHAR2", "name": "VARCHAR2_TYPE", "isPart": false, "type": "VARCHAR2", "key": "VARCHAR2_TYPE"}, {"customConverterType": "NVARCHAR2", "name": "NVARCHAR2_TYPE", "isPart": false, "type": "NVARCHAR2", "key": "NVARCHAR2_TYPE"}, {"customConverterType": "NUMBER(12,2)", "name": "NUMBER_TYPE", "isPart": false, "type": "NUMBER(12,2)", "key": "NUMBER_TYPE"}, {"customConverterType": "FLOAT", "name": "FLOAT_TYPE", "isPart": false, "type": "FLOAT", "key": "FLOAT_TYPE"}, {"customConverterType": "LONG RAW", "name": "LONG_RAW_TYPE", "isPart": false, "type": "LONG RAW", "key": "LONG_RAW_TYPE"}, {"customConverterType": "DATE", "name": "DATE_TYPE", "isPart": false, "type": "DATE", "key": "DATE_TYPE"}, {"customConverterType": "BINARY_FLOAT", "name": "BINARY_FLOAT_TYPE", "isPart": false, "type": "BINARY_FLOAT", "key": "BINARY_FLOAT_TYPE"}, {"customConverterType": "BINARY_DOUBLE", "name": "BINARY_DOUBLE_TYPE", "isPart": false, "type": "BINARY_DOUBLE", "key": "BINARY_DOUBLE_TYPE"}, {"customConverterType": "TIMESTAMP(6)", "name": "TIMESTAMP_TYPE", "isPart": false, "type": "TIMESTAMP(6)", "key": "TIMESTAMP_TYPE"}, {"customConverterType": "TIMESTAMP(9)", "name": "TIMESTAMP9_TYPE", "isPart": false, "type": "TIMESTAMP(9)", "key": "TIMESTAMP9_TYPE"}, {"customConverterType": "TIMESTAMP(9) WITH TIME ZONE", "name": "TIMESTAMP_TIME_ZONE_TYPE", "isPart": false, "type": "TIMESTAMP(9) WITH TIME ZONE", "key": "TIMESTAMP_TIME_ZONE_TYPE"}, {"customConverterType": "TIMESTAMP(9) WITH LOCAL TIME ZONE", "name": "TIMESTAMP_LOCAL_TIME_ZONE_TYPE", "isPart": false, "type": "TIMESTAMP(9) WITH LOCAL TIME ZONE", "key": "TIMESTAMP_LOCAL_TIME_ZONE_TYPE"}, {"customConverterType": "INTERVAL YEAR(2) TO MONTH", "name": "INTERVAL_YEAR_TO_MONTH_TYPE", "isPart": false, "type": "INTERVAL YEAR(2) TO MONTH", "key": "INTERVAL_YEAR_TO_MONTH_TYPE"}, {"customConverterType": "INTERVAL DAY(2) TO SECOND(6)", "name": "INTERVAL_DAY_TO_SECOND_TYPE", "isPart": false, "type": "INTERVAL DAY(2) TO SECOND(6)", "key": "INTERVAL_DAY_TO_SECOND_TYPE"}, {"customConverterType": "RAW", "name": "RAW_TYPE", "isPart": false, "type": "RAW", "key": "RAW_TYPE"}, {"customConverterType": "INTERVAL DAY(6) TO SECOND(6)", "name": "INTERVAL_DAY6_TO_SECOND_TYPE", "isPart": false, "type": "INTERVAL DAY(6) TO SECOND(6)", "key": "INTERVAL_DAY6_TO_SECOND_TYPE"}, {"customConverterType": "INTERVAL YEAR(6) TO MONTH", "name": "INTERVAL_YEAR6_TO_MONTH_TYPE", "isPart": false, "type": "INTERVAL YEAR(6) TO MONTH", "key": "INTERVAL_YEAR6_TO_MONTH_TYPE"}, {"customConverterType": "UROWID", "name": "UROWID_TYPE", "isPart": false, "type": "UROWID", "key": "UROWID_TYPE"}, {"customConverterType": "ROWID", "name": "ROWID_TYPE", "isPart": false, "type": "ROWID", "key": "ROWID_TYPE"}, {"customConverterType": "CHAR", "name": "CHAR_TYPE", "isPart": false, "type": "CHAR", "key": "CHAR_TYPE"}, {"customConverterType": "NCHAR", "name": "NCHAR_TYPE", "isPart": false, "type": "NCHAR", "key": "NCHAR_TYPE"}, {"customConverterType": "CLOB", "name": "CLOB_TYPE", "isPart": false, "type": "CLOB", "key": "CLOB_TYPE"}, {"customConverterType": "NCLOB", "name": "NCLOB_TYPE", "isPart": false, "type": "NCLOB", "key": "NCLOB_TYPE"}, {"customConverterType": "BLOB", "name": "BLOB_TYPE", "isPart": false, "type": "BLOB", "key": "BLOB_TYPE"}], "username": "%s", "password": "%s", "connection": [{"schema": "CHUNJUN", "jdbcUrl": "%s", "table": ["%s"]}], "writeMode": "insert", "preSql": []}, "name": "oraclewriter"}}], "setting": {"restore": {"maxRowNumForCheckpoint": 0, "isRestore": false, "restoreColumnName": "", "restoreColumnIndex": 0}, "errorLimit": {"record": 100}, "speed": {"readerChannel": 1, "writerChannel": 1, "bytes": 0, "channel": 1}}}}