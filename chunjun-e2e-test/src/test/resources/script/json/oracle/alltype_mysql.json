{"job": {"content": [{"reader": {"parameter": {"password": "%s", "customSql": "", "startLocation": "", "column": [{"customConverterType": "NUMBER", "name": "ID", "isPart": false, "type": "NUMBER", "key": "ID"}, {"customConverterType": "VARCHAR2", "name": "VARCHAR2_TYPE", "isPart": false, "type": "VARCHAR2", "key": "VARCHAR2_TYPE"}, {"customConverterType": "NVARCHAR2", "name": "NVARCHAR2_TYPE", "isPart": false, "type": "NVARCHAR2", "key": "NVARCHAR2_TYPE"}, {"customConverterType": "NUMBER(12,2)", "name": "NUMBER_TYPE", "isPart": false, "type": "NUMBER(12,2)", "key": "NUMBER_TYPE"}, {"customConverterType": "FLOAT", "name": "FLOAT_TYPE", "isPart": false, "type": "FLOAT", "key": "FLOAT_TYPE"}, {"customConverterType": "LONG RAW", "name": "LONG_RAW_TYPE", "isPart": false, "type": "LONG RAW", "key": "LONG_RAW_TYPE"}, {"customConverterType": "DATE", "name": "DATE_TYPE", "isPart": false, "type": "DATE", "key": "DATE_TYPE"}, {"customConverterType": "BINARY_FLOAT", "name": "BINARY_FLOAT_TYPE", "isPart": false, "type": "BINARY_FLOAT", "key": "BINARY_FLOAT_TYPE"}, {"customConverterType": "BINARY_DOUBLE", "name": "BINARY_DOUBLE_TYPE", "isPart": false, "type": "BINARY_DOUBLE", "key": "BINARY_DOUBLE_TYPE"}, {"customConverterType": "TIMESTAMP(6)", "name": "TIMESTAMP_TYPE", "isPart": false, "type": "TIMESTAMP(6)", "key": "TIMESTAMP_TYPE"}, {"customConverterType": "TIMESTAMP(9)", "name": "TIMESTAMP9_TYPE", "isPart": false, "type": "TIMESTAMP(9)", "key": "TIMESTAMP9_TYPE"}, {"customConverterType": "TIMESTAMP(9) WITH TIME ZONE", "name": "TIMESTAMP_TIME_ZONE_TYPE", "isPart": false, "type": "TIMESTAMP(9) WITH TIME ZONE", "key": "TIMESTAMP_TIME_ZONE_TYPE"}, {"customConverterType": "TIMESTAMP(9) WITH LOCAL TIME ZONE", "name": "TIMESTAMP_LOCAL_TIME_ZONE_TYPE", "isPart": false, "type": "TIMESTAMP(9) WITH LOCAL TIME ZONE", "key": "TIMESTAMP_LOCAL_TIME_ZONE_TYPE"}, {"customConverterType": "INTERVAL YEAR(2) TO MONTH", "name": "INTERVAL_YEAR_TO_MONTH_TYPE", "isPart": false, "type": "INTERVAL YEAR(2) TO MONTH", "key": "INTERVAL_YEAR_TO_MONTH_TYPE"}, {"customConverterType": "INTERVAL DAY(2) TO SECOND(6)", "name": "INTERVAL_DAY_TO_SECOND_TYPE", "isPart": false, "type": "INTERVAL DAY(2) TO SECOND(6)", "key": "INTERVAL_DAY_TO_SECOND_TYPE"}, {"customConverterType": "RAW", "name": "RAW_TYPE", "isPart": false, "type": "RAW", "key": "RAW_TYPE"}, {"customConverterType": "INTERVAL DAY(6) TO SECOND(6)", "name": "INTERVAL_DAY6_TO_SECOND_TYPE", "isPart": false, "type": "INTERVAL DAY(6) TO SECOND(6)", "key": "INTERVAL_DAY6_TO_SECOND_TYPE"}, {"customConverterType": "INTERVAL YEAR(6) TO MONTH", "name": "INTERVAL_YEAR6_TO_MONTH_TYPE", "isPart": false, "type": "INTERVAL YEAR(6) TO MONTH", "key": "INTERVAL_YEAR6_TO_MONTH_TYPE"}, {"customConverterType": "UROWID", "name": "UROWID_TYPE", "isPart": false, "type": "UROWID", "key": "UROWID_TYPE"}, {"customConverterType": "ROWID", "name": "ROWID_TYPE", "isPart": false, "type": "ROWID", "key": "ROWID_TYPE"}, {"customConverterType": "CHAR", "name": "CHAR_TYPE", "isPart": false, "type": "CHAR", "key": "CHAR_TYPE"}, {"customConverterType": "NCHAR", "name": "NCHAR_TYPE", "isPart": false, "type": "NCHAR", "key": "NCHAR_TYPE"}, {"customConverterType": "CLOB", "name": "CLOB_TYPE", "isPart": false, "type": "CLOB", "key": "CLOB_TYPE"}, {"customConverterType": "NCLOB", "name": "NCLOB_TYPE", "isPart": false, "type": "NCLOB", "key": "NCLOB_TYPE"}, {"customConverterType": "BLOB", "name": "BLOB_TYPE", "isPart": false, "type": "BLOB", "key": "BLOB_TYPE"}], "connection": [{"schema": "CHUNJUN", "password": "%s", "jdbcUrl": ["%s"], "table": ["ALL_TYPE"], "username": "%s"}], "username": "chun<PERSON>"}, "name": "oraclereader"}, "writer": {"parameter": {"schema": "chunjun_temp", "postSql": [], "password": "%s", "session": [], "column": [{"customConverterType": "BIGINT", "name": "id", "isPart": false, "type": "BIGINT", "key": "id"}, {"customConverterType": "VARCHAR", "name": "varchar_01", "isPart": false, "type": "VARCHAR", "key": "varchar_01"}, {"customConverterType": "VARCHAR", "name": "varchar_02", "isPart": false, "type": "VARCHAR", "key": "varchar_02"}, {"customConverterType": "DECIMAL", "name": "decimal_01", "isPart": false, "type": "DECIMAL", "key": "decimal_01"}, {"customConverterType": "FLOAT", "name": "float_01", "isPart": false, "type": "FLOAT", "key": "float_01"}, {"customConverterType": "BLOB", "name": "blob_01", "isPart": false, "type": "BLOB", "key": "blob_01"}, {"customConverterType": "DATE", "name": "date_01", "isPart": false, "type": "DATE", "key": "date_01"}, {"customConverterType": "FLOAT", "name": "float_02", "isPart": false, "type": "FLOAT", "key": "float_02"}, {"customConverterType": "DOUBLE", "name": "double_01", "isPart": false, "type": "DOUBLE", "key": "double_01"}, {"customConverterType": "TIMESTAMP", "name": "timestamp_01", "isPart": false, "type": "TIMESTAMP", "key": "timestamp_01"}, {"customConverterType": "DATETIME", "name": "datetime_03", "isPart": false, "type": "DATETIME", "key": "datetime_03"}, {"customConverterType": "DATETIME", "name": "datetime_01", "isPart": false, "type": "DATETIME", "key": "datetime_01"}, {"customConverterType": "DATETIME", "name": "datetime_02", "isPart": false, "type": "DATETIME", "key": "datetime_02"}, {"customConverterType": "VARCHAR", "name": "varchar_03", "isPart": false, "type": "VARCHAR", "key": "varchar_03"}, {"customConverterType": "VARCHAR", "name": "varchar_04", "isPart": false, "type": "VARCHAR", "key": "varchar_04"}, {"customConverterType": "BLOB", "name": "blob_02", "isPart": false, "type": "BLOB", "key": "blob_02"}, {"customConverterType": "VARCHAR", "name": "varchar_05", "isPart": false, "type": "VARCHAR", "key": "varchar_05"}, {"customConverterType": "VARCHAR", "name": "varchar_06", "isPart": false, "type": "VARCHAR", "key": "varchar_06"}, {"customConverterType": "VARCHAR", "name": "varchar_07", "isPart": false, "type": "VARCHAR", "key": "varchar_07"}, {"customConverterType": "VARCHAR", "name": "varchar_08", "isPart": false, "type": "VARCHAR", "key": "varchar_08"}, {"customConverterType": "VARCHAR", "name": "varchar_09", "isPart": false, "type": "VARCHAR", "key": "varchar_09"}, {"customConverterType": "VARCHAR", "name": "varchar_10", "isPart": false, "type": "VARCHAR", "key": "varchar_10"}, {"customConverterType": "VARCHAR", "name": "varchar_11", "isPart": false, "type": "VARCHAR", "key": "varchar_11"}, {"customConverterType": "VARCHAR", "name": "varchar_12", "isPart": false, "type": "VARCHAR", "key": "varchar_12"}, {"customConverterType": "BLOB", "name": "blob_03", "isPart": false, "type": "BLOB", "key": "blob_03"}], "connection": [{"schema": "chunjun_temp", "jdbcUrl": "%s", "table": ["%s"]}], "writeMode": "insert", "username": "%s", "preSql": []}, "name": "mysqlwriter"}}], "setting": {"restore": {"maxRowNumForCheckpoint": 0, "isRestore": false, "restoreColumnName": "", "restoreColumnIndex": 0}, "errorLimit": {"record": 0}, "speed": {"readerChannel": 1, "writerChannel": 1, "bytes": 0, "channel": 1}}}}