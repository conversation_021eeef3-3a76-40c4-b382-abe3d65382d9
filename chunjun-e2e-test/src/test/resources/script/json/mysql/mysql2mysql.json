{"job": {"content": [{"reader": {"parameter": {"customSql": "", "startLocation": "", "increColumn": "", "column": [{"customConverterType": "INT", "name": "id", "isPart": false, "type": "INT", "key": "id"}, {"customConverterType": "VARCHAR", "name": "name", "isPart": false, "type": "VARCHAR", "key": "name"}, {"customConverterType": "DECIMAL", "name": "money", "isPart": false, "type": "DECIMAL", "key": "money"}, {"customConverterType": "BIGINT", "name": "aphone", "isPart": false, "type": "BIGINT", "key": "aphone"}, {"customConverterType": "VARCHAR", "name": "aqq", "isPart": false, "type": "VARCHAR", "key": "aqq"}, {"customConverterType": "VARCHAR", "name": "awechat", "isPart": false, "type": "VARCHAR", "key": "awechat"}, {"customConverterType": "DECIMAL", "name": "aincome", "isPart": false, "type": "DECIMAL", "key": "aincome"}, {"customConverterType": "DATETIME", "name": "abirthday", "isPart": false, "type": "DATETIME", "key": "abirthday"}, {"customConverterType": "DATE", "name": "atoday", "isPart": false, "type": "DATE", "key": "atoday"}, {"customConverterType": "TIME", "name": "atimecurrent", "isPart": false, "type": "TIME", "key": "atimecurrent"}, {"customConverterType": "BIGINT", "name": "bphone", "isPart": false, "type": "BIGINT", "key": "bphone"}, {"customConverterType": "VARCHAR", "name": "bqq", "isPart": false, "type": "VARCHAR", "key": "bqq"}, {"customConverterType": "DECIMAL", "name": "bincome", "isPart": false, "type": "DECIMAL", "key": "bincome"}, {"customConverterType": "DATETIME", "name": "bbirthday", "isPart": false, "type": "DATETIME", "key": "bbirthday"}, {"customConverterType": "DATE", "name": "btoday", "isPart": false, "type": "DATE", "key": "btoday"}, {"customConverterType": "BIT", "name": "aboolean", "isPart": false, "type": "BIT", "key": "aboolean"}, {"customConverterType": "DOUBLE", "name": "adouble", "isPart": false, "type": "DOUBLE", "key": "adouble"}, {"customConverterType": "FLOAT", "name": "afloat", "isPart": false, "type": "FLOAT", "key": "afloat"}, {"customConverterType": "CHAR", "name": "achar", "isPart": false, "type": "CHAR", "key": "achar"}, {"customConverterType": "TINYINT", "name": "atinyint", "isPart": false, "type": "TINYINT", "key": "atinyint"}], "connection": [{"schema": "chun<PERSON>", "username": "%s", "password": "%s", "jdbcUrl": ["%s"], "table": ["%s"]}], "username": "root"}, "name": "mysqlreader"}, "writer": {"parameter": {"schema": "chun<PERSON>", "postSql": [], "session": [], "column": [{"customConverterType": "INT", "name": "id", "isPart": false, "type": "INT", "key": "id"}, {"customConverterType": "VARCHAR", "name": "name", "isPart": false, "type": "VARCHAR", "key": "name"}, {"customConverterType": "DECIMAL", "name": "money", "isPart": false, "type": "DECIMAL", "key": "money"}, {"customConverterType": "BIGINT", "name": "aphone", "isPart": false, "type": "BIGINT", "key": "aphone"}, {"customConverterType": "VARCHAR", "name": "aqq", "isPart": false, "type": "VARCHAR", "key": "aqq"}, {"customConverterType": "VARCHAR", "name": "awechat", "isPart": false, "type": "VARCHAR", "key": "awechat"}, {"customConverterType": "DECIMAL", "name": "aincome", "isPart": false, "type": "DECIMAL", "key": "aincome"}, {"customConverterType": "DATETIME", "name": "abirthday", "isPart": false, "type": "DATETIME", "key": "abirthday"}, {"customConverterType": "DATE", "name": "atoday", "isPart": false, "type": "DATE", "key": "atoday"}, {"customConverterType": "TIME", "name": "atimecurrent", "isPart": false, "type": "TIME", "key": "atimecurrent"}, {"customConverterType": "BIGINT", "name": "bphone", "isPart": false, "type": "BIGINT", "key": "bphone"}, {"customConverterType": "VARCHAR", "name": "bqq", "isPart": false, "type": "VARCHAR", "key": "bqq"}, {"customConverterType": "DECIMAL", "name": "bincome", "isPart": false, "type": "DECIMAL", "key": "bincome"}, {"customConverterType": "DATETIME", "name": "bbirthday", "isPart": false, "type": "DATETIME", "key": "bbirthday"}, {"customConverterType": "DATE", "name": "btoday", "isPart": false, "type": "DATE", "key": "btoday"}, {"customConverterType": "BIT", "name": "aboolean", "isPart": false, "type": "BIT", "key": "aboolean"}, {"customConverterType": "DOUBLE", "name": "adouble", "isPart": false, "type": "DOUBLE", "key": "adouble"}, {"customConverterType": "FLOAT", "name": "afloat", "isPart": false, "type": "FLOAT", "key": "afloat"}, {"customConverterType": "CHAR", "name": "achar", "isPart": false, "type": "CHAR", "key": "achar"}, {"customConverterType": "TINYINT", "name": "atinyint", "isPart": false, "type": "TINYINT", "key": "atinyint"}], "username": "%s", "connection": [{"schema": "chun<PERSON>", "password": "%s", "jdbcUrl": "%s", "table": ["%s"]}], "writeMode": "insert", "preSql": []}, "name": "mysqlwriter"}}], "setting": {"restore": {"maxRowNumForCheckpoint": 0, "isRestore": false, "restoreColumnName": "", "restoreColumnIndex": 0}, "errorLimit": {"record": 100}, "speed": {"readerChannel": 1, "writerChannel": 1, "bytes": 0, "channel": 1}}}}