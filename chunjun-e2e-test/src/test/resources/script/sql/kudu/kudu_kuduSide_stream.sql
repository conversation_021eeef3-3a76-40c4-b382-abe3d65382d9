CREATE TABLE kudu_source
(
    id   bigint,
    int8_data tinyint,
    int16_data smallint,
    int32_data int,
    int64_data bigint,
    binary_data binary,
    string_data string,
    bool_data boolean,
    float_data float,
    double_data double,
    unixtime_micros_data bigint,
    decimal_data decimal,
    PROCTIME AS PROCTIME()
) WITH (
      'connector' = 'kudu-x',
      'masters' = '172.16.100.109:7051',
      'table-name' = 'e2e_sql_source'
      );

CREATE TABLE kudu_side
(
    id   bigint,
    int8_data tinyint,
    int16_data smallint,
    int32_data int,
    int64_data bigint,
    binary_data binary,
    string_data string,
    bool_data boolean,
    float_data float,
    double_data double,
    unixtime_micros_data bigint,
    decimal_data decimal,
    PRIMARY KEY (id) NOT ENFORCED
) WITH (
      'connector' = 'kudu-x',
      'masters' = '172.16.100.109:7051',
      'table-name' = 'e2e_sql_sink',
      'lookup.cache-type' = 'lru'
      );

CREATE TABLE stream_sink
(
    id   bigint,
    int8_data tinyint,
    int16_data smallint,
    int32_data int,
    int64_data bigint,
    binary_data binary,
    string_data string,
    bool_data boolean,
    float_data float,
    double_data double,
    unixtime_micros_data bigint,
    decimal_data decimal
) WITH (
      'connector' = 'stream-x',
      'print' = 'true'
      );

create
TEMPORARY view view_out as
select s.*
from kudu_source u
         join kudu_side FOR SYSTEM_TIME AS OF u.PROCTIME AS s on u.id = s.id;

insert into stream_sink
select *
from view_out;
