CREATE TABLE kudu_source
(
    id   bigint,
    int8_data tinyint,
    int16_data smallint,
    int32_data int,
    int64_data bigint,
    binary_data binary,
    string_data string,
    bool_data boolean,
    float_data float,
    double_data double,
    unixtime_micros_data bigint,
    decimal_data decimal
) WITH (
      'connector' = 'kudu-x',
      'masters' = '172.16.100.109:7051',
      'table-name' = 'e2e_sql_source'
      );

CREATE TABLE stream_sink
(
    id   bigint,
    int8_data tinyint,
    int16_data smallint,
    int32_data int,
    int64_data bigint,
    binary_data binary,
    string_data string,
    bool_data boolean,
    float_data float,
    double_data double,
    unixtime_micros_data bigint,
    decimal_data decimal
) WITH (
      'connector' = 'stream-x',
      'number-of-rows' = '100'
      );

insert into stream_sink
select *
from kudu_source;
