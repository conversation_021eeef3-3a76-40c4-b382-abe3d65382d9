CREATE TABLE source_ods_fact_user_ippv (
                                           id INT
    , name STRING
    , description STRING
    , weight DOUBLE
    ,`table`      STRING METADATA FROM 'value.table'
    , otype  STRING METADATA FROM 'value.type'
) WITH (
      'connector' = 'kafka-x'
      ,'topic' = '%s'
      ,'properties.bootstrap.servers' = '%s'
      ,'properties.group.id' = 'luna_g'
      ,'scan.startup.mode' = 'latest-offset'
      ,'format' = 'chunjun-cdc-json-x'
      ,'scan.parallelism' = '1'
      );


CREATE TABLE result_total_pvuv_min
(
    id INT
    , name STRING
    , description STRING
    , weight DOUBLE
    ,`table` STRING
    , otype  STRING
) WITH (
      'connector' = 'stream-x'
      );


INSERT INTO result_total_pvuv_min
SELECT *
from source_ods_fact_user_ippv;
