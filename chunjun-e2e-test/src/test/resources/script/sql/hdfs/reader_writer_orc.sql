CREATE TABLE source
(
    id int,
    name string
) WITH (
      'connector' = 'hdfs-x'
      ,'path' = 'hdfs://ns1/dtInsight/hive/warehouse/qingxing.db/%s/pt=%s'
      ,'properties.hadoop.user.name' = 'admin'
      ,'properties.dfs.ha.namenodes.ns1' = 'nn1,nn2'
      ,'properties.fs.defaultFS' = 'hdfs://ns1'
      ,'properties.dfs.namenode.rpc-address.ns1.nn2' = '*************:9000'
      ,'properties.dfs.client.failover.proxy.provider.ns1' = 'org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider'
      ,'properties.dfs.namenode.rpc-address.ns1.nn1' = '*************:9000'
      ,'properties.dfs.nameservices' = 'ns1'
      ,'properties.fs.hdfs.impl.disable.cache' = 'true'
      ,'properties.fs.hdfs.impl' = 'org.apache.hadoop.hdfs.DistributedFileSystem'
      ,'default-fs' = 'hdfs://ns1'
      ,'file-type' = 'orc'
      );

CREATE TABLE sink
(
    id int,
    name string
) WITH (
      'connector' = 'hdfs-x'
      ,'path' = 'hdfs://ns1/dtInsight/hive/warehouse/qingxing.db/%s'
      ,'file-name' = 'pt=%s'
      ,'properties.hadoop.user.name' = 'admin'
      ,'properties.dfs.ha.namenodes.ns1' = 'nn1,nn2'
      ,'properties.fs.defaultFS' = 'hdfs://ns1'
      ,'properties.dfs.namenode.rpc-address.ns1.nn2' = '*************:9000'
      ,'properties.dfs.client.failover.proxy.provider.ns1' = 'org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider'
      ,'properties.dfs.namenode.rpc-address.ns1.nn1' = '*************:9000'
      ,'properties.dfs.nameservices' = 'ns1'
      ,'properties.fs.hdfs.impl.disable.cache' = 'true'
      ,'properties.fs.hdfs.impl' = 'org.apache.hadoop.hdfs.DistributedFileSystem'
      ,'default-fs' = 'hdfs://ns1'
      ,'encoding' = 'utf-8'
      ,'max-file-size' = '10485760'
      ,'next-check-rows' = '20000'
      ,'write-mode' = 'overwrite'
      ,'file-type' = 'orc'
      );

insert into sink
select *
from source u;
