
CREATE TABLE FILE_SOURCE (
    C_<PERSON> INT,
    C_BOOLEAN BOOLEAN,
    C_TINYINT TINYINT,
    C_SMALLINT SMALLINT,
    C_TIME TIME,
    C_BIGINT BIGINT,
    C_FLOAT FLOAT,
    C_DOUBLE DOUBLE,
    C_DECIMAL DECIMAL,
    C_STRING STRING,
    C_VARCHAR VARCHAR(255),
    C_CHAR CHAR(255),
    C_TIMESTAMP TIMESTAMP,
    C_DATE DATE
) WITH (
    'connector' = 'stream-x',
    'number-of-rows' = '10'
);

CREATE TABLE SINK (
    C_ID INT,
    C_BOOLEAN BOOLEAN,
    C_TINYINT TINYINT,
    C_SMALLINT SMALLINT,
    C_TIME TIME,
    C_BIGINT BIGINT,
    C_FLOAT FLOAT,
    C_DOUBLE DOUBLE,
    C_DECIMAL DECIMAL,
    C_STRING STRING,
    C_VA<PERSON>HAR VARCHAR(255),
    <PERSON>_<PERSON><PERSON> CHAR(255),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> TIMESTAMP,
    C_<PERSON>ATE DATE
) WITH (
    'connector' = 'ftp-x',
    'path' = '/tmp',
    'protocol' = 'sftp',
    'host' = '%s',
    'username' = '%s',
    'password' = '%s',
    'format' = 'csv'
);

INSERT INTO SINK
SELECT *
FROM FILE_SOURCE;
