CREATE TABLE source
(
    price             double,
    name           varchar
) WITH (
      'connector' = 'http-x'
      ,'url' = '%s/test/json'
      ,'intervalTime'= '3000'
       ,'method'='post'
        ,'decode'= 'json'
        ,'header'='[
              {
                "key": "<PERSON>ie",
                "value": "experimentation_subject_id"
              },
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ]'
      );

CREATE TABLE sink
(
    price             double,
    name      varchar
) WITH (
      'connector' = 'stream-x'
      );

insert into sink
select *
from source u;
