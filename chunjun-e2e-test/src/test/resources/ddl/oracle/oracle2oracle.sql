ALTER SESSION SET CURRENT_SCHEMA=CHUNJUN;
CREATE TABLE CHUNJUN_READ_%s (
    "ID" NUMBER NOT NULL,
    "VARCHAR2_TYPE" VARCHAR2(255 BYTE),
    "NVARCHAR2_TYPE" NVARCHAR2(255),
    "NUMBER_TYPE" NUMBER(12,2) NOT NULL,
    "FLOAT_TYPE" FLOAT(12),
    "LONG_RAW_TYPE" LONG RAW,
    "DATE_TYPE" DATE,
    "BINARY_FLOAT_TYPE" BINARY_FLOAT,
    "BINARY_DOUBLE_TYPE" BINARY_DOUBLE,
    "TIMESTAMP_TYPE" TIMESTAMP(6),
    "TIMESTAMP9_TYPE" TIMESTAMP(9),
    "TIMESTAMP_TIME_ZONE_TYPE" TIMESTAMP(9) WITH TIME ZONE,
    "TIMESTAMP_LOCAL_TIME_ZONE_TYPE" TIMESTAMP(9) WITH LOCAL TIME ZONE,
    "INTERVAL_YEAR_TO_MONTH_TYPE" INTERVAL YEAR(2) TO MONTH,
    "INTERVAL_DAY_TO_SECOND_TYPE" INTERVAL DAY(2) TO SECOND(6),
    "RAW_TYPE" RAW(255),
    "INTERVAL_DAY6_TO_SECOND_TYPE" INTERVAL DAY(6) TO SECOND(6),
    "INTERVAL_YEAR6_TO_MONTH_TYPE" INTERVAL YEAR(6) TO MONTH,
    "UROWID_TYPE" UROWID(4000),
    "ROWID_TYPE" ROWID,
    "CHAR_TYPE" CHAR(255 BYTE),
    "NCHAR_TYPE" NCHAR(255),
    "CLOB_TYPE" CLOB,
    "NCLOB_TYPE" NCLOB,
    "BLOB_TYPE" BLOB
    );

INSERT INTO ALL_TYPE1("ID", "VARCHAR2_TYPE", "NVARCHAR2_TYPE", "NUMBER_TYPE", "FLOAT_TYPE", "LONG_RAW_TYPE", "DATE_TYPE", "BINARY_FLOAT_TYPE", "BINARY_DOUBLE_TYPE", "TIMESTAMP_TYPE", "TIMESTAMP9_TYPE", "TIMESTAMP_TIME_ZONE_TYPE", "TIMESTAMP_LOCAL_TIME_ZONE_TYPE", "INTERVAL_YEAR_TO_MONTH_TYPE", "INTERVAL_DAY_TO_SECOND_TYPE", "RAW_TYPE", "INTERVAL_DAY6_TO_SECOND_TYPE", "INTERVAL_YEAR6_TO_MONTH_TYPE", "UROWID_TYPE", "ROWID_TYPE", "CHAR_TYPE", "NCHAR_TYPE", "CLOB_TYPE", "NCLOB_TYPE", "BLOB_TYPE")
VALUES ('1', '中文123', '德玛西亚', '12.12', '123.1', HEXTORAW('312E31322C32332C330A3132332E32313332312E0A'), TO_DATE('2023-05-05 14:20:40', 'SYYYY-MM-DD HH24:MI:SS'), '4532.41', '423.234', TO_TIMESTAMP('2023-05-05 14:20:52.000000', 'SYYYY-MM-DD HH24:MI:SS:FF6'), TO_TIMESTAMP('2023-05-05 14:20:53.000000000', 'SYYYY-MM-DD HH24:MI:SS:FF9'), TO_TIMESTAMP_TZ('2023-05-05 14:20:56.988000000 +00:00', 'SYYYY-MM-DD HH24:MI:SS:FF9 TZR'), TO_TIMESTAMP('2023-05-05 22:20:58.000000000', 'SYYYY-MM-DD HH24:MI:SS:FF9'), TO_YMINTERVAL('+10-02'), TO_DSINTERVAL('+11 10:09:08.555000'), HEXTORAW('312E31322C32332C330A3132332E32313332312E0A'), TO_DSINTERVAL('+000011 10:09:08.555000'), TO_YMINTERVAL('+000010-02'), 'AAAt3WAAEAAOF81AAA', 'AAAt3WAAEAAOF81AAA', 'vdsfv324', '234fdfdsa', '1.12,23,3123.21321.', '1.12,23,3123.21321.', HEXTORAW('312E31322C32332C330A3132332E32313332312E0A'));

CREATE TABLE CHUNJUN_WRITE_%s (
    "ID" NUMBER NOT NULL,
    "VARCHAR2_TYPE" VARCHAR2(255 BYTE),
    "NVARCHAR2_TYPE" NVARCHAR2(255),
    "NUMBER_TYPE" NUMBER(12,2) NOT NULL,
    "FLOAT_TYPE" FLOAT(12),
    "LONG_RAW_TYPE" LONG RAW,
    "DATE_TYPE" DATE,
    "BINARY_FLOAT_TYPE" BINARY_FLOAT,
    "BINARY_DOUBLE_TYPE" BINARY_DOUBLE,
    "TIMESTAMP_TYPE" TIMESTAMP(6),
    "TIMESTAMP9_TYPE" TIMESTAMP(9),
    "TIMESTAMP_TIME_ZONE_TYPE" TIMESTAMP(9) WITH TIME ZONE,
    "TIMESTAMP_LOCAL_TIME_ZONE_TYPE" TIMESTAMP(9) WITH LOCAL TIME ZONE,
    "INTERVAL_YEAR_TO_MONTH_TYPE" INTERVAL YEAR(2) TO MONTH,
    "INTERVAL_DAY_TO_SECOND_TYPE" INTERVAL DAY(2) TO SECOND(6),
    "RAW_TYPE" RAW(255),
    "INTERVAL_DAY6_TO_SECOND_TYPE" INTERVAL DAY(6) TO SECOND(6),
    "INTERVAL_YEAR6_TO_MONTH_TYPE" INTERVAL YEAR(6) TO MONTH,
    "UROWID_TYPE" UROWID(4000),
    "ROWID_TYPE" ROWID,
    "CHAR_TYPE" CHAR(255 BYTE),
    "NCHAR_TYPE" NCHAR(255),
    "CLOB_TYPE" CLOB,
    "NCLOB_TYPE" NCLOB,
    "BLOB_TYPE" BLOB
    );
