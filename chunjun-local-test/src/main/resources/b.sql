CREATE TABLE source(
                       id int,
                       `name` varchar,
                       PROCTIME AS PROCTIME()
)WITH(
     'properties.bootstrap.servers' = '172.16.85.93:9092',
     'connector' = 'kafka-x',
     'scan.parallelism' = '1',
     'format' = 'json',
     'topic' = 'test_8889',
     'scan.startup.mode' = 'latest-offset'
     );
CREATE TABLE sink(
                     id int,
                     `name` string
)WITH(
     'jdbc-url' = '********************************',
     'password' = '',
     'connector' = 'starrocks-x',
     'database-name' = 'aaa',
     'load-url' = '172.16.112.99:18030',
     'sink.buffer-flush.max-rows' = '500000',
     'sink.buffer-flush.interval-ms' = '300000',
     'table-name' = 'score_board',
     'sink.parallelism' = '1',
     'version' = 'v2',
     'username' = 'root'
     );
CREATE TABLE side(
                     id int,
                     `name` string,
                     PRIMARY KEY(id) NOT ENFORCED
)WITH(
     'lookup.cache.ttl-ms' = '60000',
     'jdbc-url' = '********************************',
     'password' = '',
     'connector' = 'starrocks-x',
     'lookup.cache-type' = 'LRU',
     'scan-url' = '172.16.112.99:18030',
     'database-name' = 'aaa',
     'lookup.cache.max-rows' = '10000',
     'table-name' = 'score_board',
     'version' = 'v2',
     'username' = 'root'
     );
insert
into
    sink
select
    u.id as id,
    s.name
from
    source  u
        left join
    side FOR SYSTEM_TIME AS OF u.PROCTIME AS s
    on u.id = s.id;
