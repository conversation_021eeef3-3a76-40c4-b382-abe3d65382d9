#!/usr/bin/env bash

# parse args
# ./ci/utils/param_parse_util.sh

. ./ci/utils/curl_util.sh

exit_code=0
add_checklist_comment "请按照下面Checklist（但不限于此）进行Code Review \n* 空值检测 \n* 参数默认值或者代码属性默认值是否合理 \n* 重复代码 \n* 方法体太大，耦合严重 \n* 缺乏必要日志 \n* 资源未释放 \n* 复杂性，代码实现过于复杂 \n* 并发安全问题 \n* 参数变更是否已同步更新语雀文档 \n* 涉及开源组件源码变更是否添加changelog"
exit_code=$?

echo "add checklist comment for merge request, exit code: $exit_code"

exit $exit_code
