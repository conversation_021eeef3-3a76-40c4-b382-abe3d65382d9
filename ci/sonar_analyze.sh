#!/usr/bin/env bash

dingding_token="b28b3549ae7afcd42bafd173be3142e2df1bae8a9625d2c499646c3f858065cf"
sonar_web_url="http://**************:9001/dashboard?id=flinkx"

# parse args
is_send_dingding="false"
while getopts "s" opt; do
  case $opt in
    s)
      is_send_dingding="true"
      ;;
    \?)
      echo "Invalid option"
      exit 1
      ;;
  esac
done

exit_code=0
mvn clean org.jacoco:jacoco-maven-plugin:0.7.8:prepare-agent prepare-package \
  -Dmaven.test.failure.ignore=true \
  -q

current_branch=$CI_COMMIT_BRANCH
if [[ -z $current_branch ]]; then
  current_branch=$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME
fi
if [[ -z $current_branch ]]; then
  current_branch=`git rev-parse --abbrev-ref HEAD`
fi

exit_code=$?
if [[ $exit_code == 0 ]]; then
  branch_param="-Dsonar.branch.name=$current_branch"
  mvn sonar:sonar \
    -Dsonar.projectKey="flinkx" \
    -Dsonar.projectName="flinkx" \
    -Dsonar.host.url=http://**************:9001 \
    -Dsonar.jdbc.url=******************************************* \
    -Dsonar.ws.timeout=3600 \
    -Dsonar.java.binaries=target \
    -Dsonar.core.codeCoveragePlugin=jacoco \
    -Dsonar.jacoco.reportPath=jacoco.exec \
    -Dsonar.exclusions="src/main/java/org/**/*" \
    -Dsonar.login=**************************************** $branch_param

  exit_code=$?
fi


# send sonar url to dingding
if [[ "$is_send_dingding" == "true" ]]; then

  if [[ $exit_code == 0 ]]; then
    curl -s "https://oapi.dingtalk.com/robot/send?access_token=$dingding_token" \
         -H "Content-Type: application/json;charset=utf-8" \
         -d "{\"msgtype\": \"markdown\", \"markdown\": {
                \"title\":\"sonar代码质量\",
                \"text\": \"### flinkx代码质量报告 \n > 请点击 [sonar地址]($sonar_web_url) 查看 \"
               }
             }"
  else
    curl -s "https://oapi.dingtalk.com/robot/send?access_token=$dingding_token" \
       -H "Content-Type: application/json;charset=utf-8" \
       -d "{\"msgtype\": \"markdown\", \"markdown\": {
              \"title\":\"sonar代码质量\",
              \"text\": \"### flinkx代码扫描失败 \"
             }
           }"
  fi
fi

exit $exit_code
