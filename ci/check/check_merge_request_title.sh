#!/usr/bin/env bash

CURR_DIR=`pwd`

title=$CI_MERGE_REQUEST_TITLE
echo $title

result=`echo $title | grep -o -E '[\[](feat|hotfix|test|doc|conflict)([-][0-9]+)?]([\[[A-Za-z]+])?\s?.*'`
if [[ -z $result ]]; then
  msg="### Flinkx Warning \n\n> event：merge request title不符合规范 \n\n> merge request title: $title \n\n> merge request url: $CI_MERGE_REQUEST_PROJECT_URL/merge_requests/$CI_MERGE_REQUEST_IID"
  send_dingding "$msg"
  exit 1
fi
