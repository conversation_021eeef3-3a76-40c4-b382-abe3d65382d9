{"replace": {"reader": [{"plugin-key": "kerberosFileTimestamp", "replace-key": "kerberosFileTimestamp"}, {"plugin-key": "connection-password", "replace-key": "password"}, {"plugin-key": "connection-jdbcUrl", "replace-key": "jdbcUrl"}, {"plugin-key": "connection-schema", "replace-key": "schema"}, {"plugin-key": "openKerberos", "replace-key": "openKerberos"}, {"plugin-key": "hadoopConfig", "replace-key": "hadoopConfig", "is-nested": true}, {"plugin-key": "hadoopConf-kerberosConf", "replace-key": "kerberosConf", "is-nested": true}, {"plugin-key": "hadoopConfig-kerberosConfig-sftpConf", "replace-key": "sftpConf", "is-nested": true}, {"plugin-key": "hadoopConfig-sftpConf", "replace-key": "sftpConf", "is-nested": true}, {"plugin-key": "defaultFS", "replace-key": "defaultFS"}, {"plugin-key": "kerberosFile", "replace-key": "kerberosFile", "is-nested": true}, {"plugin-key": "hasHdfsConfig", "replace-key": "hasHdfsConfig"}, {"plugin-key": "kerberosConfig", "replace-key": "kerberosConfig", "is-nested": true}, {"plugin-key": "username", "replace-key": "username"}, {"plugin-key": "password", "replace-key": "password"}, {"plugin-key": "jdbcUrl", "replace-key": "jdbcUrl"}, {"plugin-key": "hiveMetastoreUris", "replace-key": "hiveMetastoreUris"}], "writer": [{"plugin-key": "kerberosFileTimestamp", "replace-key": "kerberosFileTimestamp"}, {"plugin-key": "connection-password", "replace-key": "password"}, {"plugin-key": "connection-jdbcUrl", "replace-key": "jdbcUrl"}, {"plugin-key": "connection-schema", "replace-key": "schema"}, {"plugin-key": "openKerberos", "replace-key": "openKerberos"}, {"plugin-key": "hadoopConfig", "replace-key": "hadoopConfig", "is-nested": true}, {"plugin-key": "hadoopConfig-kerberosConfig", "replace-key": "kerberosConfig", "is-nested": true}, {"plugin-key": "hadoopConfig-kerberosConfig-sftpConf", "replace-key": "sftpConf", "is-nested": true}, {"plugin-key": "hadoopConfig-sftpConf", "replace-key": "sftpConf", "is-nested": true}, {"plugin-key": "defaultFS", "replace-key": "defaultFS"}, {"plugin-key": "kerberosFile", "replace-key": "kerberosFile", "is-nested": true}, {"plugin-key": "hasHdfsConfig", "replace-key": "hasHdfsConfig"}, {"plugin-key": "kerberosConfig", "replace-key": "kerberosConfig", "is-nested": true}, {"plugin-key": "username", "replace-key": "username"}, {"plugin-key": "password", "replace-key": "password"}, {"plugin-key": "jdbcUrl", "replace-key": "jdbcUrl"}, {"plugin-key": "hiveMetastoreUris", "replace-key": "hiveMetastoreUris"}]}}