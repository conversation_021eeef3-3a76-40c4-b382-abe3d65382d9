package com.dtstack.chunjun.tools.replace;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.StringJoiner;

public class ReplaceMetaItem {

    @JsonProperty("plugin-key")
    private String pluginKey;

    @JsonProperty("replace-key")
    private String replaceKey;

    @JsonProperty("is-nested")
    private boolean isNested = false;

    @JsonProperty("merge")
    private boolean merge = false;

    public String getPluginKey() {
        return pluginKey;
    }

    public void setPluginKey(String pluginKey) {
        this.pluginKey = pluginKey;
    }

    public String getReplaceKey() {
        return replaceKey;
    }

    public void setReplaceKey(String replaceKey) {
        this.replaceKey = replaceKey;
    }

    public boolean match(String replaceKey) {
        return this.replaceKey.equals(replaceKey);
    }

    public boolean isNested() {
        return isNested;
    }

    public void setNested(boolean nested) {
        isNested = nested;
    }

    public boolean isMerge() {
        return merge;
    }

    public void setMerge(boolean merge) {
        this.merge = merge;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ReplaceMetaItem.class.getSimpleName() + "[", "]")
                .add("pluginKey='" + pluginKey + "'")
                .add("replaceKey='" + replaceKey + "'")
                .add("isNested=" + isNested)
                .toString();
    }
}
