package com.dtstack.chunjun.tools.replace;

import com.dtstack.chunjun.tools.common.Function;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ReplaceFunction implements Function {

    private static final Logger LOG = LoggerFactory.getLogger(ReplaceFunction.class);

    private static final ObjectMapper MAPPER = new ObjectMapper();

    private static final Pattern PATTERN = Pattern.compile("(.*)(reader|writer)");

    private static final String FILE_NAME = "mapping.json";

    private static final String FILE_SEPARATOR = File.separator;

    static {
        // FAIL_ON_UNKNOWN_PROPERTIES在序列化的时候，如果遇到不认识的字段的处理方式
        // 默认启用特性，这意味着在遇到未知属性时抛出JsonMappingException。在引入该特性之前，这是默认的默认设置。
        MAPPER.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        // FAIL_ON_EMPTY_BEANS决定了在没有找到类型的存取器时发生了什么（并且没有注释表明它是被序列化的）。如果启用（默认），
        // 将抛出一个异常来指明这些是非序列化类型;如果禁用了，它们将被序列化为空对象，即没有任何属性。
        // 请注意，这个特性只对那些没有任何识别注释的“空”bean产生影响（如@json序列化）：那些有注释的bean不会导致抛出异常。
        MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
    }

    // 1. 获取meta.json，然后读取meta.json的信息，转化为ReplaceMeta对象；
    // 2. 获取replace-item 里的信息，根据replace key 对content 内容进行替换
    // 3. 拆分 plugin-key，如果 plugin-key 是多层，那么逐层判断替换；
    @Override
    public String execute(String parameter) throws Exception {
        ReplaceResult result = new ReplaceResult();

        try {
            ReplaceParameter replaceParameter =
                    (ReplaceParameter) transform(parameter, ReplaceParameter.class);

            // 根据 plugin 类型找到对应的替换mapping 信息
            String pluginType = replaceParameter.getPluginType();
            List<ReplaceItem> replaceItems = replaceParameter.getReplaceItems();
            Matcher matcher = PATTERN.matcher(pluginType);
            String sourceType = null;
            String opType = null;
            while (matcher.find()) {
                sourceType = matcher.group(1);
                opType = matcher.group(2);
            }

            if (StringUtils.isBlank(sourceType) || StringUtils.isBlank(opType)) {
                throw new IllegalArgumentException(
                        "Get wrong plugin type of replace-parameter, value is: " + pluginType);
            }

            // 从resource 中读取对应的mapping信息
            String conf = readMeta(sourceType);
            ReplaceMeta meta = (ReplaceMeta) transform(conf, ReplaceMeta.class);
            List<ReplaceMetaItem> metaItems = meta.get(opType);

            // 从parameter 中读取对应的job content内容
            String content = replaceParameter.getContent();
            JsonNode node = MAPPER.readTree(content);

            JsonNode contentNode = node.get("job").get("content");
            if (contentNode.isArray()) {
                Iterator<JsonNode> elements = contentNode.elements();
                while (elements.hasNext()) {
                    JsonNode next = elements.next();
                    JsonNode op = next.get(opType);
                    if (Objects.nonNull(op)) {
                        final ObjectNode parameterNode = (ObjectNode) op.get("parameter");
                        replace(metaItems, replaceItems, parameterNode);
                        result.setResult(node.toString());
                    } else {
                        throw new IllegalArgumentException(" Can not get operation of " + opType);
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("Replace failed. Return original content.", e);
            result.setException(e);
            result.setResult(parameter);
        }

        return new String(MAPPER.writeValueAsBytes(result));
    }

    private void replace(
            final List<ReplaceMetaItem> metaItems,
            final List<ReplaceItem> replaceItems,
            final ObjectNode parameterNode)
            throws IOException {
        // 1. 通过 replace-key 找到对应的 plugin-key
        // 1.1 如果 plugin-key 是多层级的，如：connection.username，那么找到上层 connection，然后替换connection
        // 中的username，如果connection中没有username，那么就往上一层查询，替换；
        // 1.2 如果 plugin-key 只有一层，如：username，那么直接替换parameter层中的username

        for (ReplaceMetaItem metaItem : metaItems) {
            // 通过 replace-key 找到对应的 plugin-key
            for (ReplaceItem replaceItem : replaceItems) {
                // 找到对应的plugin-key
                if (metaItem.match(replaceItem.getReplaceKey())) {
                    String pluginKey = metaItem.getPluginKey();
                    boolean nested = metaItem.isNested();
                    boolean merge = metaItem.isMerge();
                    Object replaceVal = replaceItem.getReplaceVal();
                    String[] split = pluginKey.split("-");

                    if (split.length > 1) {
                        // 多层级
                        for (int i = 0; i < split.length; i++) {
                            String key = split[i];
                            if (i + 1 == split.length - 1) {
                                String finalKey = split[i + 1];
                                JsonNode jsonNode = parameterNode.get(key);

                                if (Objects.isNull(jsonNode)) {
                                    return;
                                }
                                // 判断是不是array node.
                                if (jsonNode.isArray()) {
                                    Iterator<JsonNode> elements = jsonNode.elements();
                                    while (elements.hasNext()) {
                                        ObjectNode next = (ObjectNode) elements.next();
                                        replaceNode(
                                                finalKey,
                                                replaceVal,
                                                nested,
                                                merge,
                                                next,
                                                parameterNode);
                                    }
                                } else {
                                    ObjectNode node = (ObjectNode) jsonNode;
                                    replaceNode(
                                            finalKey,
                                            replaceVal,
                                            nested,
                                            merge,
                                            node,
                                            parameterNode);
                                }
                            }
                        }
                    } else {
                        // 单层级
                        if (nested) {
                            JsonNode jsonNode = MAPPER.readTree(String.valueOf(replaceVal));
                            mergeNode(parameterNode, pluginKey, merge, jsonNode);
                        } else {
                            putDataIntoNode(parameterNode, pluginKey, replaceVal);
                        }
                    }
                    break;
                }
            }
        }
    }

    /**
     * 对于nested 节点进行遍历，如果是相同key，则替换value，否则不替换
     *
     * @param parameterNode 等待替换的node
     * @param pluginKey 替换的key
     * @param merge 是否merge
     * @param jsonNode 替换值的node
     */
    private void mergeNode(
            ObjectNode parameterNode, String pluginKey, boolean merge, JsonNode jsonNode) {
        if (merge) {
            ObjectNode mergeNode = (ObjectNode) parameterNode.get(pluginKey);
            jsonNode.fields()
                    .forEachRemaining(item -> mergeNode.replace(item.getKey(), item.getValue()));
        } else {
            parameterNode.replace(pluginKey, jsonNode);
        }
    }

    /**
     * 替换objNode 中的内容，如果objNode 中没有，那么判断parameterNode 中是否存在这个参数，有则替换，无则跳过
     *
     * @param key key.
     * @param value replace val.
     * @param objNode obj node.
     * @param parameterNode parameter node.
     */
    private void replaceNode(
            String key,
            Object value,
            boolean nested,
            boolean merge,
            ObjectNode objNode,
            ObjectNode parameterNode)
            throws IOException {
        if (Objects.nonNull(objNode) && Objects.nonNull(objNode.get(key))) {
            JsonNode replaceNode = objNode.get(key);
            if (replaceNode.isArray()) {
                ArrayNode arrayNode = JsonNodeFactory.instance.arrayNode();
                arrayNode.add(String.valueOf(value));
                objNode.replace(key, arrayNode);
            } else {
                if (nested) {
                    JsonNode jsonNode = MAPPER.readTree(String.valueOf(value));
                    if (Objects.isNull(jsonNode.get(key))) {
                        mergeNode(parameterNode, key, merge, jsonNode);
                    } else {
                        objNode.replace(key, jsonNode.get(key));
                    }
                } else {
                    putDataIntoNode(objNode, key, value);
                }
            }
        } else {
            if (Objects.nonNull(key)) {
                if (Objects.nonNull(parameterNode.get(key))) {

                    // 如果需要替换的节点里没有相对应的key，那么直接跳过返回。
                    if (Objects.isNull(objNode) || Objects.isNull(objNode.get(key))) {
                        return;
                    }

                    JsonNode jsonNode = MAPPER.readTree(String.valueOf(value));
                    if (Objects.nonNull(jsonNode)) {
                        if (Objects.isNull(jsonNode.get(key))) {
                            mergeNode(parameterNode, key, merge, jsonNode);
                        } else {
                            parameterNode.replace(key, jsonNode.get(key));
                        }
                    } else {
                        putDataIntoNode(parameterNode, key, value);
                    }
                }
            }
        }
    }

    /**
     * 将json 内容转化为对应的对象
     *
     * @param json json 内容
     * @param clazz 对应对象的class
     * @return 对象
     * @throws IOException 转化过程中的io exception.
     */
    private Object transform(String json, Class<?> clazz) throws IOException {
        JsonNode jsonNode = MAPPER.readTree(json);
        JsonNode replace = jsonNode.get("replace");
        return MAPPER.readValue(replace.toString(), clazz);
    }

    /**
     * 读取指定数据源的元数据信息
     *
     * @param type 数据源类型
     * @return 文件内容
     * @throws IOException 读取文件过程中的io exception.
     */
    private String readMeta(String type) throws IOException {
        // Creating an InputStream object
        try (InputStream inputStream =
                        Objects.requireNonNull(
                                Function.class
                                        .getClassLoader()
                                        .getResourceAsStream(type + FILE_SEPARATOR + FILE_NAME));
                // creating an InputStreamReader object
                InputStreamReader isReader = new InputStreamReader(inputStream);
                // Creating a BufferedReader object
                BufferedReader reader = new BufferedReader(isReader)) {
            StringBuilder sb = new StringBuilder();
            String str;
            while ((str = reader.readLine()) != null) {
                sb.append(str);
            }

            return sb.toString();
        }
    }

    private void putDataIntoNode(ObjectNode node, String key, Object obj) {
        String strObj = String.valueOf(obj);
        if (obj instanceof Boolean) {
            node.put(key, Boolean.parseBoolean(strObj));
        } else if (obj instanceof Integer) {
            node.put(key, Integer.parseInt(strObj));
        } else if (obj instanceof Double) {
            node.put(key, Double.parseDouble(strObj));
        } else if (obj instanceof Float) {
            node.put(key, Float.parseFloat(strObj));
        } else {
            node.put(key, strObj);
        }
    }
}
