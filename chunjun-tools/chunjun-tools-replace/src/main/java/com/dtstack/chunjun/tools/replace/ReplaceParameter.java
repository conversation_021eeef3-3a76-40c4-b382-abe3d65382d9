package com.dtstack.chunjun.tools.replace;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.StringJoiner;

public class ReplaceParameter {

    @JsonProperty("content")
    private String content;

    @JsonProperty("plugin-type")
    private String pluginType;

    @JsonProperty("replace-items")
    private List<ReplaceItem> replaceItems;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getPluginType() {
        return pluginType;
    }

    public void setPluginType(String pluginType) {
        this.pluginType = pluginType;
    }

    public List<ReplaceItem> getReplaceItems() {
        return replaceItems;
    }

    public void setReplaceItems(List<ReplaceItem> replaceItems) {
        this.replaceItems = replaceItems;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ReplaceParameter.class.getSimpleName() + "[", "]")
                .add("content='" + content + "'")
                .add("pluginType='" + pluginType + "'")
                .add("replaceItems=" + replaceItems)
                .toString();
    }
}
