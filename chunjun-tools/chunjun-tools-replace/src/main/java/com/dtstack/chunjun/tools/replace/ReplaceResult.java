package com.dtstack.chunjun.tools.replace;

import com.dtstack.chunjun.tools.common.Result;

import java.util.StringJoiner;

public class ReplaceResult implements Result {

    private String result;

    private Exception exception;

    @Override
    public void setResult(String result) {
        this.result = result;
    }

    @Override
    public String getResult() {
        return result;
    }

    @Override
    public Exception getException() {
        return exception;
    }

    @Override
    public void setException(Exception e) {
        this.exception = e;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ReplaceResult.class.getSimpleName() + "[", "]")
                .add("result='" + result + "'")
                .add("exception=" + exception)
                .toString();
    }
}
