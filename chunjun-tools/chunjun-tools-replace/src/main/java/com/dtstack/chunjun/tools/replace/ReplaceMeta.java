package com.dtstack.chunjun.tools.replace;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

public class ReplaceMeta {

    private List<ReplaceMetaItem> reader;

    private List<ReplaceMetaItem> writer;

    public List<ReplaceMetaItem> getReader() {
        return reader;
    }

    public void setReader(List<ReplaceMetaItem> reader) {
        this.reader = reader;
    }

    public List<ReplaceMetaItem> getWriter() {
        return writer;
    }

    public void setWriter(List<ReplaceMetaItem> writer) {
        this.writer = writer;
    }

    public List<ReplaceMetaItem> get(String opType) {
        sort();
        return "reader".equalsIgnoreCase(opType) ? getReader() : getWriter();
    }

    private void sort() {
        List<ReplaceMetaItem> sortedReader = sort(reader);
        setReader(sortedReader);

        List<ReplaceMetaItem> sortedWriter = sort(writer);
        setWriter(sortedWriter);
    }

    private List<ReplaceMetaItem> sort(List<ReplaceMetaItem> metaItems) {
        List<Integer> countNum = new ArrayList<>();
        Map<Integer, List<ReplaceMetaItem>> map = new HashMap<>();
        List<ReplaceMetaItem> sortedList = new LinkedList<>();
        for (ReplaceMetaItem metaItem : metaItems) {
            Integer countMatches = StringUtils.countMatches(metaItem.getPluginKey(), "-");
            if (countNum.contains(countMatches)) {
                List<ReplaceMetaItem> items = map.get(countMatches);
                items.add(metaItem);
            } else {
                countNum.add(countMatches);
                List<ReplaceMetaItem> list = new ArrayList<>();
                list.add(metaItem);
                map.put(countMatches, list);
            }
        }

        map.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEachOrdered(item -> sortedList.addAll(item.getValue()));

        return sortedList;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ReplaceMeta.class.getSimpleName() + "[", "]")
                .add("reader=" + reader)
                .add("writer=" + writer)
                .toString();
    }
}
