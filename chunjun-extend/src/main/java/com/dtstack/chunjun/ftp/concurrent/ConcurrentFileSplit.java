package com.dtstack.chunjun.ftp.concurrent;

import com.dtstack.chunjun.ftp.IFormatConfig;
import com.dtstack.chunjun.ftp.IFtpHandler;

import java.util.Comparator;
import java.util.List;

public interface ConcurrentFileSplit {

    /**
     * 切割文件， 生成 FtpFileSplit
     *
     * @param handler
     * @param config
     * @param files
     * @return
     */
    List<FtpFileSplit> buildFtpFileSplit(
            IFtpHandler handler, IFormatConfig config, List<String> files);

    /**
     * 断点续传时候需要遍历FtpFileSplit，去除已经读过的文件, 所以需要保证FtpFileSplit的顺序;
     *
     * @return
     */
    Comparator<FtpFileSplit> compare();
}
