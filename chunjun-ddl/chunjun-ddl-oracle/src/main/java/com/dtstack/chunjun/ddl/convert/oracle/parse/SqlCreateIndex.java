package com.dtstack.chunjun.ddl.convert.oracle.parse;

import com.dtstack.chunjun.cdc.ddl.definition.IndexOperator;
import com.dtstack.chunjun.cdc.ddl.definition.IndexType;

import org.apache.calcite.sql.SqlCall;
import org.apache.calcite.sql.SqlIdentifier;
import org.apache.calcite.sql.SqlKind;
import org.apache.calcite.sql.SqlNode;
import org.apache.calcite.sql.SqlOperator;
import org.apache.calcite.sql.SqlSpecialOperator;
import org.apache.calcite.sql.SqlWriter;
import org.apache.calcite.sql.parser.SqlParserPos;

import java.util.List;

public abstract class SqlCreateIndex extends SqlCall {

    private static final SqlOperator OPERATOR =
            new SqlSpecialOperator("CREATE_INDEX", SqlKind.CREATE_INDEX);
    protected IndexType indexType;
    protected SqlIdentifier indexIdentifier;

    public SqlCreateIndex(SqlParserPos pos, IndexType indexType, SqlIdentifier indexIdentifier) {
        super(pos);
        this.indexType = indexType;
        this.indexIdentifier = indexIdentifier;
    }

    @Override
    public SqlOperator getOperator() {
        return OPERATOR;
    }

    @Override
    public List<SqlNode> getOperandList() {
        return null;
    }

    @Override
    public void unparse(SqlWriter writer, int leftPrec, int rightPrec) {
        writer.keyword("CREATE");
        if (indexType != null) {
            writer.keyword(indexType.name());
        }
        writer.keyword("INDEX");
        indexIdentifier.unparse(writer, leftPrec, rightPrec);
        writer.keyword("ON");
    }

    public abstract IndexOperator parseToChunjunOperator();
}
