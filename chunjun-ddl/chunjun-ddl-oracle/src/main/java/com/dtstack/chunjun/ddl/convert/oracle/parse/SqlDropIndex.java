package com.dtstack.chunjun.ddl.convert.oracle.parse;

import com.dtstack.chunjun.cdc.EventType;
import com.dtstack.chunjun.cdc.ddl.definition.IndexDefinition;
import com.dtstack.chunjun.cdc.ddl.definition.IndexOperator;

import org.apache.calcite.sql.SqlCall;
import org.apache.calcite.sql.SqlIdentifier;
import org.apache.calcite.sql.SqlKind;
import org.apache.calcite.sql.SqlNode;
import org.apache.calcite.sql.SqlOperator;
import org.apache.calcite.sql.SqlSpecialOperator;
import org.apache.calcite.sql.SqlWriter;
import org.apache.calcite.sql.dialect.OracleSqlDialect;
import org.apache.calcite.sql.parser.SqlParserPos;

import java.util.List;

import static com.dtstack.chunjun.ddl.parse.util.SqlNodeUtil.getSqlString;

public class SqlDropIndex extends SqlCall {

    private static final SqlOperator OPERATOR =
            new SqlSpecialOperator("DROP_INDEX", SqlKind.DROP_INDEX);

    private final SqlIdentifier indexIdentifier;

    public SqlDropIndex(SqlParserPos pos, SqlIdentifier indexIdentifier) {
        super(pos);
        this.indexIdentifier = indexIdentifier;
    }

    @Override
    public void unparse(SqlWriter writer, int leftPrec, int rightPrec) {
        writer.keyword("DROP INDEX");
        indexIdentifier.unparse(writer, leftPrec, rightPrec);
    }

    @Override
    public SqlOperator getOperator() {
        return OPERATOR;
    }

    @Override
    public List<SqlNode> getOperandList() {
        return null;
    }

    public IndexOperator parseToChunjunOperator() {
        IndexDefinition indexDefinition =
                new IndexDefinition.Builder().indexName(getSqlString(indexIdentifier)).build();
        return new IndexOperator(
                EventType.DROP_INDEX,
                this.toSqlString(OracleSqlDialect.DEFAULT).getSql(),
                null,
                indexDefinition,
                null);
    }
}
