package com.dtstack.chunjun.ddl.convert.oracle.parse;

import com.dtstack.chunjun.cdc.ddl.definition.DdlOperator;

import org.apache.calcite.sql.SqlCall;
import org.apache.calcite.sql.SqlIdentifier;
import org.apache.calcite.sql.SqlKind;
import org.apache.calcite.sql.SqlNode;
import org.apache.calcite.sql.SqlOperator;
import org.apache.calcite.sql.SqlSpecialOperator;
import org.apache.calcite.sql.SqlWriter;
import org.apache.calcite.sql.parser.SqlParserPos;

import java.util.List;

public abstract class SqlAlterIndex extends SqlCall {

    private static final SqlOperator OPERATOR =
            new SqlSpecialOperator("ALTER_INDEX", SqlKind.ALTER_INDEX);

    protected final SqlIdentifier indexIdentifier;

    public SqlAlterIndex(SqlParserPos pos, SqlIdentifier indexIdentifier) {
        super(pos);
        this.indexIdentifier = indexIdentifier;
    }

    @Override
    public void unparse(SqlWriter writer, int leftPrec, int rightPrec) {
        writer.keyword("ALTER INDEX");
        indexIdentifier.unparse(writer, leftPrec, rightPrec);
    }

    @Override
    public SqlOperator getOperator() {
        return OPERATOR;
    }

    @Override
    public List<SqlNode> getOperandList() {
        return null;
    }

    public abstract DdlOperator parseToChunjunOperator();
}
