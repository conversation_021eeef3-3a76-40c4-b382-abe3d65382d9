package com.dtstack.chunjun.ddl.convert.oracle.parse;

import com.dtstack.chunjun.cdc.EventType;
import com.dtstack.chunjun.cdc.ddl.definition.ConstraintDefinition;
import com.dtstack.chunjun.cdc.ddl.definition.ConstraintOperator;
import com.dtstack.chunjun.cdc.ddl.definition.DdlOperator;

import org.apache.calcite.sql.SqlIdentifier;
import org.apache.calcite.sql.SqlWriter;
import org.apache.calcite.sql.dialect.OracleSqlDialect;
import org.apache.calcite.sql.parser.SqlParserPos;

import java.util.Collections;
import java.util.List;

import static com.dtstack.chunjun.ddl.parse.util.SqlNodeUtil.convertSqlIdentifierToTableIdentifier;
import static com.dtstack.chunjun.ddl.parse.util.SqlNodeUtil.getSqlString;

public class SqlRenameConstraint extends SqlAlterTable {

    private SqlIdentifier oldName;
    private SqlIdentifier newName;

    public SqlRenameConstraint(
            SqlParserPos pos,
            SqlIdentifier tableIdentifier,
            SqlIdentifier oldName,
            SqlIdentifier newName) {
        super(pos, tableIdentifier);
        this.oldName = oldName;
        this.newName = newName;
    }

    @Override
    public void unparse(SqlWriter writer, int leftPrec, int rightPrec) {
        super.unparse(writer, leftPrec, rightPrec);
        writer.keyword("RENAME CONSTRAINT");
        oldName.unparse(writer, leftPrec, rightPrec);
        writer.keyword("TO");
        newName.unparse(writer, leftPrec, rightPrec);
    }

    @Override
    public List<DdlOperator> parseToChunjunOperator() {
        ConstraintDefinition constraintDefinition =
                new ConstraintDefinition.Builder().name(getSqlString(oldName)).build();

        return Collections.singletonList(
                new ConstraintOperator.Builder()
                        .type(EventType.RENAME_CONSTRAINT)
                        .sql(this.toSqlString(OracleSqlDialect.DEFAULT).getSql())
                        .tableIdentifier(convertSqlIdentifierToTableIdentifier(tableIdentifier))
                        .constraintDefinition(constraintDefinition)
                        .newName(getSqlString(newName))
                        .build());
    }
}
