package com.dtstack.chunjun.ddl.convert.oracle;

import com.dtstack.chunjun.cdc.EventType;
import com.dtstack.chunjun.cdc.ddl.definition.DdlOperator;
import com.dtstack.chunjun.cdc.ddl.definition.IndexOperator;
import com.dtstack.chunjun.ddl.convert.oracle.parse.SqlDropIndex;

import org.apache.flink.api.java.tuple.Tuple2;

import org.apache.calcite.sql.SqlNode;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class DropIndexTest extends ParseTestBase {

    @Override
    protected List<DdlOperator> parseToChunjunOperator(SqlNode sqlNode) {
        SqlDropIndex sqlDropIndex = (SqlDropIndex) sqlNode;
        return Collections.singletonList(sqlDropIndex.parseToChunjunOperator());
    }

    @Override
    protected Tuple2<List<String>, List<DdlOperatorChecker>> getSqlAndChecker() {
        List<String> sqlList = new ArrayList<>();
        List<DdlOperatorChecker> checkerList = new ArrayList<>();

        String dropIndexSql = "drop index \"LIULIU\".\"DDL_CREATE_TEST3_PK_2\"";
        sqlList.add(dropIndexSql);
        checkerList.add(
                new DdlOperatorChecker() {
                    @Override
                    public void check(List<DdlOperator> ddlOperatorList) {
                        assert ddlOperatorList.size() == 1;
                        DdlOperator ddlOperator = ddlOperatorList.get(0);
                        assert ddlOperator.type == EventType.DROP_INDEX;
                        assert ddlOperator instanceof IndexOperator;
                        IndexOperator indexOperator = (IndexOperator) ddlOperator;
                        assert indexOperator
                                .getIndex()
                                .getIndexName()
                                .equals("LIULIU.DDL_CREATE_TEST3_PK_2");
                    }
                });

        return Tuple2.of(sqlList, checkerList);
    }
}
