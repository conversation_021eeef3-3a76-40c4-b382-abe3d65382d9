package com.dtstack.chunjun.ddl.convert.oracle;

import com.dtstack.chunjun.cdc.EventType;
import com.dtstack.chunjun.cdc.ddl.definition.ColumnOperator;
import com.dtstack.chunjun.cdc.ddl.definition.DdlOperator;
import com.dtstack.chunjun.cdc.ddl.definition.TableOperator;
import com.dtstack.chunjun.ddl.convert.oracle.parse.SqlComment;

import org.apache.flink.api.java.tuple.Tuple2;

import org.apache.calcite.sql.SqlNode;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class CommentTest extends ParseTestBase {

    @Override
    protected List<DdlOperator> parseToChunjunOperator(SqlNode sqlNode) {
        SqlComment sqlComment = (SqlComment) sqlNode;
        return Collections.singletonList(sqlComment.parseToChunjunOperator());
    }

    @Override
    protected Tuple2<List<String>, List<DdlOperatorChecker>> getSqlAndChecker() {
        List<String> sqlList = new ArrayList<>();
        List<DdlOperatorChecker> checkerList = new ArrayList<>();

        String columnCommonStatement = "COMMENT ON COLUMN DDL_TEST1.ID is 'asd_column'";
        sqlList.add(columnCommonStatement);
        checkerList.add(
                new DdlOperatorChecker() {
                    @Override
                    public void check(List<DdlOperator> ddlOperatorList) {
                        assert ddlOperatorList.size() == 1;
                        DdlOperator ddlOperator = ddlOperatorList.get(0);
                        assert ddlOperator instanceof ColumnOperator;
                        ColumnOperator columnOperator = (ColumnOperator) ddlOperator;
                        assert columnOperator.getType() == EventType.ALTER_COLUMN;
                        assert columnOperator.getTableIdentifier().getTable().equals("DDL_TEST1");
                        assert columnOperator.getColumns().get(0).getName().equals("ID");
                        assert columnOperator
                                .getColumns()
                                .get(0)
                                .getComment()
                                .equals("'asd_column'");
                    }
                });

        String tableCommonStatement = "COMMENT ON TABLE DDL_TEST1 is 'asd_table'";
        sqlList.add(tableCommonStatement);
        checkerList.add(
                new DdlOperatorChecker() {
                    @Override
                    public void check(List<DdlOperator> ddlOperatorList) {
                        assert ddlOperatorList.size() == 1;
                        DdlOperator ddlOperator = ddlOperatorList.get(0);
                        assert ddlOperator instanceof TableOperator;
                        TableOperator tableOperator = (TableOperator) ddlOperator;
                        assert tableOperator.getType() == EventType.ALTER_TABLE_COMMENT;
                        assert tableOperator
                                .getTableDefinition()
                                .getTableIdentifier()
                                .getTable()
                                .equals("DDL_TEST1");
                        assert tableOperator
                                .getTableDefinition()
                                .getComment()
                                .equals("'asd_table'");
                    }
                });
        return Tuple2.of(sqlList, checkerList);
    }
}
