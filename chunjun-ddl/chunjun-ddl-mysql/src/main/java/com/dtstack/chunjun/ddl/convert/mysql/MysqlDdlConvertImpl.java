/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.ddl.convert.mysql;

import com.dtstack.chunjun.cdc.DdlRowData;
import com.dtstack.chunjun.cdc.ddl.DdlConvert;
import com.dtstack.chunjun.cdc.ddl.definition.ColumnOperator;
import com.dtstack.chunjun.cdc.ddl.definition.DataBaseOperator;
import com.dtstack.chunjun.cdc.ddl.definition.DdlOperator;
import com.dtstack.chunjun.cdc.ddl.definition.TableIdentifier;
import com.dtstack.chunjun.cdc.ddl.definition.TableOperator;
import com.dtstack.chunjun.ddl.convert.mysql.parse.impl.FlinkxMySqlParserImpl;
import com.dtstack.chunjun.ddl.parse.util.SqlNodeUtil;
import com.dtstack.chunjun.mapping.MappingConf;
import com.dtstack.chunjun.mapping.MappingRule;
import com.dtstack.chunjun.throwable.ConvertException;

import org.apache.calcite.config.Lex;
import org.apache.calcite.sql.SqlNode;
import org.apache.calcite.sql.dialect.MysqlSqlDialect;
import org.apache.calcite.sql.parser.SqlParser;
import org.apache.calcite.sql.util.SqlVisitor;
import org.apache.calcite.sql.validate.SqlConformanceEnum;

import java.util.Collections;
import java.util.List;

public class MysqlDdlConvertImpl implements DdlConvert {

    private final SqlVisitor<List<DdlOperator>> mysqlVisitor =
            new SqlNodeConvertVisitor(new SqlNodeParseImpl());

    private final OperatorConvert operatorConvert;
    private SqlNodeReplaceVisitor sqlNodeReplaceVisitor;

    public MysqlDdlConvertImpl() {
        this(null);
    }

    public MysqlDdlConvertImpl(MappingConf mappingConf) {
        MappingRule mappingRule = null;
        if (null != mappingConf) {
            mappingRule = new MappingRule(mappingConf);
            this.sqlNodeReplaceVisitor = new SqlNodeReplaceVisitor(mappingRule);
        }
        this.operatorConvert = new OperatorConvertImpl(mappingRule);
    }

    @Override
    public List<DdlOperator> rowConvertToDdlData(DdlRowData row) throws ConvertException {
        try {
            SqlParser parser = SqlParser.create(row.getSql(), getConfig());
            SqlNode sqlNode = parser.parseStmt();
            return sqlNode.accept(mysqlVisitor);
        } catch (Exception e) {
            throw new ConvertException(row.getSql(), e);
        }
    }

    @Override
    public List<String> ddlDataConvertToSql(
            DdlOperator ddlOperator, TableIdentifier tableIdentifier) throws ConvertException {
        try {
            if (ddlOperator instanceof TableOperator) {
                return Collections.singletonList(
                        operatorConvert.convertOperateTable(
                                (TableOperator) ddlOperator, tableIdentifier));
            } else if (ddlOperator instanceof DataBaseOperator) {
                return Collections.singletonList(
                        operatorConvert.convertOperateDataBase((DataBaseOperator) ddlOperator));
            } else if (ddlOperator instanceof ColumnOperator) {
                return Collections.singletonList(
                        (operatorConvert.convertOperateColumn((ColumnOperator) ddlOperator)));
            }
        } catch (Exception t) {
            throw new ConvertException(ddlOperator.getSql(), t);
        }
        throw new ConvertException(
                ddlOperator.getSql(),
                new RuntimeException("not support convert" + ddlOperator.sql));
    }

    @Override
    public String getDataSourceType() {
        return "mysql";
    }

    @Override
    public List<String> map(DdlRowData value) {

        if (this.sqlNodeReplaceVisitor == null) {
            return Collections.singletonList(value.getSql());
        }

        try {
            sqlNodeReplaceVisitor.setCurrentTableIdentifier(value.getTableIdentifier());
            SqlParser parser = SqlParser.create(value.getSql(), getConfig());
            SqlNode sqlNode = parser.parseStmt();
            sqlNode.accept(sqlNodeReplaceVisitor);
            return Collections.singletonList(
                    SqlNodeUtil.getSqlString(sqlNode, MysqlSqlDialect.DEFAULT));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public SqlParser.Config getConfig() {
        SqlParser.Config mysqlConfig =
                SqlParser.config()
                        // 定义解析工厂
                        .withParserFactory(FlinkxMySqlParserImpl.FACTORY)
                        .withConformance(SqlConformanceEnum.MYSQL_5)
                        .withLex(Lex.MYSQL);
        return mysqlConfig;
    }
}
