# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to you under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This file is an FMPP (http://fmpp.sourceforge.net/) configuration file to
# allow clients to extend Calcite's SQL parser to support application specific
# SQL statements, literals or data types.
#
# Calcite's parser grammar file (Parser.jj) is written in javacc
# (https://javacc.org/) with Freemarker (http://freemarker.org/) variables
# to allow clients to:
#   1. have custom parser implementation class and package name.
#   2. insert new parser method implementations written in javacc to parse
#      custom:
#      a) SQL statements.
#      b) literals.
#      c) data types.
#   3. add new keywords to support custom SQL constructs added as part of (2).
#   4. add import statements needed by inserted custom parser implementations.
#
# Parser template file (Parser.jj) along with this file are packaged as
# part of the calcite-core-<version>.jar under "codegen" directory.

data: {
  parser: {
            # Generated parser implementation package and class name.
            package: "com.dtstack.chunjun.ddl.convert.mysql.parse.impl",
            class: "FlinkxMySqlParserImpl",

            # List of additional classes and packages to import.
            # Example. "org.apache.calcite.sql.*", "java.util.List".
            # Please keep the import classes in alphabetical order if new class is added.
            imports: [
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlCreateTable"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlCreateIndex"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlCreateDataBase"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlMysqlConstraintEnable"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlCheckConstraint"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlTableConstraint"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlGeneralColumn"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.TableCreationContext"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlIndex"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlKey"
              "com.dtstack.chunjun.ddl.parse.SqlTableOption"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.MysqlSqlTableOption"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlTableSpace"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.KeyPart"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlIndexOption"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlTruncateTable"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlDropView"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlRenameTable"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlRenameTableSingleton"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlIndexType"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTable"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTableAddIndex"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTableAddConstraint"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTableDrop"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTableChangeColumn"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTableSpace"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTableForce"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTableValidation"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTableCharacterAndCollate"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTableAbleKeys"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterEvent"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterFunction"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterLogFileGroup"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterProcedure"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlProcedureCharacteristic"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterServer"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterInstance"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlServerOption"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlDataBaseOption"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTableOperator"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTableLock"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTableOrderBy"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTableOptions"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTableAddColumn"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTableIndex"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTableAlgorithm"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTableComment"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTableConstraint"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTableRename"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterTableAlterColumn"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlDropTrigger"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlAlterDataBase"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlDropServer"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlDropProcedure"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlDropFunction"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlDropTableSpace"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlDropTable"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlDropIndex"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlDropDataBase"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlDropSchema"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlDropEvent"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlDropLogfileGroup"
               "com.dtstack.chunjun.ddl.convert.mysql.parse.SqlDropSpatialSystem"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.ReferenceDefinition"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.enums.IndexStorageType"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.enums.AlterTableTableSpaceEnum"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.enums.MatchType"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.enums.OrderEnum"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.enums.ServerOptionEnum"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.enums.AlterTableRenameTypeEnum"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.enums.ReferenceOptionEnums"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.enums.StoredTypeEnum"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.enums.OrderEnums"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.enums.SqlConstraintEnforcement"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.enums.ReferenceSituationEnum"
              "com.dtstack.chunjun.cdc.ddl.definition.IndexType"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.enums.SqlConstraintSpec"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.enums.AlterTableTargetTypeEnum"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.enums.AlterTableColumnOperatorTypeEnum"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.enums.AlterTableAlgorithmType"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.enums.AlterInstanceActionEnum"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.enums.AlterTableLockTypeEnum"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.enums.MysqlTableProperty"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.enums.StorageTypeEnum"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.enums.MysqlDataBaseProperty"
              "org.apache.calcite.sql.SqlCreate"
              "org.apache.calcite.sql.SqlCollation"
              "org.apache.calcite.sql.SqlCharStringLiteral"
              "org.apache.calcite.sql.SqlStringLiteral"
              "org.apache.calcite.sql.SqlIdentifier"
              "org.apache.calcite.sql.SqlDataTypeSpec"
              "org.apache.calcite.sql.SqlLiteral"
              "org.apache.calcite.sql.SqlNode"
              "org.apache.calcite.sql.SqlNodeList"
              "org.apache.calcite.sql.SqlDrop"
              "java.util.List"
              "java.util.ArrayList"
              "org.apache.commons.collections.CollectionUtils"
              "com.dtstack.chunjun.ddl.parse.type.SqlNumberTypeNameSpec"
              "com.dtstack.chunjun.ddl.parse.type.SqlCustomTypeNameSpec"
              "com.dtstack.chunjun.ddl.convert.mysql.parse.type.SqlSetTypeNameSpec"
              "com.dtstack.chunjun.ddl.convert.mysql.type.MysqlType"
            ]

            # List of new keywords. Example: "DATABASES", "TABLES". If the keyword is not a reserved
            # keyword, please also add it to 'nonReservedKeywords' section.
            # Please keep the keyword in alphabetical order if new keyword is added.
            keywords: [
            "TINYTEXT"
            "MEDIUMTEXT"
            "LONGTEXT"
            "ENUM"
            "GEOMCOLLECTION"
            "TEXT"
            "MEDIUMBLOB"
            "LONGBLOB"
            "TINYBLOB"
            "MULTIPOLYGON"
            "MULTILINESTRING"
            "MULTIPOINT"
            "POINT"
            "LINESTRING"
            "DATETIME"
            "UNSIGNED"
            "ZEROFILL"
            "MEDIUMINT"
            "COPY"
            "INPLACE"
            "FIXED"
            "HOST"
            "SOCKET"
            "OWNER"
            "PORT"
            "UNDOFILE"
            "WAIT"
            "INITIAL_SIZE"
            "INNODB"
            "REDO_LOG"
            "ROTATE"
            "MASTER"
            "BINLOG"
            "KEYRING"
            "CHANNEL"
            "RELOAD"
            "TLS"
            "EVENT"
            "SCHEDULE"
            "COMPLETION"
            "DO"
            "SLAVE"
            "LOGFILE"
            "REFERENCE"
            "UNDO"
            "VALIDATION"
            "MODIFY"
            "LOCK"
            "SHARED"
            "EXCLUSIVE"
            "DISABLE"
            "KEYS"
            "ENABLE"
            "DISCARD"
            "TABLESPACE"
            "FORCE"
              "BYTES"
              "CATALOGS"
              "COMMENT"
              "DATABASES"
              "ENFORCED"
              "EXTENDED"
              "FUNCTIONS"
              "IF"
              "METADATA"
              "OVERWRITE"
              "OVERWRITING"
              "PARTITIONED"
              "PARTITIONS"
              "PYTHON"
              "RAW"
              "RENAME"
              "SCALA"
              "STRING"
              "TABLES"
              "USE"
              "VIEWS"
              "VIRTUAL"
              "INDEX"
            ]

            # List of keywords from "keywords" section that are not reserved.
            nonReservedKeywords: [
             "TINYTEXT"
             "MEDIUMTEXT"
             "LONGTEXT"
             "ENUM"
             "TEXT"
             "MEDIUMBLOB"
             "LONGBLOB"
             "TINYBLOB"
             "GEOMCOLLECTION"
             "MULTIPOLYGON"
             "MULTILINESTRING"
             "MULTIPOINT"
             "POINT"
             "LINESTRING"
             "DATETIME"
             "UNSIGNED"
            "ZEROFILL"
            "MEDIUMINT"
            "COPY"
            "INPLACE"
            "DYNAMIC"
            "FIXED"
            "HOST"
            "SOCKET"
            "OWNER"
            "PORT"
            "UNDOFILE"
            "WAIT"
            "INITIAL_SIZE"
            "INNODB"
            "REDO_LOG"
            "ROTATE"
            "MASTER"
            "BINLOG"
            "KEYRING"
            "CHANNEL"
            "RELOAD"
            "TLS"
            "SCHEDULE"
            "COMPLETION"
            "DO"
            "SLAVE"
            "EVENT"
            "LOGFILE"
            "REFERENCE"
            "UNDO"
            "VALIDATION"
            "MODIFY"
            "LOCK"
            "NONE"
            "SHARED"
            "EXCLUSIVE"
            "DISABLE"
            "KEYS"
            "ENABLE"
            "DISK"
            "DISCARD"
            "IMPORT"
            "TABLESPACE"
             "FORCE"
             "INDEX"
              "A"
              "ABSENT"
              "ABSOLUTE"
              "ACTION"
              "ADA"
              "ADD"
              "ADMIN"
              "AFTER"
              "ALWAYS"
              "APPLY"
              "ASC"
              "ASSERTION"
              "ASSIGNMENT"
              "ATTRIBUTE"
              "ATTRIBUTES"
              "BEFORE"
              "BERNOULLI"
              "BREADTH"
              "C"
              "CASCADE"
              "CATALOG"
              "CATALOG_NAME"
              "CENTURY"
              "CHAIN"
              "CHARACTERISTICS"
              "CHARACTERS"
              "CHARACTER_SET_CATALOG"
              "CHARACTER_SET_NAME"
              "CHARACTER_SET_SCHEMA"
              "CLASS_ORIGIN"
              "COBOL"
              "COLLATION"
              "COLLATION_CATALOG"
              "COLLATION_NAME"
              "COLLATION_SCHEMA"
              "COLUMN_NAME"
              "COMMAND_FUNCTION"
              "COMMAND_FUNCTION_CODE"
              "COMMITTED"
              "CONDITIONAL"
              "CONDITION_NUMBER"
              "CONNECTION"
              "CONNECTION_NAME"
              "CONSTRAINT_CATALOG"
              "CONSTRAINT_NAME"
              "CONSTRAINTS"
              "CONSTRAINT_SCHEMA"
              "CONSTRUCTOR"
              "CONTINUE"
              "CURSOR_NAME"
              "DATA"
              "DATABASE"
              "DATETIME_INTERVAL_CODE"
              "DATETIME_INTERVAL_PRECISION"
              "DAYS"
              "DECADE"
              "DEFAULTS"
              "DEFERRABLE"
              "DEFERRED"
              "DEFINED"
              "DEFINER"
              "DEGREE"
              "DEPTH"
              "DERIVED"
              "DESC"
              "DESCRIPTION"
              "DESCRIPTOR"
              "DIAGNOSTICS"
              "DISPATCH"
              "DOMAIN"
              "DOW"
              "DOY"
              "DYNAMIC_FUNCTION"
              "DYNAMIC_FUNCTION_CODE"
              "ENCODING"
              "EPOCH"
              "ERROR"
              "EXCEPTION"
              "EXCLUDE"
              "EXCLUDING"
              "FINAL"
              "FIRST"
              "FOLLOWING"
              "FORMAT"
              "FORTRAN"
              "FOUND"
              "FRAC_SECOND"
              "G"
              "GENERAL"
              "GENERATED"
              "GEOMETRY"
              "GO"
              "GOTO"
              "GRANTED"
              "HIERARCHY"
              "HOP"
              "HOURS"
              "IGNORE"
              "IMMEDIATE"
              "IMMEDIATELY"
              "IMPLEMENTATION"
              "INCLUDING"
              "INCREMENT"
              "INITIALLY"
              "INPUT"
              "INSTANCE"
              "INSTANTIABLE"
              "INVOKER"
              "ISODOW"
              "ISOLATION"
              "ISOYEAR"
              "JAVA"
              "JSON"
              "K"
              "KEY"
              "KEY_MEMBER"
              "KEY_TYPE"
              "LABEL"
              "LAST"
              "LENGTH"
              "LEVEL"
              "LIBRARY"
              "LOCATOR"
              "M"
              "MAP"
              "MATCHED"
              "MAXVALUE"
              "MESSAGE_LENGTH"
              "MESSAGE_OCTET_LENGTH"
              "MESSAGE_TEXT"
              "MICROSECOND"
              "MILLENNIUM"
              "MILLISECOND"
              "MINUTES"
              "MINVALUE"
              "MONTHS"
              "MORE_"
              "MUMPS"
              "NAME"
              "NAMES"
              "NANOSECOND"
              "NESTING"
              "NORMALIZED"
              "NULLABLE"
              "NULLS"
              "NUMBER"
              "OBJECT"
              "OCTETS"
              "OPTION"
              "OPTIONS"
              "ORDERING"
              "ORDINALITY"
              "OTHERS"
              "OUTPUT"
              "OVERRIDING"
              "PAD"
              "PARAMETER_MODE"
              "PARAMETER_NAME"
              "PARAMETER_ORDINAL_POSITION"
              "PARAMETER_SPECIFIC_CATALOG"
              "PARAMETER_SPECIFIC_NAME"
              "PARAMETER_SPECIFIC_SCHEMA"
              "PARTIAL"
              "PASCAL"
              "PASSING"
              "PASSTHROUGH"
              "PAST"
              "PATH"
              "PLACING"
              "PLAN"
              "PLI"
              "PRECEDING"
              "PRESERVE"
              "PRIOR"
              "PRIVILEGES"
              "PUBLIC"
              "PYTHON"
              "QUARTER"
              "READ"
              "RELATIVE"
              "REPEATABLE"
              "REPLACE"
              "RESPECT"
              "RESTART"
              "RESTRICT"
              "RETURNED_CARDINALITY"
              "RETURNED_LENGTH"
              "RETURNED_OCTET_LENGTH"
              "RETURNED_SQLSTATE"
              "RETURNING"
              "ROLE"
              "ROUTINE"
              "ROUTINE_CATALOG"
              "ROUTINE_NAME"
              "ROUTINE_SCHEMA"
              "ROW_COUNT"
              "SCALAR"
              "SCALE"
              "SCHEMA"
              "SCHEMA_NAME"
              "SCOPE_CATALOGS"
              "SCOPE_NAME"
              "SCOPE_SCHEMA"
              "SECONDS"
              "SECTION"
              "SECURITY"
              "SELF"
              "SEQUENCE"
              "SERIALIZABLE"
              "SERVER"
              "SERVER_NAME"
              "SESSION"
              "SETS"
              "SIMPLE"
              "SIZE"
              "SOURCE"
              "SPACE"
              "SPECIFIC_NAME"
              "SQL_BIGINT"
              "SQL_BINARY"
              "SQL_BIT"
              "SQL_BLOB"
              "SQL_BOOLEAN"
              "SQL_CHAR"
              "SQL_CLOB"
              "SQL_DATE"
              "SQL_DECIMAL"
              "SQL_DOUBLE"
              "SQL_FLOAT"
              "SQL_INTEGER"
              "SQL_INTERVAL_DAY"
              "SQL_INTERVAL_DAY_TO_HOUR"
              "SQL_INTERVAL_DAY_TO_MINUTE"
              "SQL_INTERVAL_DAY_TO_SECOND"
              "SQL_INTERVAL_HOUR"
              "SQL_INTERVAL_HOUR_TO_MINUTE"
              "SQL_INTERVAL_HOUR_TO_SECOND"
              "SQL_INTERVAL_MINUTE"
              "SQL_INTERVAL_MINUTE_TO_SECOND"
              "SQL_INTERVAL_MONTH"
              "SQL_INTERVAL_SECOND"
              "SQL_INTERVAL_YEAR"
              "SQL_INTERVAL_YEAR_TO_MONTH"
              "SQL_LONGVARBINARY"
              "SQL_LONGVARCHAR"
              "SQL_LONGVARNCHAR"
              "SQL_NCHAR"
              "SQL_NCLOB"
              "SQL_NUMERIC"
              "SQL_NVARCHAR"
              "SQL_REAL"
              "SQL_SMALLINT"
              "SQL_TIME"
              "SQL_TIMESTAMP"
              "SQL_TINYINT"
              "SQL_TSI_DAY"
              "SQL_TSI_FRAC_SECOND"
              "SQL_TSI_HOUR"
              "SQL_TSI_MICROSECOND"
              "SQL_TSI_MINUTE"
              "SQL_TSI_MONTH"
              "SQL_TSI_QUARTER"
              "SQL_TSI_SECOND"
              "SQL_TSI_WEEK"
              "SQL_TSI_YEAR"
              "SQL_VARBINARY"
              "SQL_VARCHAR"
              "STATE"
              "STATEMENT"
              "STRUCTURE"
              "STYLE"
              "SUBCLASS_ORIGIN"
              "SUBSTITUTE"
              "TABLE_NAME"
              "TEMPORARY"
              "TIES"
              "TIMESTAMPADD"
              "TIMESTAMPDIFF"
              "TOP_LEVEL_COUNT"
              "TRANSACTION"
              "TRANSACTIONS_ACTIVE"
              "TRANSACTIONS_COMMITTED"
              "TRANSACTIONS_ROLLED_BACK"
              "TRANSFORM"
              "TRANSFORMS"
              "TRIGGER_CATALOG"
              "TRIGGER_NAME"
              "TRIGGER_SCHEMA"
              "TUMBLE"
              "TYPE"
              "UNBOUNDED"
              "UNCOMMITTED"
              "UNCONDITIONAL"
              "UNDER"
              "UNNAMED"
              "USAGE"
              "USER_DEFINED_TYPE_CATALOG"
              "USER_DEFINED_TYPE_CODE"
              "USER_DEFINED_TYPE_NAME"
              "USER_DEFINED_TYPE_SCHEMA"
              "UTF16"
              "UTF32"
              "UTF8"
              "VERSION"
              "VIEW"
              "WEEK"
              "WORK"
              "WRAPPER"
              "WRITE"
              "XML"
              "YEARS"
              "ZONE"
            ]

            # List of non-reserved keywords to add;
            # items in this list become non-reserved.
            # Please keep the keyword in alphabetical order if new keyword is added.
            nonReservedKeywordsToAdd: [
              # not in core, added in Flink
              "ENFORCED"
              "IF"
              "METADATA"
              "OVERWRITE"
              "OVERWRITING"
              "PARTITIONED"
              "PARTITIONS"
              "VIRTUAL"
            ]

            # List of non-reserved keywords to remove;
            # items in this list become reserved
            nonReservedKeywordsToRemove: [
            ]

            # List of methods for parsing custom SQL statements.
            # Return type of method implementation should be 'SqlNode'.
            # Example: SqlShowDatabases(), SqlShowTables().
            statementParserMethods: [
                "SqlAlterTable()"
                "TruncateTable()"
                "RenameTable()"
                "SqlAlterDataBase()"
                "SqlAlterEvent()"
                "SqlAlterFunction()"
                "SqlAlterInstance()"
                "SqlAlterLogFileGroup()"
                "SqlAlterProcedure()"
                "SqlAlterServer()"
            ]

            # List of methods for parsing custom literals.
            # Return type of method implementation should be "SqlNode".
            # Example: ParseJsonLiteral().
            literalParserMethods: [
            ]

            # List of methods for parsing ddl supported data types.
            # Return type of method implementation should be "SqlTypeNameSpec".
            # Example: SqlParseTimeStampZ().
            dataTypeParserMethods: [
             "SqlTypeNameNumberUnsigned()"
             "SqlMediumintTypeNameSpecParse()"
             "SqlSetTypeNameSpecParse()"
            ]

            # List of methods for parsing builtin function calls.
            # Return type of method implementation should be "SqlNode".
            # Example: DateFunctionCall().
            builtinFunctionCallMethods: [
            ]

            # List of methods for parsing extensions to "ALTER <scope>" calls.
            # Each must accept arguments "(SqlParserPos pos, String scope)".
            # Example: "SqlUploadJarNode"
            alterStatementParserMethods: [
            ]

            # List of methods for parsing extensions to "CREATE [OR REPLACE]" calls.
            # Each must accept arguments "(SqlParserPos pos, boolean replace)".
            createStatementParserMethods: [
              "SqlCreateTable"
              "SqlCreateDatabase"
              "SqlCreateIndex"
            ]

            # List of methods for parsing extensions to "DROP" calls.
            # Each must accept arguments "(Span s)".
            dropStatementParserMethods: [
            "SqlDropView"
            "SqlDropTriggerOrServerOrFunctionOrEventOrDataBase"
            "SqlDropTableSpace"
            "SqlDropTable"
            "SqlDropSpatialSystem"
            "SqlDropLogfileGroup"
            "SqlDropIndex"
            ]

            # Binary operators tokens
            binaryOperatorsTokens: [
            ]

            # Binary operators initialization
            extraBinaryExpressions: [
            ]

            # List of files in @includes directory that have parser method
            # implementations for parsing custom SQL statements, literals or types
            # given as part of "statementParserMethods", "literalParserMethods" or
            # "dataTypeParserMethods".
            implementationFiles: [
              "parserImpls.ftl"
              "textParseUtil.ftl"
              "typeParse.ftl"
            ]

            # List of additional join types. Each is a method with no arguments.
            # Example: LeftSemiJoin()
            joinTypes: [
            ]

            includePosixOperators: false
            includeCompoundIdentifier: true
            includeBraces: true
            includeAdditionalDeclarations: false
          }

}

freemarkerLinks: {
  includes: includes/
}
