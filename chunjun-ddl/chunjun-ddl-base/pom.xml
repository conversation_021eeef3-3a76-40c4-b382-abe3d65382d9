<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>chunjun-ddl</artifactId>
		<groupId>com.dtstack.chunjun</groupId>
		<version>2.0-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>chunjun-ddl-base</artifactId>

	<name>ChunJun : DDL : BASE</name>
	<properties>
		<maven.compiler.source>${target.java.version}</maven.compiler.source>
		<maven.compiler.target>${target.java.version}</maven.compiler.target>
	</properties>

	<dependencies>
		<!-- flink sql -->
<!--		<dependency>-->
<!--			<groupId>org.apache.flink</groupId>-->
<!--			<artifactId>flink-table-planner-blink_${scala.binary.version}</artifactId>-->
<!--			<version>${flink.version}</version>-->
<!--			<exclusions>-->
<!--				<exclusion>-->
<!--					<artifactId>slf4j-api</artifactId>-->
<!--					<groupId>org.slf4j</groupId>-->
<!--				</exclusion>-->
<!--			</exclusions>-->
<!--		</dependency>-->
		<!-- flink sql -->
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>29.0-jre</version>
		</dependency>

		<dependency>
			<groupId>org.apache.calcite</groupId>
			<artifactId>calcite-core</artifactId>
			<version>1.26.0</version>
		</dependency>

		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
			<version>3.2.2</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>1.7.25</version>
		</dependency>

	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<artifactSet>
								<excludes>
									<exclude>org.slf4j:slf4j-api</exclude>
									<exclude>log4j:log4j</exclude>
									<exclude>ch.qos.logback:*</exclude>
								</excludes>
							</artifactSet>
							<filters>
								<filter>
									<artifact>*:*</artifact>
									<excludes>
										<exclude>META-INF/*.SF</exclude>
										<exclude>META-INF/*.DSA</exclude>
										<exclude>META-INF/*.RSA</exclude>
									</excludes>
								</filter>
							</filters>
							<relocations>
								<relocation>
									<pattern>com.google.common</pattern>
									<shadedPattern>shade.ddl.base.com.google.common</shadedPattern>
								</relocation>
								<relocation>
									<pattern>com.google.thirdparty</pattern>
									<shadedPattern>shade.ddl.base.com.google.thirdparty</shadedPattern>
								</relocation>
								<relocation>
									<pattern>io.netty.netty-all</pattern>
									<shadedPattern>shade.ddl.base.io.netty.netty-all</shadedPattern>
								</relocation>
							</relocations>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-resources</id>
						<!-- here the phase you need -->
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<tasks>
								<copy todir="${basedir}/../../${dist.dir}/ddl-plugins"
									  file="${basedir}/target/${project.artifactId}-${project.version}.jar"/>
								<move file="${basedir}/../../${dist.dir}/ddl-plugins/${project.artifactId}-${project.version}.jar"
									  tofile="${basedir}/../../${dist.dir}/ddl-plugins/${project.artifactId}-${git.branch}.jar"/>
								<delete>
									<fileset dir="${basedir}/../../${dist.dir}/ddl-plugins"
											 includes="${project.artifactId}-*.jar"
											 excludes="${project.artifactId}-${git.branch}.jar"/>
								</delete>
							</tasks>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>



</project>
