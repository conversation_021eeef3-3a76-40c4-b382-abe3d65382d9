package com.dtstack.chunjun.ddl.parse.util;

import com.dtstack.chunjun.cdc.ddl.definition.ColumnDefinition;
import com.dtstack.chunjun.cdc.ddl.definition.ConstraintDefinition;
import com.dtstack.chunjun.cdc.ddl.definition.IndexDefinition;
import com.dtstack.chunjun.mapping.Casing;
import com.dtstack.chunjun.mapping.MappingRule;

import java.util.ArrayList;
import java.util.List;

public class SqlOperatorUtil {

    public static List<ColumnDefinition> mappingColumnDefinition(
            List<ColumnDefinition> columnDefinitionList, MappingRule mappingRule) {
        if (mappingRule != null
                && mappingRule.getCasing() != null
                && mappingRule.getCasing() != Casing.UNCHANGE
                && columnDefinitionList != null) {
            columnDefinitionList.forEach(
                    columnDefinition ->
                            columnDefinition.setName(
                                    mappingRule.casingName(columnDefinition.getName())));
        }
        return columnDefinitionList;
    }

    public static List<IndexDefinition> mappingIndexDefinition(
            List<IndexDefinition> indexDefinitionList, MappingRule mappingRule) {
        if (mappingRule != null
                && mappingRule.getCasing() != null
                && mappingRule.getCasing() != Casing.UNCHANGE
                && indexDefinitionList != null) {
            indexDefinitionList.forEach(
                    indexDefinition -> {
                        if (indexDefinition.getColumns() != null) {
                            indexDefinition
                                    .getColumns()
                                    .forEach(
                                            columnInfo ->
                                                    columnInfo.setName(
                                                            mappingRule.casingName(
                                                                    columnInfo.getName())));
                        }
                    });
        }
        return indexDefinitionList;
    }

    public static List<ConstraintDefinition> mappingConstraintDefinition(
            List<ConstraintDefinition> constraintDefinitionList, MappingRule mappingRule) {
        if (mappingRule != null
                && mappingRule.getCasing() != null
                && mappingRule.getCasing() != Casing.UNCHANGE
                && constraintDefinitionList != null) {
            constraintDefinitionList.forEach(
                    constraintDefinition -> {
                        if (constraintDefinition.getColumns() != null) {
                            List<String> columnList = new ArrayList<>();
                            constraintDefinition
                                    .getColumns()
                                    .forEach(
                                            column ->
                                                    columnList.add(mappingRule.casingName(column)));
                            constraintDefinition.setColumns(columnList);
                        }
                    });
        }
        return constraintDefinitionList;
    }
}
