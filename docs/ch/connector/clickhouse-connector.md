# ClickHouse Connector

FlinkSQL 提供了一个 ClickHouse connector 去支持从/向 ClickHouse 读取/写入数据。

## Requirements
要求的 Flink 的最低版本: 2.0-preview1

ClickHouse 插件支持处理 ClickHouse 的数据。

# How to Create an StarRocks Table
```sql
CREATE TABLE source
(
    id        int,
    name      STRING,
    PROCTIME AS PROCTIME()
) WITH (
      'connector' = 'clickhouse-x',
      'url' = '****************************************',
      'table-name' = 'student',
      'username' = 'default',
      'password' = 'dtstack123'
      );


CREATE TABLE lookup
(
    id            int,
    name   string
) with (
      'connector' = 'clickhouse-x',
      'url' = '****************************************',
      'table-name' = 'student_lookup',
      'username' = 'default',
      'password' = 'dtstack123',
      'lookup.cache-type' = 'lru'
      );


CREATE TABLE sink
(
    id            int,
    name   string
) with (
      'connector' = 'clickhouse-x',
      'url' = '****************************************',
      'table-name' = 'student_lookup',
      'username' = 'default',
      'password' = 'dtstack123'
      );

insert into sink
select a.id, b.name
from source a INNER JOIN lookup FOR SYSTEM_TIME AS OF a.PROCTIME AS b ON a.id = b.id;
```

## Supported Data Types
| Script Type                     | Flink DataType Type           |
|---------------------------------|-------------------------------|
| BOOLEAN                         | BOOLEAN                       |
| TINYINT                         | TINYINT                       |
| SHORT                           | SMALLINT                      |
| INT/INTEGER                     | INTEGER                       |
| BIGINT/LONG                     | LONG                          |
| FLOAT                           | FLOAT                         |
| DOUBLE                          | DOUBLE                        |
| CHAR/VARCHAR/STRING/CHARACTER   | VARCHAR                       |
| DATE                            | DATE                          |
| TIME                            | TIME_WITHOUT_TIME_ZONE        |
| --                              | TIMESTAMP_WITH_TIME_ZONE      |
| TIMESTAMP/DATETIME              | TIMESTAMP_WITHOUT_TIME_ZONE   |
| DECIMAL/BIGDECIMAL/NUMERIC      | DECIMAL                       |

## Parameter Descriptions

### Source Table Parameters
| Parameter Name           | Deprecated Key | Required | Default Value   | Type     | Description                                        |
|--------------------------|----------------|----------|-----------------|----------|----------------------------------------------------|
| url                      |                | Yes      | None            | String   | ClickHouse 的 jdbc url `clickhouse://<host>:<port>` |
| username                 |                | Yes      | None            | String   | ClickHouse 的用户名。                                   |
| password                 |                | Yes      | None            | String   | ClickHouse 的密码。                                    |
| database-name            |                | Yes      | default         | String   | ClickHouse 的数据库名， 默认值时 `default`.                  |
| default-database         |                | No       | None            | String   | ClickHouse 的默认数据库名                                 |
| table-name               |                | Yes      | None            | String   | ClickHouse 的表名                                     |


### Sink Table Parameters
| Parameter Name      | Deprecated Key | Required | Default Value | Type     | Description                                        |
|---------------------|----------------|----------|---------------|----------|----------------------------------------------------|
| url                 |                | Yes      | None          | String   | ClickHouse 的 jdbc url `clickhouse://<host>:<port>` |
| username            |                | Yes      | None          | String   | ClickHouse 的用户名。                                   |
| password            |                | Yes      | None          | String   | ClickHouse 的密码。                                    |
| database-name       |                | Yes      | default       | String   | ClickHouse 的数据库名， 默认值时 `default`.                  |
| default-database    |                | No       | None          | String   | ClickHouse 的默认数据库名                                 |
| table-name          |                | Yes      | None          | String   | ClickHouse 的表名                                     |
| sink.batch-size     |                | No       | 1000L         | Long     | 刷写的最大条数，默认值是 1000                                  |
| sink.flush-interval |                | No       | 1s            | Duration | 刷写的时间间隔，默认值是 1s                                    |
| sink.max-retries    |                | No       | 3             | Integer  | 数据库连接失败最大重试次数                                      |

### Lookup Table Parameters

| Parameter Name                          | Deprecated Key        | Required | Default Value | Type     | Description                                                                                                          |
|-----------------------------------------|-----------------------|----------|---------------|----------|----------------------------------------------------------------------------------------------------------------------|
| url                                     |                       | Yes      | None          | String   | ClickHouse 的 jdbc url `clickhouse://<host>:<port>`                                                                   |
| username                                |                       | Yes      | None          | String   | ClickHouse 的用户名。                                                                                                     |
| password                                |                       | Yes      | None          | String   | ClickHouse 的密码。                                                                                                      |
| database-name                           |                       | Yes      | default       | String   | ClickHouse 的数据库名， 默认值时 `default`.                                                                                    |
| default-database                        |                       | No       | None          | String   | ClickHouse 的默认数据库名                                                                                                   |
| table-name                              |                       | Yes      | None          | String   | ClickHouse 的表名                                                                                                       |
| lookup.cache                            | lookup.cache-type     | No       | `NONE`        | String   | 维表缓存类型，包含：`NONE`, `PARTIAL`, `FULL`，默认时 NONE。当使用废弃的 key：lookup.cache-type 时，与三个策略对应的 value 分别是：`NONE`, `LRU`, `ALL`  |
| lookup.partial-cache.expire-after-write | lookup.cache.ttl      | No       | 60 * 1000L ms | Duration | 当使用缓存时，缓存的过期时间。当使用废弃的 key：lookup.cache.ttl 时无需添加 ms 后缀                                                               |
| lookup.partial-cache.max-rows           | lookup.cache.max-rows | No       | 1000L         | Long     | 最大缓存数量                                                                                                               |

## FAQs
