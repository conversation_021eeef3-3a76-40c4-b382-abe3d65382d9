# StarRocks Connector

FlinkSQL 提供了一个 StarRocks connector 去支持从/向 StarRocks 读取/写入数据。

## Requirements
要求的 Flink 的最低版本: 2.0-preview1

StarRocks 插件支持处理 StarRocks 的数据。

# How to Create an StarRocks Table
```sql
CREATE TABLE source
(
    id            int,
    name   string,
    PROCTIME AS PROCTIME()
) with (
      'connector' = 'starrocks-x',
      'url' = '********************************',
      'feNodes' = '172.16.115.254:18030',
      'schema-name' = 'default',
      'table-name' = 'student',
      'username' = 'root',
      'password' = '',
      'scan.be.param.properties' = 'timeout:600000,timeout1:600200'
      );

CREATE TABLE lookup
(
    id            int,
    name   string
) with (
      'connector' = 'starrocks-x',
      'url' = '***************************',
      'feNodes' = '172.16.115.254:18030',
      'schema-name' = 'default',
      'table-name' = 'student',
      'username' = 'root',
      'password' = '',
      'scan.be.param.properties' = 'timeout:600000,timeout1:600200'
      );

CREATE TABLE sink
(
    id   int,
    name string


) with (
      'connector' = 'starrocks-x',
      'url' = '***************************',
      'feNodes' = '127.0.0.1:8030',
      'schema-name' = 'jier',
      'table-name' = 'student',
      'username' = 'root',
      'password' = ''
      );

insert into sink
select a.id, b.name
from source a left JOIN lookup FOR SYSTEM_TIME AS OF a.PROCTIME AS b ON a.id = b.id;
```

## Supported Data Types
| Script Type                     | Flink DataType Type          |
|---------------------------------|-----------------------------|
| BOOLEAN                         | BOOLEAN                     |
| TINYINT                         | TINYINT                     |
| SHORT                           | SMALLINT                    |
| INT/INTEGER                     | INTEGER                     |
| BIGINT/LONG                     | LONG                        |
| FLOAT                           | FLOAT                       |
| DOUBLE                          | DOUBLE                      |
| CHAR/VARCHAR/STRING/CHARACTER   | VARCHAR                     |
| DATE                            | DATE                        |
| TIME                            | TIME_WITHOUT_TIME_ZONE      |
| --                              | TIMESTAMP_WITH_TIME_ZONE    |
| TIMESTAMP/DATETIME              | TIMESTAMP_WITHOUT_TIME_ZONE |
| DECIMAL/BIGDECIMAL/NUMERIC      | DECIMAL                     |

## Parameter Descriptions

### Source Table Parameters
| Parameter Name              | Deprecated Key               | Required | Default Value   | Type    | Description                                                                |
|-----------------------------|------------------------------|----------|-----------------|---------|----------------------------------------------------------------------------|
| jdbc-url                    | url                          | Yes      | None            | String  | Stream Load 的 Host: `******************************,fe_ip2:query_port...`. |
| username                    |                              | Yes      | None            | String  | StarRocks 用户名                                                              |
| password                    |                              | Yes      | None            | String  | StarRocks 用户密码                                                             |
| database-name               | schema-name                  | Yes      | None            | String  | 数据库名                                                                       |
| table-name                  |                              | Yes      | None            | String  | 表名                                                                         |
| scan-url                    | feNodes                      | Yes      | None            | String  | fe 的 Host: `fe_ip1:http_port,fe_ip2:http_port...`                          |
| scan.filter                 | filterStatement              | No       | None            | String  | SQL 过滤条件                                                                   |
| scan.params.batch-rows      | scan.be.fetch-rows           | No       | 1000            | Integer | 批量读取数量                                                                     |
| scan.params.mem-limit-byte  | scan.be.fetch-bytes-limit    | No       | 1024*1024*1024L | Long    | 单次查下内存限制                                                                   |
| scan.params.query-timeout-s | scan.be.query.timeout-s      | No       | 600             | Integer | 单次查询超时时间                                                                   |
| scan.params.keep-alive-min  | scan.be.client.keep-live-min | No       | 10              | Integer | 客户端最小的 keep alive 时间                                                       |
| scan.connect.timeout-ms     | scan.be.client.timeout       | No       | 1000            | Integer | 客户端超时时间                                                                    |
| scan.params.properties      | scan.be.param.properties     | No       | None            | String  | 客户端附加参数                                                                    |


### Sink Table Parameters
| Parameter Name              | Deprecated Key               | Required | Default Value    | Type     | Description                                                                                                    |
|-----------------------------|------------------------------|----------|------------------|----------|----------------------------------------------------------------------------------------------------------------|
| jdbc-url                    | url                          | Yes      | None             | String   | Stream Load 的 Host: `******************************,fe_ip2:query_port...`.                                     |
| username                    |                              | Yes      | None             | String   | StarRocks 用户名                                                                                                  |
| password                    |                              | Yes      | None             | String   | StarRocks 用户密码                                                                                                 |
| database-name               | schema-name                  | Yes      | None             | String   | 数据库名                                                                                                           |
| table-name                  |                              | Yes      | None             | String   | 表名                                                                                                             |
| load-url                    | feNodes                      | Yes      | None             | String   | Stream Load 的 Url，如果不指定 http/https 前缀，则默认使用 http。比如：`fe_ip1:http_port;http://fe_ip2:http_port;https://fe_nlb`  |
| sink.semantic               | semantic                     | No       | AT_LEAST_ONCE    | String   | 写入一致性语义。可选 `at-least-once` 或者 `exactly-once`                                                                   |
| sink.buffer-flush.max-rows  | batchSize                    | No       | 500000L          | Long     | 刷写的最大条数                                                                                                        |

### Lookup Table Parameters

| Parameter Name                            | Deprecated Key               | Required   | Default Value | Type     | Description                                                                                                         |
|-------------------------------------------|------------------------------|------------|---------------|----------|---------------------------------------------------------------------------------------------------------------------|
| jdbc-url                                  | url                          | Yes        | None          | String   | Stream Load 的 Host: `******************************,fe_ip2:query_port...`.                                          |
| username                                  |                              | Yes        | None          | String   | StarRocks 用户名                                                                                                       |
| password                                  |                              | Yes        | None          | String   | StarRocks 用户密码                                                                                                      |
| database-name                             | schema-name                  | Yes        | None          | String   | 数据库名                                                                                                                |
| table-name                                |                              | Yes        | None          | String   | 表名                                                                                                                  |
| scan-url                                  | feNodes                      | Yes        | None          | String   | fe 的 Host: `fe_ip1:http_port,fe_ip2:http_port...`                                                                   |
| lookup.cache                              | lookup.cache-type            | No         | `NONE`        | String   | 维表缓存类型，包含：`NONE`, `PARTIAL`, `FULL`，默认时 NONE。当使用废弃的 key：lookup.cache-type 时，与三个策略对应的 value 分别是：`NONE`, `LRU`, `ALL` |
| lookup.partial-cache.expire-after-write   | lookup.cache.ttl             | No         | 60 * 1000L ms | Duration | 当使用缓存时，缓存的过期时间。当使用废弃的 key：lookup.cache.ttl 时无需添加 ms 后缀                                                              |
| lookup.partial-cache.max-rows             | lookup.cache.max-rows        | No         | 1000L         | Long     | 最大缓存数量                                                                                                              |
| scan.params.query-timeout-s               | scan.be.query.timeout-s      | No         | 600           | Integer  | 单次查询超时时间                                                                                                            |
| scan.params.keep-alive-min                | scan.be.client.keep-live-min | No         | 10            | Integer  | 客户端最小的 keep alive 时间                                                                                                |
| scan.connect.timeout-ms                   | scan.be.client.timeout       | No         | 1000          | Integer  | 客户端超时时间                                                                                                             |
| scan.params.properties                    | scan.be.param.properties     | No         | None          | String   | 客户端附加参数                                                                                                             |

## FAQs
