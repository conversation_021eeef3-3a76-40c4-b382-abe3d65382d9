# S3 Connector

FlinkSQL 提供了一个 S3 connector 去支持从/向 S3 读取/写入数据。

## Requirements
要求的 Flink 的最低版本: 2.0-preview1

S3 插件支持处理 S3 的数据。

# How to Create an S3 Table
```sql
CREATE TABLE source (
    name String
) WITH (
      'connector' = 's3-x',
      'path' = 's3a://daishu-csp-test01/test/qianyi/',
      's3.access-key' ='xxxxxxxxxx',
      's3.secret-key' = 'xxxxxxxxxx',
      's3.endpoint'= 'https://xxxxxxx',
      'format' = 'csv'
      );

CREATE TABLE sink (
    name String
) WITH (
      'connector' = 's3-x',
      'path' = 's3a://daishu-csp-test01/test/qianyi/',
      'format' = 'json',
      'sink.rolling-policy.rollover-interval' = '10s',
      'sink.rolling-policy.rollover-interval' = '30min',
      'auto-compaction' = 'true',
      's3.access-key' ='xxxxxxxxxx',
      's3.secret-key' = 'xxxxxxxxxx',
      's3.endpoint'= 'https://xxxxxxx'
      );

insert into sink select name from source

```

## Supported Data Types
| Script Type                     | Flink DataType Type           |
|---------------------------------|-------------------------------|
| BOOLEAN                         | BOOLEAN                       |
| TINYINT                         | TINYINT                       |
| SHORT                           | SMALLINT                      |
| INT/INTEGER                     | INTEGER                       |
| BIGINT/LONG                     | LONG                          |
| FLOAT                           | FLOAT                         |
| DOUBLE                          | DOUBLE                        |
| CHAR/VARCHAR/STRING/CHARACTER   | VARCHAR                       |
| DATE                            | DATE                          |
| TIME                            | TIME_WITHOUT_TIME_ZONE        |
| --                              | TIMESTAMP_WITH_TIME_ZONE      |
| TIMESTAMP/DATETIME              | TIMESTAMP_WITHOUT_TIME_ZONE   |
| DECIMAL/BIGDECIMAL/NUMERIC      | DECIMAL                       |

## Parameter Descriptions

### Source Table Parameters
| Parameter Name             | Deprecated Key | Required | Default Value   | Type     | Description           |
|----------------------------|----------------|----------|-----------------|----------|-----------------------|
| path                       |                | Yes      | None            | String   | path                  |
| format                     |                | Yes      | None            | String   | 用户名                   |
| s3.access-key              |                | Yes      | None            | String   | 密码                    |
| s3.secret-key              |                | Yes      | default         | String   | format                |
| s3.endpoint                |                | No       | None            | String   | endpoint              |


### Sink Table Parameters
| Parameter Name                        | Deprecated Key | Required | Default Value   | Type       | Description                                |
|---------------------------------------|----------------|----------|-----------------|------------|--------------------------------------------|
| path                                  |                | Yes      | None            | String     | path                                       |
| s3.access-key                         |                | Yes      | None            | String     | 用户名                                        |
| s3.secret-key                         |                | Yes      | None            | String     | 密码                                         |
| format                                |                | Yes      | default         | String     | format                                     |
| s3.endpoint                           |                | No       | None            | String     | endpoint                                   |
| sink.rolling-policy.file-size         |                | No       | 128MB           | MemorySize | 滚动前，part 文件最大大小                            |
| sink.rolling-policy.rollover-interval |                | NO       | 30 min          | Duration   | 滚动前，part 文件处于打开状态的最大时长（默认值30分钟，以避免产生大量小文件） |
| sink.rolling-policy.check-interval    |                | No       | 1 min           | Duration   | 基于时间的滚动策略的检查间隔                             |
| auto-compaction                       |                | No       | false           | Boolean    | 是否开启自动合并功能                                 |
| compaction.file-size                  |                | No       | None            | MemorySize | 合并目标文件大小，默认值为滚动文件大小。                       |


## FAQs
