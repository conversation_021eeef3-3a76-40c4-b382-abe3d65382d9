# MongoDB connector

FlinkSQL provides a MongoDB connector to provide read and write capabilities for MongoDB, which guarantees AT-LEAST-ONCE

## Required

supported flink version：2.0-preview1
MongoDB version：3.11.3以上

## 快速开始


```sql
-- build flink sql
-- build source，Here we use the datagen connector for data simulation
CREATE TABLE source
(
    val_date      DATE,
    val_double    DOUBLE,
    val_int       INT,
    val_long      BIGINT,
    val_timestamp TIMESTAMP,
    PROCTIME AS PROCTIME()
) WITH (
      'connector' = 'datagen',
      'number-of-rows' = '1000'
      );

--Build an MongoDB sink table to write data to MongoDB
CREATE TABLE sink
(
    val_date      DATE,
    val_double    DOUBLE,
    val_int       INT,
    val_long      BIGINT,
    val_timestamp TIMESTAMP
) WITH (
      'connector' = 'mongodb-x',
      'uri' = 'mongodb://**************:27017',
      'database' = 'flink_dev',
      'collection' = 'dim_int1'
      );

-- build logic of write.
insert into sink
SELECT val_date,val_double,val_int,val_long,val_timestamp
from source u;
```

## DataType Mapping

| MongoDB Bson类型 | FlinkSQL原生类型        |
|----------------|---------------------|
| ObjectId       | STRING			           |
| String         | STRING			           |
| Boolean        | BOOLEAN			          |
| Binary         | BINARY,VARBINARY    |
| Int32          | INTEGER			          |
| -              | TINYINT,SMALLINT,FLOAT		 |
| Int64          | BIGINT			             |
| Double         | DOUBLE			            |
| Decimal128     | DECIMAL			           |
| DateTime       | TIMESTAMP_LTZ(3)	    |
| Timestamp      | TIMESTAMP_LTZ(0)			        |
| Object         | ROW                        |
| Array          | ARRAY                        |

## MongoDB Source

MongoDB connector Source is used to pull data from MongoDB. The following example shows how to use:

Create Source Table for MongoDB:
```sql
CREATE TABLE source
(
    val_date      DATE,
    val_double    DOUBLE,
    val_int       INT,
    val_long      BIGINT,
    val_timestamp TIMESTAMP
    PROCTIME AS PROCTIME()
) WITH (
      'connector' = 'mongodb-x',
      'uri' = 'mongodb://**************:27017',
      'database' = 'flink_dev',
      'collection' = 'dim_int1'
      );
```

## MongoDB LookUp

MongoDB dimension tables provide the ability to query data from the database based on association keys. For the sake of query performance, the MongoDB plugin provides caching strategies:` NULL`,`PARTIAL`,  By default, the dimension table caching function is not enabled.
Currently, only synchronous search mode is supported.
By default, cache lookup is not enabled. You can enable it PARTIAL by setting it to lookup.cache.
Search cache is used to improve the temporal connection performance of MongoDB connectors. By default, lookup caching is not enabled, so all requests are sent to an external database. After enabling cache lookup, each process (i.e. TaskManager) will hold a cache.
Flink will first look up the cache and only send requests to external databases when the cache is missing, and update the cache with the returned rows. When the cache reaches the maximum number of cache lines lookup.partial-cache.max-rows or when the rows exceed or the specified maximum lifetime,
The oldest row in the cache will expire. The cached lines may not be the latest, and users can adjust the expiration option to a smaller value to obtain better fresh data, but this may increase the number of requests sent to the database.
So this is a balance between throughput and the correctness of lookup.partial-cache-expire-after-write.
By default, Flink caches empty query results for the primary key. You can switch the behavior of lookup.partial-cache.caching-missung-key by setting it to false

Demo for creating MongoDB Lookup table:
```sql
CREATE TABLE look_up
(
    val_date      DATE,
    val_double    DOUBLE,
    val_int       INT,
    val_long      BIGINT,
    val_timestamp TIMESTAMP
) WITH (
      'connector' = 'mongodb-x',
      'uri' = 'mongodb://**************:27017',
      'database' = 'flink_dev',
      'collection' = 'dim_int1',
      'lookup.cache' = 'NULL'
      );
```

## MongoDB Sink

The MongoDB plugin supports writing data from any source upstream of Flink. At present, the plugin provides semantic assurance for AT_LATEST-ONS and supports idempotent writing.

Demo for creating MongoDB Sink table:
```sql
CREATE TABLE sink
(
    val_date      DATE,
    val_double    DOUBLE,
    val_int       INT,
    val_long      BIGINT,
    val_timestamp TIMESTAMP
) WITH (
      'connector' = 'mongodb-x',
      'uri' = 'mongodb://**************:27017',
      'database' = 'flink_dev',
      'collection' = 'dim_int1',
      'sink.buffer-flush.interval' = '30000'
      );
```

## Properties

### General properties

| Name               | Required  | Default Value   | Type   | Description                                                      |
|--------------------|-----------|-----------------|--------|------------------------------------------------------------|
| connector          | 是         | （none）          | String | Specify what connector to use, here should be 'mongodb-x'. |
| uri                | 是         | （none）          | String | The MongoDB connection uri                                           |
| database           | 是         | （none）          | String | The name of MongoDB database to read or write.                                    |
| collection         | 是         | （none）          | String | The name of MongoDB collection to read or write.                                     |

### Properties of Source

| Name                       | Required | Default Value  | Type       | Description                   |
|----------------------------|----------|----------------|------------|-----------------------|
| scan.fetch-size            | 否        | 2048           | int        | Gives the reader a hint as to the number of documents that should be fetched from the database per round-trip when reading.          |
| scan.cursor.no-timeout     | 否        | true           | Boolean    | MongoDB server normally times out idle cursors after an inactivity period (10 minutes) to prevent excess memory use. Set this option to true to prevent that. However, if the application takes longer than 30 minutes to process the current batch of documents, the session is marked as expired and closed.。      |
| scan.partition.strategy    | 否        | default        | String     | Specifies the partition strategy. Available strategies are `single`, `sample`, `split-vector`, `sharded` and `default`. See the following Partitioned Scan section for more details.               |
| scan.partition.size        | 否        | 64mb           | MemorySize | Specifies the partition memory size.       |
| scan.partition.samples     | 否        | 10             | Integer    | Specifies the samples count per partition. It only takes effect when the partition strategy is sample. The sample partitioner samples the collection, projects and sorts by the partition fields. Then uses every `scan.partition.samples` as the value to use to calculate the partition boundaries. The total number of samples taken is calculated as: `samples per partition * (count of documents / number of documents per partition)`.                            |



### Properties of Lookup

| Name                                     | Required  | Default Value  | Type                               | Description                                    |
|------------------------------------------|-----------|----------------|------------------------------------|----------------------------------------------------|
| lookup.cache                             | 否         | NONE           | EnumPossible values: NONE, PARTIAL | The cache strategy for the lookup table. Currently supports NONE (no caching) and PARTIAL (caching entries on lookup operation in external database). |
| lookup.partial-cache.max-rows            | 否         | (none)         | Long                               | The max number of rows of lookup cache, over this value, the oldest rows will be expired. "lookup.cache" must be set to "PARTIAL" to use this option. See the following Lookup Cache section for more details.                                      |
| lookup.partial-cache.expire-after-write  | 否         | (none)         | Duration                           | The max time to live for each rows in lookup cache after writing into the cache. "lookup.cache" must be set to "PARTIAL" to use this option. See the following Lookup Cache section for more details.                                       |
| lookup.partial-cache.expire-after-access | 否         | (none)         | Duration                           | The max time to live for each rows in lookup cache after accessing the entry in the cache. "lookup.cache" must be set to "PARTIAL" to use this option. See the following Lookup Cache section for more details.                                          |
| lookup.partial-cache.caching-missing-key | 否         | true           | Boolean                            | Whether to store an empty value into the cache if the lookup key doesn't match any rows in the table. "lookup.cache" must be set to "PARTIAL" to use this option.                                              |
| lookup.max-retries                       | 否         | 3              | Integer                            | The max retry times if lookup database failed.                                                  |
| lookup.retry.interval                    | 否         | 1s             | Duration                           | Specifies the retry time interval if lookup records from database failed.                                                |

### Properties of Hbase Sink
| Name                       | Required | Default Value | Type                                     |  Description                                                                                                                                                                  |
|----------------------------|----------|---------------|------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| sink.buffer-flush.max-rows | 否        | 1000          | int                                      | Specifies the maximum number of buffered rows per batch request.                                                                                                              |
| sink.buffer-flush.interval | 否        | 1s            | Duration                                 | Specifies the batch flush interval.                                                                                                                                           |
| sink.max-retries           | 否        | 3             | Integer                                  | The max retry times if writing records to database failed.                                                                                                                    |
| sink.retry.interval        | 否        | 1s            | Duration                                 | Specifies the retry time interval if writing records to database failed.                                                                                                      |
| sink.parallelism           | 否        | (none)        | Integer                                  | Defines the parallelism of the MongoDB sink operator. By default, the parallelism is determined by the framework using the same parallelism of the upstream chained operator. |
| sink.delivery-guarantee    | 否        | at-lease-once | EnumPossible values: none, at-least-once | Optional delivery guarantee when committing. The exactly-once guarantee is not supported yet.                                                                                 |

