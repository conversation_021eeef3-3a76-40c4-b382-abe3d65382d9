# Kudu Connector

FlinkSQL provides a Kudu connector to provide read and write capabilities for Kudu, which guarantees AT-LEAST-ONCE

## Required

supported flink version：2.0-preview1
Kudu version：1.14.x以上

## QuickStart


```sql
-- build flink sql
-- build source，Here we use the datagen connector for data simulation
CREATE TABLE source
(
    byte_field   tinyint,
    short_field  smallint,
    int_field    int,
    long_field   bigint,
    binary_field binary,
    string_field string,
    bool_field   boolean,
    float_field  float,
    double_field double
) WITH (
      'connector' = 'datagen',
      'number-of-rows' = '1000'
      );

--Build an kudu sink table to write data to kudu
CREATE TABLE sink
(
    byte_field   tinyint,
    short_field  smallint,
    int_field    int,
    long_field   bigint,
    binary_field binary,
    string_field string,
    bool_field   boolean,
    float_field  float,
    double_field double,
    PROCTIME AS PROCTIME()
) WITH (
      'connector' = 'kudu-x'
      ,'kudu.masters' = '**************:7051'
      ,'kudu.table' = 'test'
      );

-- build logic of write.
insert into sink
SELECT byte_field,short_field,int_field,long_field,binary_field,string_field,bool_field,float_field,double_field
from source u;
```

## DataType Mapping
 
| FlinkSQL type | source type      |
|---------------|------------------|
| TINYINT       | int8			          |
| SMALLINT      | int16			         |
| INT           | int32			         |
| BIGINT        | int64			         |
| BINARY        | binary			        |
| STRING        | string			        |
| BOOLEAN       | bool			          |
| FLOAT         | float			         |
| DOUBLE        | double			        |
| TIMESTAMP     | unixtime_micros	 |
| DECIMAL       | decimal			       |

## Kudu Source

Kudu connector Source is used to pull data from Kudu. The following example shows how to use:

Create Source Table for Kudu:
```sql
CREATE TABLE source
(
    byte_field   tinyint,
    short_field  smallint,
    int_field    int,
    long_field   bigint,
    binary_field binary,
    string_field string,
    bool_field   boolean,
    float_field  float,
    double_field double,
    PROCTIME AS PROCTIME()
) WITH (
      'connector' = 'kudu-x'
      ,'kudu.masters' = '**************:7051'
      ,'kudu.table' = 'test'
      );
```

## Kudu LookUp

Kudu dimension tables provide the ability to query data from a database based on association keys. For the sake of query performance, the Kudu plugin provides a caching strategy:` FULL`,  By default, the dimension table caching function is not enabled .

- ` FULL `: It will cache all the data in the dimension table, which will improve the performance of dimension table association, but may occupy some resources.

NOTE:  Regardless of which caching strategy is enabled, it will affect the accuracy of data association to some extent. Users can adjust the associated window and cache refresh time according to their own business needs.

Demo for creating Kudu Lookup table:
```sql
CREATE TABLE look_up
(
    byte_field   tinyint,
    short_field  smallint,
    int_field    int,
    long_field   bigint,
    binary_field binary,
    string_field string,
    bool_field   boolean,
    float_field  float,
    double_field double
) WITH (
      'connector' = 'kudu-x'
      ,'kudu.masters' = '172.16.21.107:7051'
      ,'kudu.table' = 'testd'
      ,'lookup.cache' = 'FULL'
      );
```

## Kudu Sink

The Kudu plugin supports writing data from any source upstream of Flink. At present, the plugin provides semantic assurance for AT_LATEST-ONS and supports idempotent writing.
Simultaneously capable of processing upstream drawdown flow data.

Demo for creating Kudu Sink table:
```sql
CREATE TABLE sink
(
    byte_field   tinyint,
    short_field  smallint,
    int_field    int,
    long_field   bigint,
    binary_field binary,
    string_field string,
    bool_field   boolean,
    float_field  float,
    double_field double,
    PROCTIME AS PROCTIME()
) WITH (
      'connector' = 'kudu-x'
      ,'kudu.masters' = '**************:7051'
      ,'kudu.name' = 'test'
      );
```

## Properties

### General properties

| Name                       | Required | Default Value | Type | Description             |
|-------------------------------------------------|-----|-----------------|--------|-------------------------|
| connector                                       | 是   | （none）          | String | connector name          |
| kudu.master                                     | 是   | （none）          | String | localhost:7051          |
| kudu.table                                      | 是   | （none）          | String | 表名                      |
| kudu.client.worker-count                        | 否   | 2               | int    | kudu worker count       |
| kudu.client.default-operation-timeout-ms        | 否   | 30*1000（30秒）    | long   | kudu operation timeout  |
| kudu.client.default-admin-operation-timeout-ms  | 否   | 30*1000（30秒）    | long   | admin operation timeout |
| kudu.hash-partition-nums                        | 否   | 2*kudu.replicas | int    | parition                |
| kudu.replicas                                   | 否   | 3               | int    | replicas                     |

### Properties of Source

Reference General Parameters

### Properties of Lookup

| Name                       | Required | Default Value | Type | Description    |
|----------------------------------|------|---------| --- |----------------|
| kudu.scan-token.read-mode        | 否    | （none）  | String  | connector name |
| kudu.scan-token.batch-size-bytes | 否    | 1m      | String | max-size-bytes |
| kudu.scan-token.query-timeout    | 否    | 30000L  | long | query timeout  |

### Properties of Hbase Sink
| Name                       | Required | Default Value | Type | Description          |
|----------------------------| --- | --- | --- |----------------------|
| kudu.flush-interval   | 否 | 1000  | String  | flush interval       |
| kudu.max-buffer-size  | 否 | 1000 | int | flush count interval |
| kudu.ignore-not-found | 否 | false | boolean | Do you ignore rows that were not found|
| kudu.ignore-duplicate | 否 | false | boolean | Do you want to ignore duplicate lines  |

### to be continued：
The LRU of the maintenance table has not been developed yet。
