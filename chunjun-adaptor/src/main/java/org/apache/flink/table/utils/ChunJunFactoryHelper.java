/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.table.utils;

import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.configuration.ConfigOptions;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.factories.TableFactoryService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;
import java.net.URL;
import java.net.URLClassLoader;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class ChunJunFactoryHelper {
    /** shipfile需要的jar的classPath name */
    public static final ConfigOption<String> CLASS_FILE_NAME_FMT =
            ConfigOptions.key("class_file_name_fmt")
                    .stringType()
                    .defaultValue("class_path_%d")
                    .withDescription("");

    private static final Logger LOG = LoggerFactory.getLogger(TableFactoryService.class);

    /** 插件路径 */
    protected String localPluginPath = null;

    /** 远端插件路径 */
    protected String remotePluginPath = null;

    /** 插件加载类型 */
    protected String pluginLoadMode = ChunJunConstants.SHIP_FILE_PLUGIN_LOAD_MODE;

    /** 上下文环境 */
    protected StreamExecutionEnvironment env = null;

    /** shipfile需要的jar */
    protected List<URL> classPathSet = new ArrayList<>();

    /** shipfile需要的jar的classPath index */
    protected int classFileNameIndex = 0;

    /** 任务执行模式 */
    protected String executionMode;

    public ChunJunFactoryHelper() {}

    /**
     * register plugin jar file
     *
     * @param factoryIdentifier
     * @param classLoader
     * @param dirName
     */
    public void registerCachedFile(
            String factoryIdentifier, ClassLoader classLoader, String dirName) {
        registerCachedFile(factoryIdentifier, classLoader, dirName, false);
    }

    // public FlinkUserCodeClassLoader registerConnectorJarForChildFirstCacheClassloader(
    //        String factoryIdentifier, ChildFirstCacheClassLoader classLoader) {
    //    Set<URL> urlSet =
    //            ChunJunPluginUtil.getJarFileDirPath(
    //                    factoryIdentifier,
    //                    this.localPluginPath,
    //                    this.remotePluginPath,
    //                    ChunJunConstants.CONNECTOR_DIR_NAME);
    //
    //    // 如果classloader不是plugin级别的，需要重新构建一个
    //    if (!classLoader.isPluginClassLoader()) {
    //        Iterator<URL> iterator = urlSet.iterator();
    //        if (iterator.hasNext()) {
    //            URL next = iterator.next();
    //            return classLoader.getOrCreatePluginClassLoader(next.toString());
    //        } else {
    //            // 正常不会进入这个流程
    //            LOG.warn(
    //                    "not find plugin jar..., factoryIdentifier [ {} ] localPluginPath [ {} ]",
    //                    factoryIdentifier,
    //                    localPluginPath);
    //        }
    //    }
    //    return classLoader;
    // }

    /**
     * register plugin jar file
     *
     * @param factoryIdentifier
     * @param classLoader
     * @param dirName
     */
    public void registerCachedFile(
            String factoryIdentifier, ClassLoader classLoader, String dirName, boolean isAbsolute) {
        Set<URL> urlSet =
                ChunJunPluginUtil.getJarFileDirPath(
                        factoryIdentifier, this.localPluginPath, dirName, isAbsolute);
        ClassLoader realClassLoader = classLoader;
        // if (classLoader instanceof FlinkUserCodeClassLoaders.SafetyNetWrapperClassLoader) {
        //    realClassLoader =
        //            ((FlinkUserCodeClassLoaders.SafetyNetWrapperClassLoader) classLoader)
        //                    .ensureInner();
        // }

        try {
            Method add = URLClassLoader.class.getDeclaredMethod("addURL", URL.class);
            add.setAccessible(true);
            List<String> urlList = new ArrayList<>(urlSet.size());
            for (URL jarUrl : urlSet) {
                add.invoke(realClassLoader, jarUrl);
                if (!this.classPathSet.contains(jarUrl)) {
                    urlList.add(jarUrl.toString());
                    this.classPathSet.add(jarUrl);
                    String classFileName =
                            String.format(
                                    CLASS_FILE_NAME_FMT.defaultValue(), this.classFileNameIndex);
                    this.env.registerCachedFile(jarUrl.getPath(), classFileName, true);
                    this.classFileNameIndex++;
                }
            }
            ChunJunPluginUtil.setPipelineOptionsToEnvConfig(this.env, urlList, executionMode);
        } catch (Exception e) {
            LOG.warn("can't add jar in {} to cachedFile, e = {}", urlSet, e.getMessage());
        }
    }

    public void setLocalPluginPath(String localPluginPath) {
        this.localPluginPath = localPluginPath;
    }

    public void setRemotePluginPath(String remotePluginPath) {
        this.remotePluginPath = remotePluginPath;
    }

    public void setPluginLoadMode(String pluginLoadMode) {
        this.pluginLoadMode = pluginLoadMode;
    }

    public void setEnv(StreamExecutionEnvironment env) {
        this.env = env;
    }

    public String getExecutionMode() {
        return executionMode;
    }

    public void setExecutionMode(String executionMode) {
        this.executionMode = executionMode;
    }
}
