package org.apache.flink.runtime.metric;

import org.apache.flink.api.common.accumulators.Accumulator;
import org.apache.flink.api.common.accumulators.SimpleAccumulator;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.api.common.state.OperatorStateStore;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.Meter;
import org.apache.flink.metrics.View;
import org.apache.flink.runtime.state.StateInitializationContext;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import static org.apache.flink.runtime.metric.ObjectSizeCalculator.CalculatorType.OBJECT_SIZE_CALCULATOR;

public abstract class ChunjunMetric implements Serializable {
    public static final long serialVersionUID = 1L;
    protected static final Logger LOG = LoggerFactory.getLogger(ChunjunMetric.class);
    protected final transient RuntimeContext runtimeContext;

    private transient ListState<MetricState> unionMetricStates;
    private Map<Integer, MetricState> metricStateMap;
    protected MetricState metricState;
    protected transient volatile ObjectSizeCalculator objectSizeCalculator;
    private final String stateName;

    public ChunjunMetric(RuntimeContext runtimeContext, String stateName) {
        this.runtimeContext = runtimeContext;
        this.stateName = stateName;
    }

    public void initializeState(StateInitializationContext context) throws Exception {
        OperatorStateStore stateStore = context.getOperatorStateStore();
        LOG.info("Start initialize input format state, is restored:{}", context.isRestored());
        unionMetricStates =
                stateStore.getUnionListState(
                        new ListStateDescriptor<>(
                                stateName, TypeInformation.of(new TypeHint<MetricState>() {})));
        if (context.isRestored()) {
            metricStateMap = new HashMap<>(16);
            for (MetricState formatState : unionMetricStates.get()) {
                metricStateMap.put(formatState.getNumOfSubTask(), formatState);
                LOG.info("initializeState state info:{}", formatState);
            }

            int indexOfSubTask = runtimeContext.getTaskInfo().getIndexOfThisSubtask();
            metricState = metricStateMap.get(indexOfSubTask);
            if (metricState == null || metricState.getMetric() == null) {
                metricState = new MetricState(indexOfSubTask);
            }

            restoreMetric();
        } else {
            metricState = new MetricState(runtimeContext.getTaskInfo().getIndexOfThisSubtask());
        }
    }

    public void snapshotState() throws Exception {
        snapshotMetricState();
        unionMetricStates.clear();
        unionMetricStates.add(metricState);
    }

    protected abstract void snapshotMetricState() throws Exception;

    protected abstract void restoreMetric() throws Exception;

    public ObjectSizeCalculator getObjectSizeCalculator() {
        if (objectSizeCalculator == null) {
            synchronized (this) {
                if (objectSizeCalculator == null) {
                    objectSizeCalculator = rowSizeCalculator();
                }
            }
        }
        return objectSizeCalculator;
    }

    private ObjectSizeCalculator rowSizeCalculator() {
        Map<String, String> globalJobParameters = runtimeContext.getGlobalJobParameters();
        String type =
                globalJobParameters.getOrDefault(
                        "objectSizeCalculatorType", OBJECT_SIZE_CALCULATOR.getTypeName());
        return ObjectSizeCalculator.getObjectSizeCalculator(type);
    }

    public class SimpleCount implements Counter, Serializable {
        private static final long serialVersionUID = 1L;

        private long count;

        public SimpleCount() {}

        public void inc() {
            ++this.count;
        }

        public void inc(long n) {
            this.count += n;
        }

        public void dec() {
            --this.count;
        }

        public void dec(long n) {
            this.count -= n;
        }

        public long getCount() {
            return this.count;
        }
    }

    public class DurationCount implements Counter, Serializable {
        private static final long serialVersionUID = 1L;

        private long starTime;

        public DurationCount(long starTime) {
            this.starTime = starTime;
        }

        public DurationCount() {
            this.starTime = System.currentTimeMillis();
        }

        public void inc() {}

        public void inc(long n) {}

        public void dec() {}

        public void dec(long n) {}

        public long getCount() {
            return System.currentTimeMillis() - starTime;
        }

        public long getStarTime() {
            return starTime;
        }
    }

    public class SimpleLongCounterMeterView implements Meter, View {

        private static final int DEFAULT_TIME_SPAN_IN_SECONDS = 60;
        /** The underlying counter maintaining the count. */
        private final Counter counter;
        /** The time-span over which the average is calculated. */
        private final int timeSpanInSeconds;
        /** Circular array containing the history of values. */
        private final long[] values;
        /** The index in the array for the current time. */
        private int time = 0;
        /** The last rate we computed. */
        private double currentRate = 0;

        public SimpleLongCounterMeterView(Counter counter) {
            this(counter, DEFAULT_TIME_SPAN_IN_SECONDS);
        }

        public SimpleLongCounterMeterView(Counter counter, boolean isRestore) {
            this(counter, DEFAULT_TIME_SPAN_IN_SECONDS);
            // 从状态恢复后，要把 meter 全部初始化
            if (isRestore) {
                for (int time = 0; time < this.values.length; time++) {
                    values[time] = counter.getCount();
                }
            }
        }

        public SimpleLongCounterMeterView(Counter counter, int timeSpanInSeconds) {
            this.counter = counter;
            this.timeSpanInSeconds =
                    timeSpanInSeconds - (timeSpanInSeconds % UPDATE_INTERVAL_SECONDS);
            this.values = new long[this.timeSpanInSeconds / UPDATE_INTERVAL_SECONDS + 1];
        }

        @Override
        public void markEvent() {
            this.counter.inc(1);
        }

        @Override
        public void markEvent(long n) {
            this.counter.inc(n);
        }

        @Override
        public long getCount() {
            return counter.getCount();
        }

        @Override
        public double getRate() {
            return currentRate;
        }

        @Override
        public void update() {
            time = (time + 1) % values.length;
            values[time] = counter.getCount();
            currentRate =
                    ((double) (values[time] - values[(time + 1) % values.length])
                            / timeSpanInSeconds);
        }
    }

    public class CountWrapperAccumulator implements SimpleAccumulator<Long> {
        private static final long serialVersionUID = 1L;
        private Counter simpleCount;

        public CountWrapperAccumulator(Counter simpleCount) {
            this.simpleCount = simpleCount;
        }

        public void add(Long value) {
            this.simpleCount.inc(value);
        }

        public Long getLocalValue() {
            return this.simpleCount.getCount();
        }

        public void merge(Accumulator<Long, Long> other) {
            this.simpleCount.inc(other.getLocalValue());
        }

        public void resetLocal() {
            this.simpleCount.dec(simpleCount.getCount());
        }

        public CountWrapperAccumulator clone() {
            CountWrapperAccumulator result;
            if (this.simpleCount instanceof ChunjunMetric.DurationCount) {
                result =
                        new CountWrapperAccumulator(
                                new ChunjunMetric.DurationCount(
                                        ((ChunjunMetric.DurationCount) simpleCount).getStarTime()));
            } else {
                result = new CountWrapperAccumulator(new ChunjunMetric.SimpleCount());
                result.add(this.simpleCount.getCount());
            }
            return result;
        }

        public String toString() {
            return "CountWrapperAccumulator " + this.simpleCount.getCount();
        }
    }
}
