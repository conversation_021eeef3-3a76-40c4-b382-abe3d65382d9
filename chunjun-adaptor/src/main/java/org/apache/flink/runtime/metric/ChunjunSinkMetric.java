package org.apache.flink.runtime.metric;

import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.metrics.MetricGroup;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ChunjunSinkMetric extends ChunjunMetric {
    protected static final Logger LOG = LoggerFactory.getLogger(ChunjunSinkMetric.class);

    private final transient MetricGroup flinkxMetricGroup;
    private SimpleCount bytesWrite;
    private SimpleCount numWrite;
    private SimpleCount snapshotWrite;
    private DurationCount durationCounter;

    public ChunjunSinkMetric(RuntimeContext runtimeContext) {
        super(runtimeContext, "chunjun-metric-states");
        this.bytesWrite = new SimpleCount();
        this.numWrite = new SimpleCount();
        this.snapshotWrite = new SimpleCount();
        this.durationCounter = new DurationCount();

        MetricGroup operatorMetricGroup = runtimeContext.getMetricGroup();
        this.flinkxMetricGroup =
                operatorMetricGroup.addGroup(
                        Metrics.METRIC_GROUP_KEY_FLINKX, Metrics.METRIC_GROUP_VALUE_OUTPUT);
        registerMetric();
        registerAccumator();
    }

    private void registerMetric() {
        flinkxMetricGroup.counter(Metrics.NUM_WRITES, numWrite);
        flinkxMetricGroup.counter(Metrics.SNAPSHOT_WRITES, snapshotWrite);
        flinkxMetricGroup.counter(Metrics.WRITE_BYTES, bytesWrite);
        flinkxMetricGroup.counter(Metrics.WRITE_DURATION, durationCounter);

        flinkxMetricGroup.meter(
                Metrics.NUM_WRITES + Metrics.SUFFIX_RATE,
                new SimpleLongCounterMeterView(numWrite, true));

        flinkxMetricGroup.meter(
                Metrics.WRITE_BYTES + Metrics.SUFFIX_RATE,
                new SimpleLongCounterMeterView(bytesWrite, true));
    }

    private void registerAccumator() {
        if (runtimeContext.getAccumulator(Metrics.NUM_WRITES) == null) {
            runtimeContext.addAccumulator(
                    Metrics.NUM_WRITES, new CountWrapperAccumulator(numWrite));
        }

        if (runtimeContext.getAccumulator(Metrics.SNAPSHOT_WRITES) == null) {
            runtimeContext.addAccumulator(
                    Metrics.SNAPSHOT_WRITES, new CountWrapperAccumulator(snapshotWrite));
        }

        if (runtimeContext.getAccumulator(Metrics.WRITE_BYTES) == null) {
            runtimeContext.addAccumulator(
                    Metrics.WRITE_BYTES, new CountWrapperAccumulator(bytesWrite));
        }

        if (runtimeContext.getAccumulator(Metrics.WRITE_DURATION) == null) {
            runtimeContext.addAccumulator(
                    Metrics.WRITE_DURATION, new CountWrapperAccumulator(durationCounter));
        }
    }

    @Override
    protected void restoreMetric() {
        metricState
                .getMetricLongValue(Metrics.NUM_WRITES)
                .ifPresent(
                        i -> {
                            numWrite.inc(i);
                        });

        metricState
                .getMetricLongValue(Metrics.WRITE_BYTES)
                .ifPresent(
                        i -> {
                            bytesWrite.inc(i);
                        });
    }

    @Override
    public void snapshotMetricState() throws Exception {
        this.metricState.getMetric().put(Metrics.NUM_WRITES, numWrite.getCount());
        this.metricState.getMetric().put(Metrics.WRITE_BYTES, bytesWrite.getCount());
        // snapshot时重新更新snapshotWrite指标信息
        this.snapshotWrite.dec(snapshotWrite.getCount());
        LOG.info("snapshot sink state into:{}", metricState);
    }

    public void incWriteMetric(Object object) {
        long objectSize = getObjectSizeCalculator().getObjectSize(object);
        bytesWrite.inc(objectSize);
        numWrite.inc();
        snapshotWrite.inc();
    }
}
