package com.dtstack.chunjun.catalog.hms.catalog;

import com.dtstack.chunjun.enums.CatalogType;

import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.configuration.ConfigOptions;

public class CatalogOptions {
    public static final ConfigOption<String> DEFAULT_DATABASE =
            ConfigOptions.key("default-database").stringType().defaultValue("default");
    public static final ConfigOption<CatalogType> DEFAULT_CATALOG_TYPE =
            ConfigOptions.key("default-catalog-type")
                    .enumType(CatalogType.class)
                    .defaultValue(CatalogType.HIVE);
    public static final ConfigOption<String> HIVE_CONF_DIR =
            ConfigOptions.key("hive-conf-dir").stringType().noDefaultValue();
    public static final ConfigOption<String> HADOOP_CONF_DIR =
            ConfigOptions.key("hadoop-conf-dir").stringType().noDefaultValue();

    // HIVE
    public static final ConfigOption<String> HIVE_PROPERTY_VERSION =
            ConfigOptions.key("hive.property-version").stringType().defaultValue("1");

    public static final ConfigOption<String> HIVE_DEFAULT_DATABASE =
            ConfigOptions.key("hive.default-database").stringType().defaultValue("default");

    public static final ConfigOption<String> HIVE_HIVE_CONF_DIR =
            ConfigOptions.key("hive.hive-conf-dir").stringType().noDefaultValue();

    public static final ConfigOption<String> HIVE_HADOOP_CONF_DIR =
            ConfigOptions.key("hive.hadoop-conf-dir").stringType().noDefaultValue();

    // HUDI
    public static final ConfigOption<String> HUDI_PROPERTY_VERSION =
            ConfigOptions.key("hudi.property-version").stringType().defaultValue("1");

    public static final ConfigOption<String> HUDI_DEFAULT_DATABASE =
            ConfigOptions.key("hudi.default-database").stringType().defaultValue("default");

    public static final ConfigOption<String> HUDI_HIVE_CONF_DIR =
            ConfigOptions.key("hudi.hive.conf.dir").stringType().noDefaultValue();

    public static final ConfigOption<String> HUDI_HADOOP_CONF_DIR =
            ConfigOptions.key("hudi.hadoop.conf.dir").stringType().noDefaultValue();

    // ICEBERG
    public static final ConfigOption<String> ICEBERG_PROPERTY_VERSION =
            ConfigOptions.key("iceberg.property-version").stringType().defaultValue("1");

    public static final ConfigOption<String> ICEBERG_DEFAULT_DATABASE =
            ConfigOptions.key("iceberg.default-database").stringType().defaultValue("default");

    public static final ConfigOption<String> ICEBERG_HIVE_CONF_DIR =
            ConfigOptions.key("iceberg.hive-conf-dir").stringType().noDefaultValue();

    public static final ConfigOption<String> ICEBERG_HADOOP_CONF_DIR =
            ConfigOptions.key("iceberg.hadoop-conf-dir").stringType().noDefaultValue();

    // PAIMON
    public static final ConfigOption<String> PAIMON_PROPERTY_VERSION =
            ConfigOptions.key("paimon.property-version").stringType().defaultValue("1");

    public static final ConfigOption<String> PAIMON_DEFAULT_DATABASE =
            ConfigOptions.key("paimon.default-database").stringType().defaultValue("default");

    public static final ConfigOption<String> PAIMON_HIVE_CONF_DIR =
            ConfigOptions.key("paimon.hive-conf-dir").stringType().noDefaultValue();

    public static final ConfigOption<String> PAIMON_HADOOP_CONF_DIR =
            ConfigOptions.key("paimon.hadoop-conf-dir").stringType().noDefaultValue();
}
