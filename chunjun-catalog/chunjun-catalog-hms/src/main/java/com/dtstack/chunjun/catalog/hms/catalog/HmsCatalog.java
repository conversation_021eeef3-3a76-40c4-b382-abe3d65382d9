package com.dtstack.chunjun.catalog.hms.catalog;

import com.dtstack.chunjun.catalog.hms.catalog.factory.HmsDynamicTableFactory;
import com.dtstack.chunjun.catalog.hms.util.HmsUtil;
import com.dtstack.chunjun.common.throwble.FlinkxRuntimeException;
import com.dtstack.chunjun.enums.CatalogType;

import org.apache.flink.configuration.ReadableConfig;
import org.apache.flink.table.catalog.AbstractCatalog;
import org.apache.flink.table.catalog.Catalog;
import org.apache.flink.table.catalog.CatalogBaseTable;
import org.apache.flink.table.catalog.CatalogDatabase;
import org.apache.flink.table.catalog.CatalogFunction;
import org.apache.flink.table.catalog.CatalogPartition;
import org.apache.flink.table.catalog.CatalogPartitionSpec;
import org.apache.flink.table.catalog.ObjectPath;
import org.apache.flink.table.catalog.exceptions.CatalogException;
import org.apache.flink.table.catalog.exceptions.DatabaseAlreadyExistException;
import org.apache.flink.table.catalog.exceptions.DatabaseNotEmptyException;
import org.apache.flink.table.catalog.exceptions.DatabaseNotExistException;
import org.apache.flink.table.catalog.exceptions.FunctionAlreadyExistException;
import org.apache.flink.table.catalog.exceptions.FunctionNotExistException;
import org.apache.flink.table.catalog.exceptions.PartitionAlreadyExistsException;
import org.apache.flink.table.catalog.exceptions.PartitionNotExistException;
import org.apache.flink.table.catalog.exceptions.PartitionSpecInvalidException;
import org.apache.flink.table.catalog.exceptions.TableAlreadyExistException;
import org.apache.flink.table.catalog.exceptions.TableNotExistException;
import org.apache.flink.table.catalog.exceptions.TableNotPartitionedException;
import org.apache.flink.table.catalog.exceptions.TablePartitionedException;
import org.apache.flink.table.catalog.stats.CatalogColumnStatistics;
import org.apache.flink.table.catalog.stats.CatalogTableStatistics;
import org.apache.flink.table.expressions.Expression;
import org.apache.flink.table.factories.Factory;
import org.apache.flink.table.factories.FactoryUtil;
import org.apache.flink.util.Preconditions;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import static com.dtstack.chunjun.catalog.hms.catalog.CatalogOptions.DEFAULT_CATALOG_TYPE;
import static com.dtstack.chunjun.catalog.hms.util.CatalogCacheUtil.initialClassLoader;
import static com.dtstack.chunjun.catalog.hms.util.HmsUtil.classLoaderCallBack;
import static com.dtstack.chunjun.catalog.hms.util.HmsUtil.getConfByType;
import static org.apache.flink.configuration.CoreOptions.ALWAYS_PARENT_FIRST_LOADER_PATTERNS;

public class HmsCatalog extends AbstractCatalog {

    private static final String ALWAYS_PARENT_FIRST_PATTERN_KEY =
            "hms." + ALWAYS_PARENT_FIRST_LOADER_PATTERNS.key();

    // fix record
    // 1. hudi use hive-exec:2.3.1 -> remove org.apache.hadoop; add org.apache.tez.dag.api
    private static final String DEFAULT_ALWAYS_PARENT_FIRST_LOADER_PATTERNS =
            "java.;scala.;"
                    + "org.apache.flink.;"
                    + "com.esotericsoftware.kryo;"
                    + "javax.annotation.;"
                    + "org.slf4j;"
                    + "org.apache.log4j;"
                    + "org.apache.logging;"
                    + "org.apache.commons.logging;"
                    + "ch.qos.logback;"
                    + "org.xml;"
                    + "javax.xml;"
                    //                    + "org.apache.xerces;"
                    + "org.w3c;"
                    + "org.apache.tez.dag.api";

    private static final Map<CatalogType, ClassLoader> cacheClassLoader = new ConcurrentHashMap<>();
    private Map<String, Catalog> cacheCatalog;

    private final String catalogName;
    private final Map<String, String> properties;
    private final ReadableConfig configuration;

    private final CatalogType defaultCatalogType;
    private Method invokeCatalogTypeMethod;

    public HmsCatalog(
            String name,
            String defaultDatabase,
            Map<String, String> properties,
            ReadableConfig configuration) {
        super(name, defaultDatabase);
        this.catalogName = name;
        this.properties = properties;
        this.configuration = configuration;

        this.defaultCatalogType =
                CatalogType.fromConnectorName(
                        properties.getOrDefault(
                                DEFAULT_CATALOG_TYPE.key(),
                                DEFAULT_CATALOG_TYPE.defaultValue().getConnectorName()));
    }

    @Override
    public void open() throws CatalogException {
        this.cacheCatalog = new ConcurrentHashMap<>();
        try {
            invokeCatalogTypeMethod =
                    proxyCatalog(
                            CatalogType.HIVE,
                            catalog -> {
                                ClassLoader contextClassLoader =
                                        Thread.currentThread().getContextClassLoader();
                                Class<?> hiveCatalogClass =
                                        Class.forName(
                                                "org.apache.flink.table.catalog.hive.HiveCatalog",
                                                true,
                                                contextClassLoader);
                                Method getCatalogType =
                                        hiveCatalogClass.getDeclaredMethod(
                                                "getCatalogType", ObjectPath.class);
                                getCatalogType.setAccessible(true);
                                return getCatalogType;
                            });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void close() throws CatalogException {
        if (cacheCatalog != null) {
            cacheCatalog.forEach((k, v) -> v.close());
            cacheCatalog.clear();
        }
    }

    @Override
    public List<String> listDatabases() throws CatalogException {
        try {
            return proxyCatalog(CatalogType.HIVE, Catalog::listDatabases);
        } catch (Exception e) {
            if (e instanceof CatalogException) {
                throw (CatalogException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public CatalogDatabase getDatabase(String databaseName)
            throws DatabaseNotExistException, CatalogException {
        try {
            return proxyCatalog(CatalogType.HIVE, catalog -> catalog.getDatabase(databaseName));
        } catch (Exception e) {
            if (e instanceof CatalogException) {
                throw (CatalogException) e;
            } else if (e instanceof DatabaseNotExistException) {
                throw (DatabaseNotExistException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public boolean databaseExists(String databaseName) throws CatalogException {
        try {
            return proxyCatalog(CatalogType.HIVE, catalog -> catalog.databaseExists(databaseName));
        } catch (Exception e) {
            if (e instanceof CatalogException) {
                throw (CatalogException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public void createDatabase(String name, CatalogDatabase database, boolean ignoreIfExists)
            throws DatabaseAlreadyExistException, CatalogException {
        try {
            proxyCatalog(
                    CatalogType.HIVE,
                    catalog -> {
                        catalog.createDatabase(name, database, ignoreIfExists);
                        return null;
                    });
        } catch (Exception e) {
            if (e instanceof CatalogException) {
                throw (CatalogException) e;
            } else if (e instanceof DatabaseAlreadyExistException) {
                throw (DatabaseAlreadyExistException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public void dropDatabase(String name, boolean ignoreIfNotExists, boolean cascade)
            throws DatabaseNotExistException, DatabaseNotEmptyException, CatalogException {
        try {
            proxyCatalog(
                    CatalogType.HIVE,
                    catalog -> {
                        catalog.dropDatabase(name, ignoreIfNotExists, cascade);
                        return null;
                    });
        } catch (Exception e) {
            if (e instanceof CatalogException) {
                throw (CatalogException) e;
            } else if (e instanceof DatabaseNotExistException) {
                throw (DatabaseNotExistException) e;
            } else if (e instanceof DatabaseNotEmptyException) {
                throw (DatabaseNotEmptyException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public void alterDatabase(String name, CatalogDatabase newDatabase, boolean ignoreIfNotExists)
            throws DatabaseNotExistException, CatalogException {
        try {
            proxyCatalog(
                    CatalogType.HIVE,
                    catalog -> {
                        catalog.alterDatabase(name, newDatabase, ignoreIfNotExists);
                        return null;
                    });
        } catch (Exception e) {
            if (e instanceof CatalogException) {
                throw (CatalogException) e;
            } else if (e instanceof DatabaseNotExistException) {
                throw (DatabaseNotExistException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public List<String> listTables(String databaseName)
            throws DatabaseNotExistException, CatalogException {
        try {
            return proxyCatalog(CatalogType.HIVE, catalog -> catalog.listTables(databaseName));
        } catch (Exception e) {
            if (e instanceof CatalogException) {
                throw (CatalogException) e;
            } else if (e instanceof DatabaseNotExistException) {
                throw (DatabaseNotExistException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public List<String> listViews(String databaseName)
            throws DatabaseNotExistException, CatalogException {
        try {
            return proxyCatalog(CatalogType.HIVE, catalog -> catalog.listViews(databaseName));
        } catch (Exception e) {
            if (e instanceof CatalogException) {
                throw (CatalogException) e;
            } else if (e instanceof DatabaseNotExistException) {
                throw (DatabaseNotExistException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public CatalogBaseTable getTable(ObjectPath tablePath)
            throws CatalogException, TableNotExistException {
        try {
            CatalogType catalogType = invokeGetCatalogType(tablePath);
            return proxyCatalog(
                    catalogType,
                    catalog -> {
                        CatalogBaseTable table = catalog.getTable(tablePath).copy();
                        // DTStack userId
                        table.getOptions().remove("userId");
                        // HiveCatalog fields
                        table.getOptions().remove("isGeneric");
                        // add connector options for iceberg
                        if (!table.getOptions().containsKey("connector")
                                && catalogType.isAppendConnectorName()) {
                            table.getOptions().put("connector", catalogType.getConnectorName());
                        }
                        return table;
                    });
        } catch (Exception e) {
            if (e instanceof CatalogException) {
                throw (CatalogException) e;
            } else if (e instanceof TableNotExistException) {
                throw (TableNotExistException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public boolean tableExists(ObjectPath tablePath) throws CatalogException {
        try {
            return proxyCatalog(tablePath, catalog -> catalog.tableExists(tablePath));
        } catch (Exception e) {
            if (e instanceof CatalogException) {
                throw (CatalogException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public void dropTable(ObjectPath tablePath, boolean ignoreIfNotExists)
            throws CatalogException, TableNotExistException {
        try {
            proxyCatalog(
                    tablePath,
                    catalog -> {
                        catalog.dropTable(tablePath, ignoreIfNotExists);
                        return null;
                    });
        } catch (Exception e) {
            if (e instanceof CatalogException) {
                throw (CatalogException) e;
            } else if (e instanceof TableNotExistException) {
                throw (TableNotExistException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public void renameTable(ObjectPath tablePath, String newTableName, boolean ignoreIfNotExists)
            throws CatalogException, TableNotExistException, TableAlreadyExistException {
        try {
            proxyCatalog(
                    tablePath,
                    catalog -> {
                        catalog.renameTable(tablePath, newTableName, ignoreIfNotExists);
                        return null;
                    });
        } catch (Exception e) {
            if (e instanceof CatalogException) {
                throw (CatalogException) e;
            } else if (e instanceof TableNotExistException) {
                throw (TableNotExistException) e;
            } else if (e instanceof TableAlreadyExistException) {
                throw (TableAlreadyExistException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public void createTable(ObjectPath tablePath, CatalogBaseTable table, boolean ignoreIfExists)
            throws CatalogException, DatabaseNotExistException, TableAlreadyExistException {
        try {
            CatalogType catalogType =
                    CatalogType.fromFlinkTableWithDefault(table, defaultCatalogType);
            table.getOptions().computeIfAbsent("connector", k -> catalogType.getConnectorName());

            // icebergCatalog does not support connector=iceberg
            if (catalogType == CatalogType.ICEBERG) {
                table.getOptions().remove("connector");
            }
            proxyCatalog(
                    catalogType,
                    catalog -> {
                        catalog.createTable(tablePath, table, ignoreIfExists);
                        return null;
                    });
        } catch (Exception e) {
            if (e instanceof TableAlreadyExistException) {
                throw (TableAlreadyExistException) e;
            } else if (e instanceof DatabaseNotExistException) {
                throw (DatabaseNotExistException) e;
            } else if (e instanceof CatalogException) {
                throw (CatalogException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public void alterTable(
            ObjectPath tablePath, CatalogBaseTable newTable, boolean ignoreIfNotExists)
            throws CatalogException, TableNotExistException {
        // Verify that the catalog type is not modified
        CatalogBaseTable hiveTable;
        try {
            hiveTable = proxyCatalog(CatalogType.HIVE, catalog -> catalog.getTable(tablePath));
        } catch (Exception e) {
            if (e instanceof TableNotExistException) {
                if (ignoreIfNotExists) {
                    return;
                } else {
                    throw (TableNotExistException) e;
                }
            }
            throw new FlinkxRuntimeException(e);
        }
        CatalogType originCatalogType = CatalogType.fromFlinkTable(hiveTable);
        CatalogType newCatalogType = CatalogType.fromFlinkTable(newTable);
        Preconditions.checkArgument(
                originCatalogType == newCatalogType,
                String.format(
                        "The alter table operator does not support changing the directory type from %s to %s",
                        originCatalogType, newCatalogType));
        // remove empty options,
        newTable.getOptions().entrySet().stream()
                .filter(entry -> entry.getValue().isEmpty())
                .forEach(entry -> newTable.getOptions().remove(entry));
        // proxy
        try {
            proxyCatalog(
                    newCatalogType,
                    catalog -> {
                        catalog.alterTable(tablePath, newTable, ignoreIfNotExists);
                        return null;
                    });
        } catch (Exception e) {
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public List<CatalogPartitionSpec> listPartitions(ObjectPath tablePath)
            throws CatalogException, TableNotExistException, TableNotPartitionedException {
        try {
            return proxyCatalog(tablePath, catalog -> catalog.listPartitions(tablePath));
        } catch (Exception e) {
            if (e instanceof TableNotPartitionedException) {
                throw (TableNotPartitionedException) e;
            } else if (e instanceof CatalogException) {
                throw (CatalogException) e;
            } else if (e instanceof TableNotExistException) {
                throw (TableNotExistException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public List<CatalogPartitionSpec> listPartitions(
            ObjectPath tablePath, CatalogPartitionSpec partitionSpec)
            throws CatalogException, TableNotExistException, TableNotPartitionedException,
                    PartitionSpecInvalidException {
        try {
            return proxyCatalog(
                    tablePath, catalog -> catalog.listPartitions(tablePath, partitionSpec));
        } catch (Exception e) {
            if (e instanceof TableNotExistException) {
                throw (TableNotExistException) e;
            } else if (e instanceof TableNotPartitionedException) {
                throw (TableNotPartitionedException) e;
            } else if (e instanceof CatalogException) {
                throw (CatalogException) e;
            } else if (e instanceof PartitionSpecInvalidException) {
                throw (PartitionSpecInvalidException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public List<CatalogPartitionSpec> listPartitionsByFilter(
            ObjectPath tablePath, List<Expression> filters)
            throws CatalogException, TableNotExistException, TableNotPartitionedException {
        try {
            return proxyCatalog(
                    tablePath, catalog -> catalog.listPartitionsByFilter(tablePath, filters));
        } catch (Exception e) {
            if (e instanceof TableNotExistException) {
                throw (TableNotExistException) e;
            } else if (e instanceof TableNotPartitionedException) {
                throw (TableNotPartitionedException) e;
            } else if (e instanceof CatalogException) {
                throw (CatalogException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public CatalogPartition getPartition(ObjectPath tablePath, CatalogPartitionSpec partitionSpec)
            throws CatalogException, PartitionNotExistException {
        try {
            return proxyCatalog(
                    tablePath, catalog -> catalog.getPartition(tablePath, partitionSpec));
        } catch (Exception e) {
            if (e instanceof PartitionNotExistException) {
                throw (PartitionNotExistException) e;
            } else if (e instanceof CatalogException) {
                throw (CatalogException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public boolean partitionExists(ObjectPath tablePath, CatalogPartitionSpec partitionSpec)
            throws CatalogException {
        try {
            return proxyCatalog(
                    tablePath, catalog -> catalog.partitionExists(tablePath, partitionSpec));
        } catch (Exception e) {
            if (e instanceof CatalogException) {
                throw (CatalogException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public void createPartition(
            ObjectPath tablePath,
            CatalogPartitionSpec partitionSpec,
            CatalogPartition partition,
            boolean ignoreIfExists)
            throws CatalogException, TableNotExistException, TableNotPartitionedException,
                    PartitionSpecInvalidException, PartitionAlreadyExistsException {
        try {
            proxyCatalog(
                    tablePath,
                    catalog -> {
                        catalog.createPartition(
                                tablePath, partitionSpec, partition, ignoreIfExists);
                        return null;
                    });
        } catch (Exception e) {
            if (e instanceof TableNotExistException) {
                throw (TableNotExistException) e;
            } else if (e instanceof TableNotPartitionedException) {
                throw (TableNotPartitionedException) e;
            } else if (e instanceof PartitionSpecInvalidException) {
                throw (PartitionSpecInvalidException) e;
            } else if (e instanceof PartitionAlreadyExistsException) {
                throw (PartitionAlreadyExistsException) e;
            } else if (e instanceof CatalogException) {
                throw (CatalogException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public void dropPartition(
            ObjectPath tablePath, CatalogPartitionSpec partitionSpec, boolean ignoreIfNotExists)
            throws CatalogException, PartitionNotExistException {
        try {
            proxyCatalog(
                    tablePath,
                    catalog -> {
                        catalog.dropPartition(tablePath, partitionSpec, ignoreIfNotExists);
                        return null;
                    });
        } catch (Exception e) {
            if (e instanceof PartitionNotExistException) {
                throw (PartitionNotExistException) e;
            } else if (e instanceof CatalogException) {
                throw (CatalogException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public void alterPartition(
            ObjectPath tablePath,
            CatalogPartitionSpec partitionSpec,
            CatalogPartition newPartition,
            boolean ignoreIfNotExists)
            throws CatalogException, PartitionNotExistException {
        try {
            proxyCatalog(
                    tablePath,
                    catalog -> {
                        catalog.alterPartition(
                                tablePath, partitionSpec, newPartition, ignoreIfNotExists);
                        return null;
                    });
        } catch (Exception e) {
            if (e instanceof PartitionNotExistException) {
                throw (PartitionNotExistException) e;
            } else if (e instanceof CatalogException) {
                throw (CatalogException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public List<String> listFunctions(String dbName)
            throws DatabaseNotExistException, CatalogException {
        try {
            return proxyCatalog(CatalogType.HIVE, catalog -> catalog.listFunctions(dbName));
        } catch (Exception e) {
            if (e instanceof CatalogException) {
                throw (CatalogException) e;
            } else if (e instanceof DatabaseNotExistException) {
                throw (DatabaseNotExistException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public CatalogFunction getFunction(ObjectPath functionPath)
            throws FunctionNotExistException, CatalogException {
        try {
            return proxyCatalog(CatalogType.HIVE, catalog -> catalog.getFunction(functionPath));
        } catch (Exception e) {
            if (e instanceof CatalogException) {
                throw (CatalogException) e;
            } else if (e instanceof FunctionNotExistException) {
                throw (FunctionNotExistException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public boolean functionExists(ObjectPath functionPath) throws CatalogException {
        try {
            return proxyCatalog(CatalogType.HIVE, catalog -> catalog.functionExists(functionPath));
        } catch (Exception e) {
            if (e instanceof CatalogException) {
                throw (CatalogException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public void createFunction(
            ObjectPath functionPath, CatalogFunction function, boolean ignoreIfExists)
            throws FunctionAlreadyExistException, DatabaseNotExistException, CatalogException {
        try {
            proxyCatalog(
                    CatalogType.HIVE,
                    catalog -> {
                        catalog.createFunction(functionPath, function, ignoreIfExists);
                        return null;
                    });
        } catch (Exception e) {
            if (e instanceof CatalogException) {
                throw (CatalogException) e;
            } else if (e instanceof FunctionAlreadyExistException) {
                throw (FunctionAlreadyExistException) e;
            } else if (e instanceof DatabaseNotExistException) {
                throw (DatabaseNotExistException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public void alterFunction(
            ObjectPath functionPath, CatalogFunction newFunction, boolean ignoreIfNotExists)
            throws FunctionNotExistException, CatalogException {
        try {
            proxyCatalog(
                    CatalogType.HIVE,
                    catalog -> {
                        catalog.alterFunction(functionPath, newFunction, ignoreIfNotExists);
                        return null;
                    });
        } catch (Exception e) {
            if (e instanceof CatalogException) {
                throw (CatalogException) e;
            } else if (e instanceof FunctionNotExistException) {
                throw (FunctionNotExistException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public void dropFunction(ObjectPath functionPath, boolean ignoreIfNotExists)
            throws FunctionNotExistException, CatalogException {
        try {
            proxyCatalog(
                    CatalogType.HIVE,
                    catalog -> {
                        catalog.dropFunction(functionPath, ignoreIfNotExists);
                        return null;
                    });
        } catch (Exception e) {
            if (e instanceof CatalogException) {
                throw (CatalogException) e;
            } else if (e instanceof FunctionNotExistException) {
                throw (FunctionNotExistException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public CatalogTableStatistics getTableStatistics(ObjectPath tablePath)
            throws CatalogException, TableNotExistException {
        try {
            return proxyCatalog(tablePath, catalog -> catalog.getTableStatistics(tablePath));
        } catch (Exception e) {
            if (e instanceof TableNotExistException) {
                throw (TableNotExistException) e;
            } else if (e instanceof CatalogException) {
                throw (CatalogException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public CatalogColumnStatistics getTableColumnStatistics(ObjectPath tablePath)
            throws CatalogException, TableNotExistException {
        try {
            return proxyCatalog(tablePath, catalog -> catalog.getTableColumnStatistics(tablePath));
        } catch (Exception e) {
            if (e instanceof TableNotExistException) {
                throw (TableNotExistException) e;
            } else if (e instanceof CatalogException) {
                throw (CatalogException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public CatalogTableStatistics getPartitionStatistics(
            ObjectPath tablePath, CatalogPartitionSpec partitionSpec)
            throws CatalogException, PartitionNotExistException {
        try {
            return proxyCatalog(
                    tablePath, catalog -> catalog.getPartitionStatistics(tablePath, partitionSpec));
        } catch (Exception e) {
            if (e instanceof PartitionNotExistException) {
                throw (PartitionNotExistException) e;
            } else if (e instanceof CatalogException) {
                throw (CatalogException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public CatalogColumnStatistics getPartitionColumnStatistics(
            ObjectPath tablePath, CatalogPartitionSpec partitionSpec)
            throws CatalogException, PartitionNotExistException {
        try {
            return proxyCatalog(
                    tablePath,
                    catalog -> catalog.getPartitionColumnStatistics(tablePath, partitionSpec));
        } catch (Exception e) {
            if (e instanceof PartitionNotExistException) {
                throw (PartitionNotExistException) e;
            } else if (e instanceof CatalogException) {
                throw (CatalogException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public void alterTableStatistics(
            ObjectPath tablePath, CatalogTableStatistics tableStatistics, boolean ignoreIfNotExists)
            throws CatalogException, TableNotExistException {
        try {
            proxyCatalog(
                    tablePath,
                    catalog -> {
                        catalog.alterTableStatistics(tablePath, tableStatistics, ignoreIfNotExists);
                        return null;
                    });
        } catch (Exception e) {
            if (e instanceof TableNotExistException) {
                throw (TableNotExistException) e;
            } else if (e instanceof CatalogException) {
                throw (CatalogException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public void alterTableColumnStatistics(
            ObjectPath tablePath,
            CatalogColumnStatistics columnStatistics,
            boolean ignoreIfNotExists)
            throws CatalogException, TablePartitionedException, TableNotExistException {
        try {
            proxyCatalog(
                    tablePath,
                    catalog -> {
                        catalog.alterTableColumnStatistics(
                                tablePath, columnStatistics, ignoreIfNotExists);
                        return null;
                    });
        } catch (Exception e) {
            if (e instanceof TableNotExistException) {
                throw (TableNotExistException) e;
            } else if (e instanceof CatalogException) {
                throw (CatalogException) e;
            } else if (e instanceof TablePartitionedException) {
                throw (TablePartitionedException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public void alterPartitionStatistics(
            ObjectPath tablePath,
            CatalogPartitionSpec partitionSpec,
            CatalogTableStatistics partitionStatistics,
            boolean ignoreIfNotExists)
            throws CatalogException, PartitionNotExistException {
        try {
            proxyCatalog(
                    tablePath,
                    catalog -> {
                        catalog.alterPartitionStatistics(
                                tablePath, partitionSpec, partitionStatistics, ignoreIfNotExists);
                        return null;
                    });
        } catch (Exception e) {
            if (e instanceof PartitionNotExistException) {
                throw (PartitionNotExistException) e;
            } else if (e instanceof CatalogException) {
                throw (CatalogException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public void alterPartitionColumnStatistics(
            ObjectPath tablePath,
            CatalogPartitionSpec partitionSpec,
            CatalogColumnStatistics columnStatistics,
            boolean ignoreIfNotExists)
            throws CatalogException, PartitionNotExistException {
        try {
            proxyCatalog(
                    tablePath,
                    catalog -> {
                        catalog.alterPartitionColumnStatistics(
                                tablePath, partitionSpec, columnStatistics, ignoreIfNotExists);
                        return null;
                    });
        } catch (Exception e) {
            if (e instanceof PartitionNotExistException) {
                throw (PartitionNotExistException) e;
            } else if (e instanceof CatalogException) {
                throw (CatalogException) e;
            }
            throw new FlinkxRuntimeException(e);
        }
    }

    private CatalogType invokeGetCatalogType(ObjectPath tablePath) throws Exception {
        return (CatalogType)
                proxyCatalog(
                        CatalogType.HIVE,
                        catalog -> {
                            try {
                                return invokeCatalogTypeMethod.invoke(catalog, tablePath);
                            } catch (Exception e) {
                                if (e.getCause() != null
                                        && e.getCause() instanceof TableNotExistException) {
                                    throw (TableNotExistException) e.getCause();
                                }
                                throw e;
                            }
                        });
    }

    private <T> T proxyCatalog(ObjectPath tablePath, HmsUtil.CallBackOperator<T, Catalog> operator)
            throws Exception {
        CatalogType catalogType = invokeGetCatalogType(tablePath);
        return proxyCatalog(catalogType, operator);
    }

    private <T> T proxyCatalog(
            CatalogType catalogType, HmsUtil.CallBackOperator<T, Catalog> operator)
            throws Exception {
        Preconditions.checkState(
                catalogType != null && !catalogName.equals(""), "catalogType must not be empty");

        ClassLoader catalogClassLoader =
                initClassLoader(catalogType, this.getClass().getClassLoader());

        Catalog catalog =
                cacheCatalog.computeIfAbsent(
                        catalogName + "_inner_" + catalogType,
                        key -> loadCatalog(catalogType, catalogClassLoader));
        return classLoaderCallBack(catalogClassLoader, catalog, operator);
    }

    private ClassLoader initClassLoader(CatalogType catalogType, ClassLoader currentClassLoader) {
        return cacheClassLoader.computeIfAbsent(
                catalogType,
                key -> {
                    String[] alwaysParentFirstPatterns =
                            properties
                                    .getOrDefault(
                                            ALWAYS_PARENT_FIRST_PATTERN_KEY,
                                            DEFAULT_ALWAYS_PARENT_FIRST_LOADER_PATTERNS)
                                    .split(";");
                    return initialClassLoader(
                            catalogType, alwaysParentFirstPatterns, currentClassLoader);
                });
    }

    private Catalog loadCatalog(CatalogType catalogType, ClassLoader catalogClassLoader) {
        Map<String, String> confByType = getConfByType(properties, catalogType);
        try {
            return classLoaderCallBack(
                    catalogClassLoader,
                    "ignore",
                    (object) -> {
                        Catalog catalog =
                                createCatalog(
                                        catalogType, confByType, configuration, catalogClassLoader);
                        catalog.open();
                        return catalog;
                    });
        } catch (Exception e) {
            throw new FlinkxRuntimeException("failed to load catalog instance", e);
        }
    }

    private Catalog createCatalog(
            CatalogType catalogType,
            Map<String, String> options,
            ReadableConfig config,
            ClassLoader classLoader)
            throws Exception {
        return classLoaderCallBack(
                classLoader,
                "ignore",
                (object) -> {
                    Catalog catalog =
                            FactoryUtil.createCatalog(
                                    catalogName + "_inner_" + catalogType,
                                    options,
                                    config,
                                    classLoader,
                                    catalogType.isAddPipelineJar());
                    catalog.open();
                    return catalog;
                });
    }

    @Override
    public Optional<Factory> getFactory() {
        return Optional.of(new HmsDynamicTableFactory(this));
    }

    public Catalog getCachedCatalog(CatalogType catalogType) {
        return cacheCatalog.get(catalogName + "_inner_" + catalogType);
    }

    public ClassLoader getCachedClassLoader(CatalogType catalogType) {
        return cacheClassLoader.get(catalogType);
    }
}
