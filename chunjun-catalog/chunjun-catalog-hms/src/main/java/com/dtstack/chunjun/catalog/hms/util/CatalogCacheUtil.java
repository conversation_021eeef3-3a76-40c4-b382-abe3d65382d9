package com.dtstack.chunjun.catalog.hms.util;

import com.dtstack.chunjun.catalog.hms.catalog.factory.HmsCatalogFactory;
import com.dtstack.chunjun.common.throwble.FlinkxRuntimeException;
import com.dtstack.chunjun.enums.CatalogType;

import org.apache.flink.util.CatalogClassLoader;
import org.apache.flink.util.ChildFirstClassLoader;
import org.apache.flink.util.FlinkUserCodeClassLoader;
import org.apache.flink.util.Preconditions;

import java.io.File;
import java.lang.reflect.Field;
import java.net.URL;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;

public class CatalogCacheUtil {

    public static Optional<String[]> getPatternFromParentLoader(ClassLoader currentClassLoader) {
        try {
            if (currentClassLoader instanceof ChildFirstClassLoader) {
                ChildFirstClassLoader classLoader = (ChildFirstClassLoader) currentClassLoader;
                Field alwaysParentFirstPatterns =
                        ChildFirstClassLoader.class.getDeclaredField("alwaysParentFirstPatterns");
                alwaysParentFirstPatterns.setAccessible(true);
                return Optional.ofNullable((String[]) alwaysParentFirstPatterns.get(classLoader));
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return Optional.empty();
    }

    public static ClassLoader initialClassLoader(
            CatalogType catalogType,
            String[] alwaysParentFirstPatterns,
            ClassLoader currentClassLoader) {

        return new CatalogClassLoader(
                new URL[] {findJarWithCatalogType(catalogType, currentClassLoader)},
                currentClassLoader,
                alwaysParentFirstPatterns,
                FlinkUserCodeClassLoader.NOOP_EXCEPTION_HANDLER);
        //        return FlinkUserCodeClassLoaders.childFirst(
        //                new URL[] {findJarWithCatalogType(catalogType, currentClassLoader)},
        //                currentClassLoader,
        //                alwaysParentFirstPatterns,
        //                FlinkUserCodeClassLoader.NOOP_EXCEPTION_HANDLER,
        //                false);
    }

    public static URL findJarWithCatalogType(
            CatalogType catalogType, ClassLoader currentClassLoader) {
        try {
            File catalogDir = getCatalogDir(currentClassLoader);
            File targetCatalogDir = findTargetCatalogDir(catalogDir, catalogType);
            File[] files = targetCatalogDir.listFiles();
            if (files == null) {
                throw new FlinkxRuntimeException(
                        "find no catalog jar file in dir " + targetCatalogDir.getPath());
            } else if (files.length != 1) {
                throw new FlinkxRuntimeException(
                        "find more than one catalog jar file in dir " + targetCatalogDir.getPath());
            } else {
                return files[0].toURI().toURL();
            }
        } catch (Throwable e) {
            throw new FlinkxRuntimeException(e);
        }
    }

    private static File getCatalogDir(ClassLoader currentClassLoader)
            throws ClassNotFoundException {
        // find chunjun catalogDir like xxx/chunjun-plugin/catalog
        URL hmsCatalogURL =
                currentClassLoader
                        .loadClass(HmsCatalogFactory.class.getName())
                        .getProtectionDomain()
                        .getCodeSource()
                        .getLocation();
        File hmsCatalogJarFile = new File(hmsCatalogURL.getPath());
        File catalogDir = hmsCatalogJarFile.getParentFile().getParentFile();
        Preconditions.checkState(
                catalogDir.getName().equals("catalog"), "find HmsCatalogFactory in a error path");
        return catalogDir;
    }

    private static File findTargetCatalogDir(File catalogDir, CatalogType catalogType)
            throws Throwable {
        // find the catalog jar file in catalogDir using catalogType
        return Arrays.stream(Objects.requireNonNull(catalogDir.listFiles()))
                .filter(file -> file.getName().contains(catalogType.name().toLowerCase()))
                .findFirst()
                .orElseThrow(
                        (Supplier<Throwable>)
                                () ->
                                        new RuntimeException(
                                                String.format(
                                                        "failed to find catalog jar file for catalogType %s in dir %s",
                                                        catalogType, catalogDir.getPath())));
    }
}
