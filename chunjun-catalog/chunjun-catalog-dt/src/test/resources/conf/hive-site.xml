<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<configuration>
    <property>
        <name>hive.server2.thrift.bind.host</name>
        <value>hadoop3x3</value>
    </property>
    <property>
        <name>hive.metastore.uris</name>
        <value>thrift://hadoop3x3:9083</value>
        <description>Thrift URI for the remote metastore. Used by metastore client to connect to remote metastore.
        </description>
    </property>
    <property>
        <name>hive.metastore.event.listeners</name>
        <value>org.apache.hive.hcatalog.listener.DbNotificationListener</value>
    </property>
    <property>
        <name>hive.metastore.event.db.listener.timetolive</name>
        <value>86400s</value>
    </property>
    <property>
        <name>hive.server2.thrift.port</name>
        <value>10000</value>
    </property>
    <property>
        <name>hive.zookeeper.quorum</name>
        <value>hadoop3x1:2181,hadoop3x2:2181,hadoop3x3:2181</value>
    </property>
    <property>
        <name>javax.jdo.option.ConnectionURL</name>
        <value>*********************************************</value>
    </property>
    <property>
        <name>javax.jdo.option.ConnectionDriverName</name>
        <value>com.mysql.jdbc.Driver</value>
    </property>
    <property>
        <name>javax.jdo.option.ConnectionUserName</name>
        <value>root</value>
    </property>
    <property>
        <name>javax.jdo.option.ConnectionPassword</name>
        <value>abc123</value>
    </property>
    <property>
        <name>hive.metastore.warehouse.dir</name>
        <value>/user/hive/warehouse</value>
    </property>
    <property>
        <name>hive.exec.scratchdir</name>
        <value>/user/hive/warehouse</value>
    </property>
    <property>
        <name>datanucleus.schema.autoCreateAll</name>
        <value>true</value>
        <description>creates necessary schema on a startup if one doesn't exist. set this to false, after creating it
            once
        </description>
    </property>
    <property>
        <name>hive.metastore.schema.verification</name>
        <value>false</value>
    </property>
    <property>
        <name>hive.exec.dynamic.partition</name>
        <value>true</value>
    </property>
    <property>
        <name>hive.exec.dynamic.partition.mode</name>
        <value>nonstrict</value>
    </property>
    <property>
        <name>hive.exec.max.dynamic.partitions.pernode</name>
        <value>10000</value>
    </property>
    <property>
        <name>hive.exec.max.dynamic.partitions</name>
        <value>10000</value>
    </property>
    <property>
        <name>hive.metastore.sasl.enabled</name>
        <value>false</value>
    </property>
    <property>
        <name>hive.support.concurrency</name>
        <value>true</value>
    </property>
    <property>
        <name>hive.txn.manager</name>
        <value>org.apache.hadoop.hive.ql.lockmgr.DbTxnManager</value>
    </property>
    <property>
        <name>hive.enforce.bucketing</name>
        <value>true</value>
    </property>
    <property>
        <name>hive.exec.dynamic.partition.mode</name>
        <value>nostrict</value>
    </property>
    <property>
        <name>hive.compactor.initiator.on</name>
        <value>true</value>
    </property>
    <property>
        <name>metastore.compactor.worker.threads</name>
        <value>3</value>
    </property>
    <!--Spark依赖位置-->
    <property>
        <name>spark.yarn.jars</name>
        <value>hdfs://hadoop3x1:9000/spark-jars/*</value>
    </property>
    <!--Hive执行引擎-->
    <property>
        <name>hive.execution.engine</name>
        <value>spark</value>
    </property>
    <!--Hive和spark连接超时时间-->
    <property>
        <name>hive.spark.client.connect.timeout</name>
        <value>300000ms</value>
    </property>
    <property>
        <name>hive.spark.client.server.connect.timeout</name>
        <value>300000ms</value>
    </property>
    <property>
        <name>hive.spark.client.future.timeout</name>
        <value>300s</value>
    </property>
    <!-- Metadata Storage Licensing  -->
    <property>
        <name>hive.metastore.event.db.notification.api.auth</name>
        <value>false</value>
    </property>
</configuration>
