/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.catalog.dt.catalog.factory;

import com.dtstack.chunjun.catalog.dt.catalog.DTCatalog;

import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.table.catalog.Catalog;
import org.apache.flink.table.factories.CatalogFactory;
import org.apache.flink.table.factories.FactoryUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.Set;

import static com.dtstack.chunjun.catalog.dt.catalog.factory.DTCatalogFactoryOptions.CATALOG_DEFAULT_DATABASE;
import static com.dtstack.chunjun.catalog.dt.catalog.factory.DTCatalogFactoryOptions.CATALOG_JDBC_BASE_TENANT_ID;
import static com.dtstack.chunjun.catalog.dt.catalog.factory.DTCatalogFactoryOptions.CATALOG_JDBC_DRIVER;
import static com.dtstack.chunjun.catalog.dt.catalog.factory.DTCatalogFactoryOptions.CATALOG_JDBC_PASSWORD;
import static com.dtstack.chunjun.catalog.dt.catalog.factory.DTCatalogFactoryOptions.CATALOG_JDBC_PROJECT_ID;
import static com.dtstack.chunjun.catalog.dt.catalog.factory.DTCatalogFactoryOptions.CATALOG_JDBC_URL;
import static com.dtstack.chunjun.catalog.dt.catalog.factory.DTCatalogFactoryOptions.CATALOG_JDBC_USERNAME;
import static com.dtstack.chunjun.catalog.dt.catalog.factory.DTCatalogFactoryOptions.IDENTIFIER;
import static org.apache.flink.table.factories.FactoryUtil.PROPERTY_VERSION;

/** Factory for {@link DTCatalog}. */
public class DTCatalogFactory implements CatalogFactory {
    private static final Logger LOG = LoggerFactory.getLogger(DTCatalogFactory.class);

    @Override
    public String factoryIdentifier() {
        return IDENTIFIER;
    }

    @Override
    public Set<ConfigOption<?>> requiredOptions() {
        final Set<ConfigOption<?>> options = new HashSet<>();
        options.add(CATALOG_DEFAULT_DATABASE);
        options.add(CATALOG_JDBC_DRIVER);
        options.add(CATALOG_JDBC_URL);
        options.add(CATALOG_JDBC_USERNAME);
        options.add(CATALOG_JDBC_PASSWORD);
        options.add(CATALOG_JDBC_PROJECT_ID);
        options.add(CATALOG_JDBC_BASE_TENANT_ID);
        return options;
    }

    @Override
    public Set<ConfigOption<?>> optionalOptions() {
        final Set<ConfigOption<?>> options = new HashSet<>();
        options.add(PROPERTY_VERSION);
        return options;
    }

    @Override
    public Catalog createCatalog(Context context) {
        final FactoryUtil.CatalogFactoryHelper helper =
                FactoryUtil.createCatalogFactoryHelper(this, context);
        helper.validate();

        return new DTCatalog(
                context.getClassLoader(),
                context.getName(),
                helper.getOptions().get(CATALOG_DEFAULT_DATABASE),
                helper.getOptions().get(CATALOG_JDBC_USERNAME),
                helper.getOptions().get(CATALOG_JDBC_PASSWORD),
                helper.getOptions().get(CATALOG_JDBC_URL),
                helper.getOptions().get(CATALOG_JDBC_DRIVER),
                helper.getOptions().get(CATALOG_JDBC_PROJECT_ID),
                helper.getOptions().get(CATALOG_JDBC_BASE_TENANT_ID));
    }
}
