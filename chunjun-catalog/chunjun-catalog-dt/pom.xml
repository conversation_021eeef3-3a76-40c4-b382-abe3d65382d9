<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>chunjun-catalog</artifactId>
		<groupId>com.dtstack.chunjun</groupId>
		<version>2.0-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>chunjun-catalog-dt</artifactId>
	<name>ChunJun : Catalog : DT</name>
	<properties>
		<maven.compiler.source>${target.java.version}</maven.compiler.source>
		<maven.compiler.target>${target.java.version}</maven.compiler.target>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<postgresql.version>42.2.10</postgresql.version>
		<ojdbc8.version>19.3.0.0</ojdbc8.version>
		<mysql.version>8.0.20</mysql.version>
		<catalog.dir>dt</catalog.dir>
	</properties>

	<dependencies>
		<!-- Postgres -->
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<version>42.2.10</version>
			<scope>provided</scope>
		</dependency>
		<!-- Oracle -->
		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc8</artifactId>
			<version>19.3.0.0</version>
			<scope>provided</scope>
		</dependency>
		<!-- Mysql -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>${mysql.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.4</version>
			<scope>test</scope>
		</dependency>
		<!-- https://mvnrepository.com/artifact/commons-dbutils/commons-dbutils -->
		<dependency>
			<groupId>commons-dbutils</groupId>
			<artifactId>commons-dbutils</artifactId>
			<version>1.7</version>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.12</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.github.stefanbirkner</groupId>
			<artifactId>system-rules</artifactId>
			<version>1.19.0</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.dtstack.chunjun</groupId>
			<artifactId>chunjun-catalog-jdbc</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<filters>
								<filter>
									<artifact>*:*</artifact>
									<excludes>
										<exclude>META-INF/*.SF</exclude>
										<exclude>META-INF/*.DSA</exclude>
										<exclude>META-INF/*.RSA</exclude>
									</excludes>
								</filter>
								<filter>
									<artifact>org.apache.hive:hive-exec</artifact>
									<excludes>
										<exclude>parquet/**</exclude>
									</excludes>
								</filter>
							</filters>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-resources</id>
						<!-- here the phase you need -->
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<tasks>
								<copy todir="${basedir}/../../${dist.dir}/catalog/dt"
									  file="${basedir}/target/${project.artifactId}-${project.version}.jar"/>
								<!--suppress UnresolvedMavenProperty -->
								<move file="${basedir}/../../${dist.dir}/catalog/dt/${project.artifactId}-${project.version}.jar"
									  tofile="${basedir}/../../${dist.dir}/catalog/dt/${project.artifactId}-${git.branch}.jar"/>
								<delete>
									<!--suppress UnresolvedMavenProperty -->
									<fileset dir="${basedir}/../../${dist.dir}/catalog/dt/"
											 includes="${project.artifactId}-*.jar"
											 excludes="${project.artifactId}-${git.branch}.jar"/>
								</delete>
							</tasks>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
