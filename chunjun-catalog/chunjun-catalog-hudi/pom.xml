<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.dtstack.chunjun</groupId>
		<artifactId>chunjun-catalog</artifactId>
		<version>2.0-SNAPSHOT</version>
	</parent>

	<groupId>org.example</groupId>
	<artifactId>chunjun-catalog-hudi</artifactId>

	<properties>
		<maven.compiler.source>${target.java.version}</maven.compiler.source>
		<maven.compiler.target>${target.java.version}</maven.compiler.target>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<hive.version>2.3.1</hive.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.dtstack.chunjun</groupId>
			<artifactId>chunjun-common</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>commons-logging</groupId>
			<artifactId>commons-logging</artifactId>
			<version>1.1.3</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.hudi</groupId>
			<artifactId>hudi-flink1.16-bundle</artifactId>
			<version>0.14.0-dt-4</version>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-common</artifactId>
			<version>${hadoop.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>gson</artifactId>
					<groupId>com.google.code.gson</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-cli</artifactId>
					<groupId>commons-cli</groupId>
				</exclusion>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-hdfs</artifactId>
			<version>${hadoop.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>commons-cli</artifactId>
					<groupId>commons-cli</groupId>
				</exclusion>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-mapreduce-client-core</artifactId>
			<version>${hadoop.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.hive</groupId>
			<artifactId>hive-exec</artifactId>
			<version>${hive.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>commons-logging</artifactId>
					<groupId>commons-logging</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-cli</artifactId>
					<groupId>commons-cli</groupId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.derby</groupId>
					<artifactId>derby</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>groovy-all</artifactId>
					<groupId>org.codehaus.groovy</groupId>
				</exclusion>
				<exclusion>
					<artifactId>gson</artifactId>
					<groupId>com.google.code.gson</groupId>
				</exclusion>
				<exclusion>
					<artifactId>parquet-hadoop-bundle</artifactId>
					<groupId>org.apache.parquet</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-mapreduce-client-core</artifactId>
			<version>${hadoop.version}</version>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<filters>
								<filter>
									<artifact>*:*</artifact>
									<excludes>
										<exclude>META-INF/*.SF</exclude>
										<exclude>META-INF/*.DSA</exclude>
										<exclude>META-INF/*.RSA</exclude>
									</excludes>
								</filter>
								<filter>
									<artifact>org.apache.hudi:hudi-flink1.12-bundle</artifact>
									<excludes>
										<exclude>META-INF/services/org.apache.flink.table.factories.TableFactory</exclude>
										<exclude>org/apache/flink/*/**</exclude>
									</excludes>
								</filter>
							</filters>
							<relocations>
								<relocation>
									<pattern>com.google.common</pattern>
									<shadedPattern>shade.core.com.google.common</shadedPattern>
								</relocation>
								<relocation>
									<pattern>org.apache.flink.configuration.AkkaOptions</pattern>
									<shadedPattern>shade.core.com.google.common</shadedPattern>
								</relocation>
								<relocation>
									<pattern>com.google.common</pattern>
									<shadedPattern>com.dtstack.chunjun.connector.shaded.hudi.com.google.common</shadedPattern>
								</relocation>
								<relocation>
									<pattern>org.apache.flink.formats.parquet.vector</pattern>
									<shadedPattern>com.dtstack.chunjun.connector.shaded.hudi.org.apache.flink.formats.parquet.vector</shadedPattern>
								</relocation>
								<relocation>
									<pattern>org.apache.flink.formats.parquet.vector</pattern>
									<shadedPattern>com.dtstack.chunjun.connector.shaded.hudi.org.apache.flink.formats.parquet.vector</shadedPattern>
								</relocation>
								<relocation>
									<pattern>org.apache.flink.formats.parquet.utils.SerializableConfiguration</pattern>
									<shadedPattern>com.dtstack.chunjun.connector.shaded.hudi.org.apache.flink.formats.parquet.utils.SerializableConfiguration</shadedPattern>
								</relocation>
								<!-- 防止FileInputFormat被AppClassLoader加载，因为lib下没有com.github.luben:zstd-jni的依赖 -->
								<relocation>
									<pattern>org.apache.flink.api.common.io.FileInputFormat</pattern>
									<shadedPattern>com.dtstack.chunjun.connector.shaded.hudi.org.apache.flink.api.common.io.FileInputFormat</shadedPattern>
								</relocation>
								<relocation>
									<pattern>org.apache.flink.api.common.io.compression.ZStandardInputStreamFactory</pattern>
									<shadedPattern>com.dtstack.chunjun.connector.shaded.hudi.org.apache.flink.api.common.io.compression.ZStandardInputStreamFactory</shadedPattern>
								</relocation>
							</relocations>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-resources</id>
						<!-- here the phase you need -->
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<tasks>
								<copy todir="${basedir}/../../${dist.dir}/catalog/hudi"
									  file="${basedir}/target/${project.artifactId}-${project.version}.jar"/>
								<!--suppress UnresolvedMavenProperty -->
								<move file="${basedir}/../../${dist.dir}/catalog/hudi/${project.artifactId}-${project.version}.jar"
									  tofile="${basedir}/../../${dist.dir}/catalog/hudi/${project.artifactId}-${git.branch}.jar"/>
								<delete>
									<!--suppress UnresolvedMavenProperty -->
									<fileset dir="${basedir}/../../${dist.dir}/catalog/hudi/"
											 includes="${project.artifactId}-*.jar"
											 excludes="${project.artifactId}-${git.branch}.jar"/>
								</delete>
							</tasks>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>


</project>
