package org.apache.hudi.util;

import com.dtstack.chunjun.constants.Metrics;
import com.dtstack.chunjun.metrics.BaseMetric;
import com.dtstack.chunjun.metrics.RowSizeCalculator;
import com.dtstack.chunjun.common.throwble.FlinkxRuntimeException;
import com.dtstack.chunjun.common.util.ReflectionUtils;

import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.api.common.accumulators.LongMaximum;
import org.apache.flink.api.common.functions.RuntimeContext;

import java.lang.reflect.InvocationTargetException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/18 16:46 星期日
 * @email <EMAIL>
 * @company www.dtstack.com
 */
public class ChunjunMetric {

    // ------------------- Flink Metric --------------------
    protected transient BaseMetric outputMetric;
    protected RuntimeContext context;

    // 批量提交字节数
    protected long rowsBytes;

    protected LongCounter bytesWriteCounter;
    protected LongMaximum durationCounter;
    protected LongCounter numWriteCounter;
    protected LongCounter snapshotWriteCounter;
    protected LongCounter errCounter;

    protected LongCounter errBytes;
    protected LongCounter nullErrCounter;
    protected LongCounter duplicateErrCounter;
    protected LongCounter conversionErrCounter;
    protected LongCounter otherErrCounter;

    protected LongCounter nWriteErrors;
    // ------------------- Flink Metric --------------------

    /** 任务开始时间, openInputFormat()开始计算 */
    protected long startTime;

    protected RowSizeCalculator rowSizeCalculator;

    public ChunjunMetric(RuntimeContext context) {
        this.context = context;
        initMetric();
    }

    public void initMetric() {
        errCounter = this.context.getLongCounter(Metrics.NUM_ERRORS);
        errBytes = this.context.getLongCounter(Metrics.ERRORS_BYTES);
        nWriteErrors = this.context.getLongCounter(Metrics.NUM_WRITE_ERRORS);
        nullErrCounter = this.context.getLongCounter(Metrics.NUM_NULL_ERRORS);
        duplicateErrCounter = this.context.getLongCounter(Metrics.NUM_DUPLICATE_ERRORS);
        conversionErrCounter = this.context.getLongCounter(Metrics.NUM_CONVERSION_ERRORS);
        otherErrCounter = this.context.getLongCounter(Metrics.NUM_OTHER_ERRORS);
        numWriteCounter = this.context.getLongCounter(Metrics.NUM_WRITES);
        snapshotWriteCounter = this.context.getLongCounter(Metrics.SNAPSHOT_WRITES);
        bytesWriteCounter = this.context.getLongCounter(Metrics.WRITE_BYTES);
        try {
            durationCounter =
                    (LongMaximum)
                            ReflectionUtils.getDeclaredMethod(
                                            context, "getAccumulator", String.class, Class.class)
                                    .invoke(context, Metrics.WRITE_DURATION, LongMaximum.class);
        } catch (IllegalAccessException e) {
            throw new FlinkxRuntimeException(e);
        } catch (InvocationTargetException e) {
            throw new FlinkxRuntimeException(e);
        }

        outputMetric = new BaseMetric(context);
        outputMetric.addMetric(Metrics.NUM_ERRORS, errCounter);
        outputMetric.addMetric(Metrics.ERRORS_BYTES, errBytes);
        outputMetric.addMetric(Metrics.NUM_WRITE_ERRORS, nWriteErrors);
        outputMetric.addMetric(Metrics.NUM_NULL_ERRORS, nullErrCounter);
        outputMetric.addMetric(Metrics.NUM_DUPLICATE_ERRORS, duplicateErrCounter);
        outputMetric.addMetric(Metrics.NUM_CONVERSION_ERRORS, conversionErrCounter);
        outputMetric.addMetric(Metrics.NUM_OTHER_ERRORS, otherErrCounter);
        outputMetric.addMetric(Metrics.NUM_WRITES, numWriteCounter, true);
        outputMetric.addMetric(Metrics.SNAPSHOT_WRITES, snapshotWriteCounter);
        outputMetric.addMetric(Metrics.WRITE_BYTES, bytesWriteCounter, true);
        outputMetric.addMetric(Metrics.WRITE_DURATION, durationCounter);

        this.startTime = System.currentTimeMillis();
        initRowSizeCalculator();
    }

    protected void initRowSizeCalculator() {
        rowSizeCalculator = RowSizeCalculator.getRowSizeCalculator("objectSizeCalculator", true);
    }

    public void updateNumWrite(Long numWrite) {
        numWriteCounter.add(numWrite);
    }

    public void updateBytesWrite(Object o) {
        bytesWriteCounter.add(rowSizeCalculator.getObjectSize(o));
    }

    public void updateDuration() {
        if (durationCounter != null) {
            durationCounter.resetLocal();
            durationCounter.add(System.currentTimeMillis() - startTime);
        }
    }

    public void closeMetric() {
        if (outputMetric != null) {
            outputMetric.waitForReportMetrics();
        }
    }
}
