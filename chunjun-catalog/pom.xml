<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>chunjun</artifactId>
		<groupId>com.dtstack.chunjun</groupId>
		<version>2.0-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>chunjun-catalog</artifactId>
	<name>ChunJun : Catalog</name>
	<packaging>pom</packaging>
	<modules>
		<module>chunjun-catalog-hive</module>
		<module>chunjun-catalog-jdbc</module>
		<module>chunjun-catalog-dt</module>
		<module>chunjun-catalog-iceberg</module>
        <module>chunjun-catalog-hms</module>
		<module>chunjun-catalog-hudi</module>
        <module>chunjun-catalog-paimon</module>
    </modules>

	<properties>
		<catalog.base.dir>catalog</catalog.base.dir>
		<maven.compiler.source>${target.java.version}</maven.compiler.source>
		<maven.compiler.target>${target.java.version}</maven.compiler.target>
		<slf4j.version>1.7.30</slf4j.version>
		<shading.prefix>chunjun.catalog.shaded</shading.prefix>
		<dist.dir>chunjun-dist</dist.dir>
		<hive.version>1.1.1</hive.version>
	</properties>


	<dependencies>
		<dependency>
			<groupId>com.dtstack.chunjun</groupId>
			<artifactId>chunjun-core</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<groupId>ch.qos.logback</groupId>
					<artifactId>logback-classic</artifactId>
				</exclusion>
				<exclusion>
					<groupId>ch.qos.logback</groupId>
					<artifactId>logback-core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-compress</artifactId>
			<version>1.20</version>
		</dependency>
		<dependency>
			<groupId>commons-cli</groupId>
			<artifactId>commons-cli</artifactId>
			<version>${commons-cli.version}</version>
		</dependency>
	</dependencies>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>commons-cli</groupId>
				<artifactId>commons-cli</artifactId>
				<version>${commons-cli.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<artifactId>maven-antrun-plugin</artifactId>
					<executions>
						<execution>
							<id>copy-resources</id>
							<!-- here the phase you need -->
							<phase>package</phase>
							<goals>
								<goal>run</goal>
							</goals>
							<configuration>
								<tasks>
									<copy todir="${basedir}/../../${dist.dir}/${catalog.base.dir}/${catalog.dir}/"
										  file="${basedir}/target/${project.artifactId}-${project.version}.jar"/>
									<move file="${basedir}/../../${dist.dir}/${catalog.base.dir}/${catalog.dir}/${project.artifactId}-${project.version}.jar"
										  tofile="${basedir}/../../${dist.dir}/${catalog.base.dir}/${catalog.dir}/${project.artifactId}-${git.branch}.jar"/>
									<delete>
										<fileset dir="${basedir}/../../${dist.dir}/${catalog.base.dir}/${catalog.dir}/"
												 includes="${project.artifactId}-*.jar"
												 excludes="${project.artifactId}-${git.branch}.jar"/>
									</delete>
								</tasks>
							</configuration>
						</execution>
					</executions>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>

</project>
