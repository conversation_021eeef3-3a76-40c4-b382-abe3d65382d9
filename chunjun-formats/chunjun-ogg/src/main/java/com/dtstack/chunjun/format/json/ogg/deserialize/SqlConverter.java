/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.format.json.ogg.deserialize;

import com.dtstack.chunjun.common.util.GsonUtil;
import com.dtstack.chunjun.converter.AbstractCDCRowConverter;
import com.dtstack.chunjun.converter.IDeserializationConverter;

import org.apache.flink.table.data.DecimalData;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.StringData;
import org.apache.flink.table.data.TimestampData;
import org.apache.flink.table.types.logical.DecimalType;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.types.RowKind;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.time.temporal.TemporalQueries;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class SqlConverter extends AbstractCDCRowConverter<OggData, LogicalType> {
    protected final List<IDeserializationConverter> metaDataConverters;
    private final List<String> metaKey;

    public SqlConverter(RowType rowType, List<String> metaKey) {
        super.fieldNameList = rowType.getFieldNames();
        this.metaKey = metaKey;
        this.metaDataConverters = buildMetaDataConverters(metaKey);
        super.converters = new ArrayList<>();
        for (int i = 0; i < rowType.getFieldCount(); i++) {
            super.converters.add(
                    wrapIntoNullableInternalConverter(
                            createInternalConverter(rowType.getTypeAt(i))));
        }
    }

    @Override
    public LinkedList<RowData> toInternal(OggData oggData) throws Exception {

        LinkedList<RowData> data = new LinkedList<>();

        GenericRowData rowData = new GenericRowData(fieldNameList.size() + metaKey.size());
        Object opType = oggData.getMetaData().get("op_type");
        if (opType.equals("D")) {
            rowData.setRowKind(RowKind.DELETE);
            fillGenericRowData(rowData, oggData.getBeforeData());
            fillGenericRowDataWithMetaData(rowData, oggData.getMetaData());
            data.add(rowData);
        } else if (opType.equals("I")) {
            rowData.setRowKind(RowKind.INSERT);
            fillGenericRowData(rowData, oggData.getAfterData());
            fillGenericRowDataWithMetaData(rowData, oggData.getMetaData());
            data.add(rowData);
        } else {
            if (MapUtils.isNotEmpty(oggData.getBeforeData())) {
                rowData.setRowKind(RowKind.UPDATE_BEFORE);
                fillGenericRowData(rowData, oggData.getBeforeData());
                fillGenericRowDataWithMetaData(rowData, oggData.getMetaData());
                data.add(rowData);
            }
            GenericRowData afterRowData =
                    new GenericRowData(RowKind.UPDATE_AFTER, fieldNameList.size() + metaKey.size());
            fillGenericRowData(afterRowData, oggData.getAfterData());
            fillGenericRowDataWithMetaData(afterRowData, oggData.getMetaData());
            data.add(afterRowData);
        }

        return data;
    }

    @Override
    protected IDeserializationConverter createInternalConverter(LogicalType type) {
        switch (type.getTypeRoot()) {
            case NULL:
                return val -> null;
            case BOOLEAN:
                return val -> Boolean.valueOf(val.toString());
            case TINYINT:
                return val -> Byte.parseByte(val.toString().trim());
            case SMALLINT:
                return val -> Short.parseShort(val.toString().trim());
            case INTEGER:
            case INTERVAL_YEAR_MONTH:
                return val -> Integer.valueOf(val.toString());
            case BIGINT:
            case INTERVAL_DAY_TIME:
                return val -> Long.valueOf(val.toString());
            case DATE:
                return val -> {
                    if (NumberUtils.isNumber(val.toString())) {
                        return Integer.valueOf(val.toString());
                    } else {
                        LocalDate date =
                                DateTimeFormatter.ISO_LOCAL_DATE
                                        .parse(val.toString())
                                        .query(TemporalQueries.localDate());
                        return (int) date.toEpochDay();
                    }
                };
            case TIME_WITHOUT_TIME_ZONE:
                return val -> {
                    if (NumberUtils.isNumber(val.toString())) {
                        return Integer.valueOf(val.toString());
                    } else {
                        TemporalAccessor parsedTime = SQL_TIME_FORMAT.parse(val.toString());
                        LocalTime localTime = parsedTime.query(TemporalQueries.localTime());
                        return localTime.toSecondOfDay() * 1000;
                    }
                };
            case TIMESTAMP_WITHOUT_TIME_ZONE:
                return val -> {
                    TemporalAccessor parsedTimestamp;
                    try {
                        parsedTimestamp = ISO8601_TIMESTAMP_FORMAT.parse(val.toString());
                    } catch (Exception e) {
                        parsedTimestamp = SQL_TIMESTAMP_FORMAT.parse(val.toString());
                    }
                    LocalTime localTime = parsedTimestamp.query(TemporalQueries.localTime());
                    LocalDate localDate = parsedTimestamp.query(TemporalQueries.localDate());
                    return TimestampData.fromLocalDateTime(LocalDateTime.of(localDate, localTime));
                };
            case TIMESTAMP_WITH_LOCAL_TIME_ZONE:
                return val -> {
                    TemporalAccessor parsedTimestampWithLocalZone;
                    try {
                        parsedTimestampWithLocalZone =
                                ISO8601_TIMESTAMP_WITH_LOCAL_TIMEZONE_FORMAT.parse(val.toString());
                    } catch (Exception e) {
                        parsedTimestampWithLocalZone =
                                SQL_TIMESTAMP_WITH_LOCAL_TIMEZONE_FORMAT.parse(val.toString());
                    }
                    LocalTime localTime =
                            parsedTimestampWithLocalZone.query(TemporalQueries.localTime());
                    LocalDate localDate =
                            parsedTimestampWithLocalZone.query(TemporalQueries.localDate());

                    return TimestampData.fromInstant(
                            LocalDateTime.of(localDate, localTime).toInstant(ZoneOffset.UTC));
                };
            case FLOAT:
                return val -> Float.valueOf(val.toString());
            case DOUBLE:
                return val -> Double.valueOf(val.toString());
            case CHAR:
            case VARCHAR:
                return val -> {
                    if (val instanceof Map) {
                        return StringData.fromString(GsonUtil.GSON.toJson(val));
                    } else {
                        return StringData.fromString(val.toString());
                    }
                };
            case BINARY:
            case VARBINARY:
                return val -> {
                    if (val instanceof byte[]) {
                        return val;
                    } else {
                        return val.toString().getBytes(StandardCharsets.UTF_8);
                    }
                };
            case DECIMAL:
                return val -> {
                    final int precision = ((DecimalType) type).getPrecision();
                    final int scale = ((DecimalType) type).getScale();
                    return DecimalData.fromBigDecimal(
                            new BigDecimal(val.toString()), precision, scale);
                };
            case ARRAY:
            case MAP:
            case MULTISET:
            case ROW:
                return createRowConverter((RowType) type);
            case RAW:
            default:
                throw new UnsupportedOperationException("Not support to parse type: " + type);
        }
    }

    private List<IDeserializationConverter> buildMetaDataConverters(List<String> fieldNameList) {
        return fieldNameList.stream()
                .map(
                        i ->
                                this.createInternalConverter(
                                        Arrays.stream(
                                                        OggJsonDeserializationSchema
                                                                .ReadableMetadata.values())
                                                .filter(m -> m.key.equals(i))
                                                .findFirst()
                                                .get()
                                                .dataType
                                                .getLogicalType()))
                .collect(Collectors.toList());
    }

    public IDeserializationConverter createRowConverter(RowType rowType) {
        final IDeserializationConverter[] fieldConverters =
                rowType.getFields().stream()
                        .map(RowType.RowField::getType)
                        .map(i1 -> wrapIntoNullableInternalConverter(createInternalConverter(i1)))
                        .toArray(IDeserializationConverter[]::new);
        final String[] fieldNames = rowType.getFieldNames().toArray(new String[0]);

        return val -> {
            Map<String, Object> map = (Map<String, Object>) val;
            int arity = fieldNames.length;
            GenericRowData row = new GenericRowData(arity);
            for (int i = 0; i < arity; i++) {
                String fieldName = fieldNames[i];
                Object field = map.get(fieldName);
                try {
                    Object convertedField = fieldConverters[i].deserialize(field);
                    row.setField(i, convertedField);
                } catch (Throwable t) {
                    throw new RuntimeException(
                            String.format(
                                    "Fail to deserialize at field: %s.  data: %s",
                                    fieldName, field),
                            t);
                }
            }
            return row;
        };
    }

    private void fillGenericRowData(GenericRowData rowData, Map<String, Object> data) {
        for (int i = 0; i < fieldNameList.size(); i++) {
            String name = fieldNameList.get(i);
            try {
                rowData.setField(i, converters.get(i).deserialize(data.get(name)));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    private void fillGenericRowDataWithMetaData(
            GenericRowData rowData, Map<String, Object> metaData) {
        for (int i = 0; i < metaKey.size(); i++) {
            Object data = metaData.get(metaKey.get(i));
            Object deserialize = null;
            try {
                deserialize = metaDataConverters.get(i).deserialize(data);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            rowData.setField(fieldNameList.size() + i, deserialize);
        }
    }
}
