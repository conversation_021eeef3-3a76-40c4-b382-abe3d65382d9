/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.format.json.chunjun.deserialize;

import com.dtstack.chunjun.common.util.GsonUtil;
import com.dtstack.chunjun.converter.IDeserializationConverter;

import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.data.DecimalData;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.StringData;
import org.apache.flink.table.data.TimestampData;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.logical.DecimalType;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.types.RowKind;
import org.apache.flink.util.Collector;

import com.google.common.collect.Lists;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.time.temporal.TemporalAccessor;
import java.time.temporal.TemporalQueries;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ChunjunJsonDeserializationSchema implements DeserializationSchema<RowData> {

    // times
    private static final DateTimeFormatter SQL_TIME_FORMAT =
            (new DateTimeFormatterBuilder())
                    .appendPattern("HH:mm:ss")
                    .appendFraction(ChronoField.NANO_OF_SECOND, 0, 9, true)
                    .toFormatter();

    protected static final DateTimeFormatter SQL_TIMESTAMP_FORMAT =
            (new DateTimeFormatterBuilder())
                    .append(DateTimeFormatter.ISO_LOCAL_DATE)
                    .appendLiteral(' ')
                    .append(SQL_TIME_FORMAT)
                    .toFormatter();
    protected static final DateTimeFormatter ISO8601_TIMESTAMP_FORMAT =
            DateTimeFormatter.ISO_LOCAL_DATE_TIME;

    protected static DateTimeFormatter SQL_TIMESTAMP_WITH_LOCAL_TIMEZONE_FORMAT =
            new DateTimeFormatterBuilder()
                    .append(DateTimeFormatter.ISO_LOCAL_DATE)
                    .appendLiteral(' ')
                    .append(SQL_TIME_FORMAT)
                    .appendPattern("'Z'")
                    .toFormatter();
    protected static DateTimeFormatter ISO8601_TIMESTAMP_WITH_LOCAL_TIMEZONE_FORMAT =
            new DateTimeFormatterBuilder()
                    .append(DateTimeFormatter.ISO_LOCAL_DATE)
                    .appendLiteral('T')
                    .append(DateTimeFormatter.ISO_LOCAL_TIME)
                    .appendPattern("'Z'")
                    .toFormatter();

    private final List<String> METADATA_KEY =
            Lists.newArrayList("database", "schema", "table", "opTime", "ts", "lsn", "type", "scn");
    private TypeInformation<RowData> typeInformation;
    private RowType rowType;
    private int rowTypeSize;
    private List<IDeserializationConverter> converters;
    private List<IDeserializationConverter> metaDataConverters;
    private List<String> metadataKeys;

    public ChunjunJsonDeserializationSchema(
            RowType rowType, TypeInformation<RowData> typeInformation, List<String> metadataKeys) {
        this.rowType = rowType;
        this.rowTypeSize = rowType.getFields().size();
        this.typeInformation = typeInformation;
        this.converters = new ArrayList<>(rowType.getFieldCount());
        for (int i = 0; i < rowType.getFieldCount(); i++) {
            this.converters.add(
                    wrapIntoNullableConverter(createNotNullConverter(rowType.getTypeAt(i))));
        }
        this.metadataKeys = metadataKeys;
        this.metaDataConverters = new ArrayList<>(metadataKeys.size());
        for (String key : metadataKeys) {
            Arrays.stream(ReadableMetadata.values())
                    .filter(i -> i.key.equals(key))
                    .findFirst()
                    .ifPresent(i -> metaDataConverters.add(i.converter));
        }
    }

    @Override
    public RowData deserialize(byte[] message) {
        throw new RuntimeException(
                "Please invoke DeserializationSchema#deserialize(byte[], Collector<RowData>) instead.");
    }

    @Override
    public void deserialize(byte[] message, Collector<RowData> out) throws IOException {
        if (message == null || message.length == 0) {
            return;
        }

        Map<String, Object> map = GsonUtil.GSON.fromJson(new String(message), Map.class);
        if (MapUtils.isEmpty(map)) {
            return;
        }

        if (map.size() == 1 && map.containsKey("message")) {
            map = (Map<String, Object>) map.get("message");
        }

        HashMap<String, Object> metaData = new HashMap<>();
        HashMap<String, Object> columnData = new HashMap<>();
        map.forEach(
                (k, v) -> {
                    if (METADATA_KEY.contains(k)) {
                        metaData.put(k, v);
                    } else {
                        columnData.put(k, v);
                    }
                });

        Map<String, Object> before = new HashMap<>();
        Map<String, Object> after = new HashMap<>();

        String beforeKey = "before";
        String afterKey = "after";
        boolean pavingData =
                !(columnData.size() == 2
                        && columnData.containsKey(beforeKey)
                        && columnData.containsKey(afterKey)
                        && columnData.get(beforeKey) instanceof Map
                        && columnData.get(afterKey) instanceof Map);

        if (!pavingData) {
            if (columnData.get(beforeKey) instanceof Map) {
                before = (Map<String, Object>) columnData.get(beforeKey);
            }
        }
        if (columnData.get(afterKey) instanceof Map) {
            after = (Map<String, Object>) columnData.get(afterKey);
        }

        try {
            String type = metaData.get("type").toString();
            if (type.equalsIgnoreCase("UPDATE")) {
                // need split update_before and update_after.
                GenericRowData update_before =
                        new GenericRowData(
                                RowKind.UPDATE_BEFORE,
                                rowType.getFieldCount() + metadataKeys.size());
                GenericRowData update_after =
                        new GenericRowData(
                                RowKind.UPDATE_AFTER,
                                rowType.getFieldCount() + metadataKeys.size());

                for (int i = 0; i < rowType.getFields().size(); i++) {
                    RowType.RowField rowField = rowType.getFields().get(i);
                    String name = rowField.getName();

                    Object beforeData;
                    Object afterData;
                    if (pavingData) {
                        beforeData =
                                columnData.getOrDefault("before_" + name, columnData.get(name));
                        afterData = columnData.getOrDefault("after_" + name, columnData.get(name));
                    } else {
                        beforeData = before.get(name);
                        afterData = after.get(name);
                    }
                    Object beforeColumn = converters.get(i).deserialize(beforeData);
                    Object afterColumn = converters.get(i).deserialize(afterData);
                    update_before.setField(i, beforeColumn);
                    update_after.setField(i, afterColumn);
                }
                fillRowDataWithMetaData(update_before, metaData);
                fillRowDataWithMetaData(update_after, metaData);

                out.collect(update_before);
                out.collect(update_after);
            } else {
                RowKind rowKind = RowKind.valueOf(type);
                GenericRowData rowData =
                        new GenericRowData(rowKind, rowType.getFieldCount() + metadataKeys.size());
                if (rowKind == RowKind.INSERT) {
                    if (pavingData) {
                        fillRowDataWithColumn(rowData, "after_", columnData);
                    } else {
                        fillRowDataWithColumn(rowData, "", after);
                    }
                    fillRowDataWithMetaData(rowData, metaData);
                    out.collect(rowData);
                } else if (rowKind == RowKind.DELETE) {
                    if (pavingData) {
                        fillRowDataWithColumn(rowData, "before_", columnData);
                    } else {
                        fillRowDataWithColumn(rowData, "", before);
                    }
                    fillRowDataWithMetaData(rowData, metaData);
                    out.collect(rowData);
                } else if (rowKind == RowKind.UPDATE_AFTER || rowKind == RowKind.UPDATE_BEFORE) {
                    fillRowDataWithColumn(rowData, "", columnData);
                    fillRowDataWithMetaData(rowData, metaData);
                    out.collect(rowData);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean isEndOfStream(RowData nextElement) {
        return false;
    }

    @Override
    public TypeInformation<RowData> getProducedType() {
        return typeInformation;
    }

    private void fillRowDataWithColumn(
            GenericRowData rowData, String prefix, Map<String, Object> columnMap) throws Exception {
        for (int i = 0; i < rowType.getFields().size(); i++) {
            RowType.RowField rowField = rowType.getFields().get(i);
            String name = rowField.getName();
            Object data = columnMap.getOrDefault(prefix + name, columnMap.get(name));
            Object afterColumn = converters.get(i).deserialize(data);
            rowData.setField(i, afterColumn);
        }
    }

    private void fillRowDataWithMetaData(GenericRowData rowData, Map<String, Object> metaData)
            throws Exception {
        for (int i = 0; i < metadataKeys.size(); i++) {
            Object data = metaData.get(metadataKeys.get(i));
            Object deserialize = metaDataConverters.get(i).deserialize(data);
            rowData.setField(rowTypeSize + i, deserialize);
        }
    }

    private static IDeserializationConverter wrapIntoNullableConverter(
            IDeserializationConverter converter) {
        return data -> {
            if (data == null) {
                return null;
            }
            return converter.deserialize(data);
        };
    }

    private IDeserializationConverter createNotNullConverter(LogicalType type) {
        switch (type.getTypeRoot()) {
            case NULL:
                return val -> null;
            case BOOLEAN:
                return val -> Boolean.valueOf(val.toString());
            case TINYINT:
                return val -> Byte.parseByte(val.toString().trim());
            case SMALLINT:
                return val -> Short.parseShort(val.toString().trim());
            case INTEGER:
            case INTERVAL_YEAR_MONTH:
                return val -> Integer.valueOf(val.toString());
            case BIGINT:
            case INTERVAL_DAY_TIME:
                return val -> Long.valueOf(val.toString());
            case DATE:
                return val -> {
                    if (NumberUtils.isNumber(val.toString())) {
                        return Integer.valueOf(val.toString());
                    } else {
                        LocalDate date =
                                DateTimeFormatter.ISO_LOCAL_DATE
                                        .parse(val.toString())
                                        .query(TemporalQueries.localDate());
                        return (int) date.toEpochDay();
                    }
                };
            case TIME_WITHOUT_TIME_ZONE:
                return val -> {
                    if (NumberUtils.isNumber(val.toString())) {
                        return Integer.valueOf(val.toString());
                    } else {
                        TemporalAccessor parsedTime = SQL_TIME_FORMAT.parse(val.toString());
                        LocalTime localTime = parsedTime.query(TemporalQueries.localTime());
                        return localTime.toSecondOfDay() * 1000;
                    }
                };
            case TIMESTAMP_WITHOUT_TIME_ZONE:
                return val -> {
                    TemporalAccessor parsedTimestamp;
                    try {
                        parsedTimestamp = ISO8601_TIMESTAMP_FORMAT.parse(val.toString());
                    } catch (Exception e) {
                        parsedTimestamp = SQL_TIMESTAMP_FORMAT.parse(val.toString());
                    }
                    LocalTime localTime = parsedTimestamp.query(TemporalQueries.localTime());
                    LocalDate localDate = parsedTimestamp.query(TemporalQueries.localDate());
                    return TimestampData.fromLocalDateTime(LocalDateTime.of(localDate, localTime));
                };
            case TIMESTAMP_WITH_LOCAL_TIME_ZONE:
                return val -> {
                    TemporalAccessor parsedTimestampWithLocalZone;
                    try {
                        parsedTimestampWithLocalZone =
                                ISO8601_TIMESTAMP_WITH_LOCAL_TIMEZONE_FORMAT.parse(val.toString());
                    } catch (Exception e) {
                        parsedTimestampWithLocalZone =
                                SQL_TIMESTAMP_WITH_LOCAL_TIMEZONE_FORMAT.parse(val.toString());
                    }
                    LocalTime localTime =
                            parsedTimestampWithLocalZone.query(TemporalQueries.localTime());
                    LocalDate localDate =
                            parsedTimestampWithLocalZone.query(TemporalQueries.localDate());

                    return TimestampData.fromInstant(
                            LocalDateTime.of(localDate, localTime).toInstant(ZoneOffset.UTC));
                };
            case FLOAT:
                return val -> Float.valueOf(val.toString());
            case DOUBLE:
                return val -> Double.valueOf(val.toString());
            case CHAR:
            case VARCHAR:
                return val -> StringData.fromString(val.toString());
            case BINARY:
            case VARBINARY:
                return val -> {
                    if (val instanceof byte[]) {
                        return val;
                    } else {
                        return val.toString().getBytes(StandardCharsets.UTF_8);
                    }
                };
            case DECIMAL:
                return val -> {
                    final int precision = ((DecimalType) type).getPrecision();
                    final int scale = ((DecimalType) type).getScale();
                    return DecimalData.fromBigDecimal(
                            new BigDecimal(val.toString()), precision, scale);
                };
            case ARRAY:
            case MAP:
            case MULTISET:
            case ROW:
            case RAW:
            default:
                throw new UnsupportedOperationException("Not support to parse type: " + type);
        }
    }

    enum ReadableMetadata {
        DATABASE(
                "database",
                DataTypes.STRING().nullable(),
                wrapIntoNullableConverter(
                        new IDeserializationConverter() {
                            private static final long serialVersionUID = 1L;

                            @Override
                            public StringData deserialize(Object field) {
                                return StringData.fromString(field.toString());
                            }
                        })),
        SCHEMA(
                "schema",
                DataTypes.STRING().nullable(),
                wrapIntoNullableConverter(
                        new IDeserializationConverter() {
                            private static final long serialVersionUID = 1L;

                            @Override
                            public StringData deserialize(Object field) {
                                return StringData.fromString(field.toString());
                            }
                        })),
        TABLE(
                "table",
                DataTypes.STRING().nullable(),
                wrapIntoNullableConverter(
                        new IDeserializationConverter() {
                            private static final long serialVersionUID = 1L;

                            @Override
                            public StringData deserialize(Object field) {
                                return StringData.fromString(field.toString());
                            }
                        })),

        OPTIME(
                "opTime",
                DataTypes.STRING().nullable(),
                wrapIntoNullableConverter(
                        new IDeserializationConverter() {
                            private static final long serialVersionUID = 1L;

                            @Override
                            public StringData deserialize(Object field) {
                                return StringData.fromString(field.toString());
                            }
                        })),

        TS(
                "ts",
                DataTypes.BIGINT().nullable(),
                wrapIntoNullableConverter(
                        new IDeserializationConverter() {
                            private static final long serialVersionUID = 1L;

                            @Override
                            public Long deserialize(Object field) {
                                return Long.valueOf(field.toString());
                            }
                        })),

        LSN(
                "lsn",
                DataTypes.STRING().nullable(),
                wrapIntoNullableConverter(
                        new IDeserializationConverter() {
                            private static final long serialVersionUID = 1L;

                            @Override
                            public StringData deserialize(Object field) {
                                return StringData.fromString(field.toString());
                            }
                        })),
        SCN(
                "scn",
                DataTypes.STRING().nullable(),
                wrapIntoNullableConverter(
                        new IDeserializationConverter() {
                            private static final long serialVersionUID = 1L;

                            @Override
                            public DecimalData deserialize(Object field) {
                                return DecimalData.fromBigDecimal(
                                        new BigDecimal(field.toString()), 38, 0);
                            }
                        })),

        TYPE(
                "type",
                DataTypes.STRING().nullable(),
                wrapIntoNullableConverter(
                        new IDeserializationConverter() {
                            private static final long serialVersionUID = 1L;

                            @Override
                            public StringData deserialize(Object field) {
                                return StringData.fromString(field.toString());
                            }
                        })),
        ;

        final String key;

        final DataType dataType;

        final IDeserializationConverter converter;

        ReadableMetadata(String key, DataType dataType, IDeserializationConverter converter) {
            this.key = key;
            this.dataType = dataType;

            this.converter = converter;
        }
    }
}
