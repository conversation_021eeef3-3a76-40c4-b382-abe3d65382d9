package com.dtstack.chunjun.format.json.ar.deserialize;

import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.configuration.ReadableConfig;
import org.apache.flink.formats.common.TimestampFormat;
import org.apache.flink.formats.json.JsonFormatOptionsUtil;
import org.apache.flink.table.api.ValidationException;
import org.apache.flink.table.connector.ChangelogMode;
import org.apache.flink.table.connector.format.DecodingFormat;
import org.apache.flink.table.connector.source.DynamicTableSource;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.factories.DeserializationFormatFactory;
import org.apache.flink.table.factories.DynamicTableFactory;
import org.apache.flink.table.factories.FactoryUtil;
import org.apache.flink.table.types.DataType;
import org.apache.flink.types.RowKind;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

import static com.dtstack.chunjun.format.json.ar.deserialize.ARJsonOptions.IGNORE_PARSE_ERRORS;
import static com.dtstack.chunjun.format.json.ar.deserialize.ARJsonOptions.JSON_MAP_NULL_KEY_LITERAL;
import static com.dtstack.chunjun.format.json.ar.deserialize.ARJsonOptions.JSON_MAP_NULL_KEY_MODE;
import static com.dtstack.chunjun.format.json.ar.deserialize.ARJsonOptions.SCHEMA_INCLUDE;
import static com.dtstack.chunjun.format.json.ar.deserialize.ARJsonOptions.TIMESTAMP_FORMAT;
import static org.apache.flink.formats.json.JsonFormatOptions.ENCODE_DECIMAL_AS_PLAIN_NUMBER;

/** duweike,2021/07/16 */
public class ARJsonFormatFactory implements DeserializationFormatFactory {

    public static final String IDENTIFIER = "ar-json-x";

    @Override
    public DecodingFormat<DeserializationSchema<RowData>> createDecodingFormat(
            DynamicTableFactory.Context context, ReadableConfig formatOptions) {

        FactoryUtil.validateFactoryOptions(this, formatOptions);
        validateDecodingFormatOptions(formatOptions);

        final boolean ignoreParseErrors = formatOptions.get(IGNORE_PARSE_ERRORS);
        TimestampFormat timestampFormat = JsonFormatOptionsUtil.getTimestampFormat(formatOptions);

        return new DecodingFormat<DeserializationSchema<RowData>>() {
            @Override
            public DeserializationSchema<RowData> createRuntimeDecoder(
                    DynamicTableSource.Context context, DataType physicalDataType) {

                final TypeInformation<RowData> producedTypeInfo =
                        context.createTypeInformation(physicalDataType);

                return new ARJsonDeserializationSchema(
                        physicalDataType, producedTypeInfo, ignoreParseErrors, timestampFormat);
            }

            @Override
            public ChangelogMode getChangelogMode() {
                return ChangelogMode.newBuilder()
                        .addContainedKind(RowKind.INSERT)
                        .addContainedKind(RowKind.UPDATE_BEFORE)
                        .addContainedKind(RowKind.UPDATE_AFTER)
                        .addContainedKind(RowKind.DELETE)
                        .build();
            }
        };
    }

    @Override
    public String factoryIdentifier() {
        return IDENTIFIER;
    }

    @Override
    public Set<ConfigOption<?>> requiredOptions() {
        return Collections.emptySet();
    }

    @Override
    public Set<ConfigOption<?>> optionalOptions() {
        Set<ConfigOption<?>> options = new HashSet<>();
        options.add(SCHEMA_INCLUDE);
        options.add(IGNORE_PARSE_ERRORS);
        options.add(TIMESTAMP_FORMAT);
        options.add(JSON_MAP_NULL_KEY_MODE);
        options.add(JSON_MAP_NULL_KEY_LITERAL);
        options.add(ENCODE_DECIMAL_AS_PLAIN_NUMBER);
        return options;
    }

    /** Validator for debezium decoding format. */
    private static void validateDecodingFormatOptions(ReadableConfig tableOptions) {
        JsonFormatOptionsUtil.validateDecodingFormatOptions(tableOptions);
    }

    /** Validator for debezium encoding format. */
    private static void validateEncodingFormatOptions(ReadableConfig tableOptions) {
        JsonFormatOptionsUtil.validateEncodingFormatOptions(tableOptions);

        // validator for {@link SCHEMA_INCLUDE}
        if (tableOptions.get(SCHEMA_INCLUDE)) {
            throw new ValidationException(
                    String.format(
                            "Debezium JSON serialization doesn't support '%s.%s' option been set to true.",
                            IDENTIFIER, SCHEMA_INCLUDE.key()));
        }
    }
}
