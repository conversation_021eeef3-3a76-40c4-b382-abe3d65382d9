-- noinspection SqlNoDataSourceInspectionForFile

-- register a MySQL table 'users' in Flink SQL

CREATE TABLE datagen
(
    id   INT,
    name STRING,
    age     INT,
    ts AS localtimestamp,
    WATERMARK    FOR ts AS ts
) WITH (
  'connector' = 'datagen',
  -- optional options --
  'rows-per-second'='1',
  'fields.id.kind'='sequence',
  'fields.id.start'='1',
  'fields.id.end'='500',
  'fields.age.min'='1',
  'fields.age.max'='500',
  'fields.name.length'='10'
  );


-- create database dtstack;
-- CREATE TABLE dtstack.sink_table (
--     id BIGINT,
--     name VARCHAR(255),
--     age INT,
--     ts timestamp
-- );


CREATE TABLE sink_table (
     id BIGINT,
     name STRING,
     age INT,
     ts timestamp,
     PRIMARY KEY (id) NOT ENFORCED
) WITH (
      'connector' = 'oracle-x',
      'url' = '***********************************',
      'username' = 'zhiqiang',
      'password' = '123456',
      'table-name' = 'ZHIQIANG.SINK_TABLE'
);


insert into sink_table
select *
from datagen
