/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.cmq.source;

import com.dtstack.chunjun.connector.cmq.conf.CmqConf;
import com.dtstack.chunjun.throwable.ReadRecordException;

import org.apache.flink.core.io.GenericInputSplit;
import org.apache.flink.core.io.InputSplit;
import org.apache.flink.table.data.RowData;

import com.qcloud.cmq.client.consumer.Consumer;
import com.qcloud.cmq.client.consumer.Message;
import com.qcloud.cmq.client.consumer.MessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2022-12-19 20:09
 * @description
 */
public class CmqInputFormat extends BaseRichInputFormat {

    private static final Logger LOG = LoggerFactory.getLogger(CmqInputFormat.class);

    /** Cmq Conf */
    private CmqConf cmqConf;

    private Consumer consumer;

    private String queue;
    /** Encapsulate the data in emq */
    private transient BlockingQueue<String> dataQueue;

    private String data;
    private List<Long> ackList;

    @Override
    protected InputSplit[] createInputSplitsInternal(int minNumSplits) {
        InputSplit[] splits = new InputSplit[minNumSplits];
        for (int i = 0; i < minNumSplits; i++) {
            splits[i] = new GenericInputSplit(i, minNumSplits);
        }
        return splits;
    }

    @Override
    protected void openInternal(InputSplit inputSplit) throws IOException {
        super.openInputFormat();
        consumer = new Consumer();
        consumer.setNameServerAddress(cmqConf.getNameServerAddress());
        consumer.setSecretId(cmqConf.getSecretId());
        consumer.setSecretKey(cmqConf.getSecretKey());
        queue = cmqConf.getQueue();

        consumer.setSignMethod(cmqConf.getSignMethod());

        consumer.setBatchPullNumber(Integer.valueOf(cmqConf.getBatchPullNumber()));
        consumer.setPollingWaitSeconds(Integer.valueOf(cmqConf.getPollingWaitSeconds()));
        consumer.setRequestTimeoutMS(Integer.valueOf(cmqConf.getRequestTimeoutMS()));

        dataQueue = new LinkedBlockingDeque<>(1024);
        MessageListener listener =
                (queue, msgs) -> {
                    ackList = new ArrayList<>(msgs.size());
                    for (Message msg : msgs) {
                        if (LOG.isDebugEnabled()) {
                            LOG.debug("msg = {}", msg);
                        }
                        try {
                            dataQueue.put(msg.getData());
                            ackList.add(msg.getReceiptHandle());
                        } catch (InterruptedException e) {
                            LOG.error(e.getMessage() + "\n" + msg);
                        }
                    }
                    return ackList;
                };

        try {
            consumer.start();
            consumer.subscribe(queue, listener);
            LOG.info("Subscribe Success!");
        } catch (Exception e) {
            LOG.error("Subscribe Error: ", e);
        }
    }

    @Override
    protected RowData nextRecordInternal(RowData rowData) throws ReadRecordException {
        try {
            // take 会阻塞 checkpoint.
            data = dataQueue.poll(100, TimeUnit.MILLISECONDS);
            if (data == null) {
                return null;
            }
            rowData = converter.toInternal(data);
        } catch (Exception e) {
            throw new ReadRecordException("", e, 0, rowData);
        }
        return rowData;
    }

    @Override
    protected void closeInternal() {
        consumer.shutdown();
    }

    @Override
    public boolean reachedEnd() {
        return false;
    }

    public void setCmqConf(CmqConf CmqConf) {
        this.cmqConf = CmqConf;
    }

    public CmqConf getCmqConf() {
        return cmqConf;
    }
}
