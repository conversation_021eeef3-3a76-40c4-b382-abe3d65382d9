<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>chunjun</artifactId>
		<groupId>com.dtstack.chunjun</groupId>
		<version>2.0-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>chunjun-connectors</artifactId>
	<name>ChunJun : Connectors</name>
	<packaging>pom</packaging>

	<properties>
		<connector.base.dir>connector</connector.base.dir>
		<connector.shading.prefix>com.dtstack.chunjun.connector.shaded</connector.shading.prefix>
	</properties>

	<modules>
		<!--mock & print-->
		<!--<module>chunjun-connector-stream</module>-->

		<!--MQ-->
		<!--<module>chunjun-connector-kafka</module>-->
		<module>chunjun-connector-pulsar</module>
		<!--<module>chunjun-connector-emqx</module>-->
		<!--<module>chunjun-connector-rocketmq</module>-->
		<!--<module>chunjun-connector-rabbitmq</module>-->

		<!--RDB-->
		<module>chunjun-connector-jdbc-base</module>
		<!--<module>chunjun-connector-jdbc</module>-->
		<module>chunjun-connector-mysql</module>
		<module>chunjun-connector-hbase-1.4</module>
		<module>chunjun-connector-hbase-2</module>
		<module>chunjun-connector-hbase-base</module>





		<!--<module>chunjun-connector-hive-2</module>-->
		<!--<module>chunjun-connector-hive-3</module>-->
		<!--<module>chunjun-connector-elasticsearch7</module>-->
		<!--<module>chunjun-connector-mongodb</module>-->
		<!--<module>chunjun-connector-files</module>-->
<!--		<module>chunjun-connector-starrocks</module>-->

		<module>chunjun-connector-oracle</module>
		<!--<module>chunjun-connector-oracle9</module>-->
		<!--<module>chunjun-connector-sqlserver</module>-->
		<!--<module>chunjun-connector-db2</module>-->
		<!--<module>chunjun-connector-postgresql</module>-->
		<!--<module>chunjun-connector-gbase</module>-->
		<!--<module>chunjun-connector-kingbase</module>-->
		<!--<module>chunjun-connector-greenplum</module>-->
		<!--<module>chunjun-connector-dm</module>-->
		<module>chunjun-connector-clickhouse</module>
		<!--<module>chunjun-connector-saphana</module>-->
		<!--<module>chunjun-connector-doris</module>-->
		<!--<module>chunjun-connector-influxdb</module>-->
		<!--<module>chunjun-connector-polardb</module>-->
		<!--<module>chunjun-connector-greatdb</module>-->

		<!--<module>chunjun-connector-vertica11</module>-->
		<!--<module>chunjun-connector-tidb</module>-->
		<!--<module>chunjun-connector-sybase</module>-->
		<!--<module>chunjun-connector-cassandra</module>-->

		<!--File-->
<!--		<module>chunjun-connector-file</module>-->
<!--&lt;!&ndash;		<module>chunjun-connector-filesystem</module>&ndash;&gt;-->
		<module>chunjun-connector-mongodb</module>
<!--		<module>chunjun-connector-s3</module>-->
		<module>chunjun-connector-ftp</module>
		<module>chunjun-connector-s3</module>
<!--		<module>chunjun-connector-hudi</module>-->

<!--		&lt;!&ndash;NoSql&ndash;&gt;-->
<!--		&lt;!&ndash;<module>chunjun-connector-phoenix5</module>&ndash;&gt;-->
<!--		<module>chunjun-connector-hdfs-base</module>-->
<!--		<module>chunjun-connector-hbase-base</module>-->
<!--		<module>chunjun-connector-elasticsearch-base</module>-->
<!--		<module>chunjun-connector-elasticsearch7</module>-->
		<module>chunjun-connector-redis</module>
<!--		<module>chunjun-connector-solr</module>-->
		<module>chunjun-connector-kudu</module>
<!--		<module>chunjun-connector-opentsdb</module>-->
<!--		<module>chunjun-connector-odps</module>-->
		<module>chunjun-connector-starrocks</module>
<!--		<module>chunjun-connector-phoenix5</module>-->
<!--		<module>chunjun-connector-inceptor-base</module>-->

<!--		&lt;!&ndash;CDC&ndash;&gt;-->
<!--		<module>chunjun-connector-binlog</module>-->
<!--		<module>chunjun-connector-oraclelogminer</module>-->
<!--		<module>chunjun-connector-sqlservercdc</module>-->
<!--		<module>chunjun-connector-pgwal</module>-->

<!--		&lt;!&ndash;Network&ndash;&gt;-->
		<module>chunjun-connector-http</module>
        <module>chunjun-connector-kafka</module>
        <!--		<module>chunjun-connector-socket</module>-->
<!--		<module>chunjun-connector-websocket</module>-->

<!--		&lt;!&ndash;Distribute plugin, just for SYNC&ndash;&gt;-->
<!--		<module>chunjun-connector-mysqld</module>-->
<!--        <module>chunjun-connector-iceberg</module>-->
<!--        <module>chunjun-connector-phoenix4</module>-->
<!--        <module>chunjun-connector-tdengine</module>-->
<!--        <module>chunjun-connector-ckafka</module>-->
<!--        <module>chunjun-connector-cmq</module>-->
<!--        <module>chunjun-connector-oceanbase</module>-->
<!--        <module>chunjun-connector-oceanbasecdc</module>-->
<!--        <module>chunjun-connector-oushu</module>-->

	</modules>

	<repositories>
		<repository>
			<id>huaweicloudsdk</id>
			<url>https://repo.huaweicloud.com/repository/maven/huaweicloudsdk/</url>
		</repository>
	</repositories>
	<profiles>

		<profile>
			<id>all</id>
			<modules>
				<!-- default -->
				<!-- 使用 hadoop version 2.7.5 module-->
				<!--<module>chunjun-connector-hive</module>-->


<!--				-->
<!--				-->
<!--				-->
<!--				<module>chunjun-connector-hdfs</module>-->
<!--				<module>chunjun-connector-impala</module>-->
<!--				<module>chunjun-connector-hbase-base</module>-->
<!--				<module>chunjun-connector-hbase-1.4</module>-->
<!--				<module>chunjun-connector-hbase2</module>-->
<!--&lt;!&ndash;				<module>chunjun-connector-elasticsearch5</module>&ndash;&gt;-->
<!--				&lt;!&ndash;<module>chunjun-connector-elasticsearch6</module>&ndash;&gt;-->
<!--				&lt;!&ndash; tbds &ndash;&gt;-->
<!--				<module>chunjun-connector-hdfs-tbds</module>-->
<!--				<module>chunjun-connector-hive-tbds</module>-->
<!--				<module>chunjun-connector-hbase-tbds</module>-->
<!--				<module>chunjun-connector-kafka-tbds</module>-->
<!--				<module>chunjun-connector-tdsql</module>-->
<!--				&lt;!&ndash; cdp &ndash;&gt;-->
<!--				<module>chunjun-connector-hive3cdp</module>-->
<!--				&lt;!&ndash; tdh &ndash;&gt;-->
<!--				<module>chunjun-connector-inceptor</module>-->
<!--				<module>chunjun-connector-inceptor-hyperbase</module>-->
<!--				<module>chunjun-connector-hyperbase</module>-->
<!--				&lt;!&ndash;huawei&ndash;&gt;-->
<!--				&lt;!&ndash;<module>chunjun-connector-kafka-huawei</module>&ndash;&gt;-->
<!--				<module>chunjun-connector-hdfs-huawei</module>-->
<!--				<module>chunjun-connector-hbase-huawei</module>-->
<!--				<module>chunjun-connector-elasticsearch7-huawei</module>-->
			</modules>
		</profile>

		<!-- huawei -->
		<profile>
			<id>none</id>
		</profile>


		<!-- apache hadoop -->
		<profile>
			<id>default</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<modules>
				<!-- 使用 hadoop version 2.7.5 module-->
				<!--<module>chunjun-connector-hdfs</module>-->
				<!--<module>chunjun-connector-hive</module>-->
				<!--<module>chunjun-connector-impala</module>-->
				<!--<module>chunjun-connector-hbase-1.4</module>-->
				<!--<module>chunjun-connector-hbase2</module>-->
			</modules>
		</profile>

		<!-- tbds -->
		<profile>
			<id>tbds</id>
			<modules>
				<!--<module>chunjun-connector-hdfs-tbds</module>-->
				<!--<module>chunjun-connector-hive-tbds</module>-->
				<!--<module>chunjun-connector-hbase-tbds</module>-->
				<!--<module>chunjun-connector-kafka-tbds</module>-->
				<!--<module>chunjun-connector-tdsql</module>-->
			</modules>
		</profile>

		<!-- cdp -->
		<profile>
			<id>cdp</id>
			<modules>
				<module>chunjun-connector-hive3cdp</module>
			</modules>
		</profile>

		<!-- tdh -->
		<profile>
			<id>tdh</id>
			<modules>
				<!--<module>chunjun-connector-inceptor</module>-->
				<!--<module>chunjun-connector-argodb</module>-->
				<!--<module>chunjun-connector-hyperbase</module>-->
			</modules>
		</profile>

		<!-- huawei -->
		<profile>
			<id>huawei</id>
			<properties>
				<!-- TODO 要再复制出来一个插件 -->
				<!--<kafka.version>1.1.0-mrs-2.0</kafka.version>-->
				<!--<kafka.version>1.1.0-mrs-2.0</kafka.version>-->
			</properties>
			<modules>
				<!--huawei-->
				<!--<module>chunjun-connector-kafka-huawei</module>-->
				<!--<module>chunjun-connector-hbase-huawei</module>-->
				<!--<module>chunjun-connector-hdfs-huawei</module>-->
				<!--<module>chunjun-connector-elasticsearch7-huawei</module>-->
			</modules>
		</profile>

	</profiles>

	<dependencies>
		<dependency>
			<groupId>com.dtstack.chunjun</groupId>
			<artifactId>chunjun-core</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<groupId>ch.qos.logback</groupId>
					<artifactId>logback-classic</artifactId>
				</exclusion>
				<exclusion>
					<groupId>ch.qos.logback</groupId>
					<artifactId>logback-core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.dtstack.chunjun</groupId>
			<artifactId>chunjun-common</artifactId>
			<version>${project.version}</version>
			<scope>provided</scope>
		</dependency>
	</dependencies>
	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<artifactId>maven-antrun-plugin</artifactId>
					<executions>
						<execution>
							<id>copy-resources</id>
							<!-- here the phase you need -->
							<phase>package</phase>
							<goals>
								<goal>run</goal>
							</goals>
							<configuration>
								<tasks>
									<copy todir="${basedir}/../../${dist.dir}/${connector.base.dir}/${connector.dir}/"
										  file="${basedir}/target/${project.artifactId}-${project.version}.jar"/>
									<move file="${basedir}/../../${dist.dir}/${connector.base.dir}/${connector.dir}/${project.artifactId}-${project.version}.jar"
										  tofile="${basedir}/../../${dist.dir}/${connector.base.dir}/${connector.dir}/${project.artifactId}-${git.branch}.jar"/>
									<delete>
										<fileset dir="${basedir}/../../${dist.dir}/${connector.base.dir}/${connector.dir}/"
												 includes="${project.artifactId}-*.jar"
												 excludes="${project.artifactId}-${git.branch}.jar"/>
									</delete>
								</tasks>
							</configuration>
						</execution>
					</executions>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>
</project>
