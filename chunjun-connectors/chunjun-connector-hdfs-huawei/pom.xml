<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>chunjun-connectors</artifactId>
		<groupId>com.dtstack.chunjun</groupId>
		<version>2.0-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>chunjun-connector-hdfs-huawei</artifactId>
	<name>ChunJun : Connectors : HDFS-HUAWEI</name>

	<properties>
		<maven.compiler.source>${target.java.version}</maven.compiler.source>
		<maven.compiler.target>${target.java.version}</maven.compiler.target>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<hive.version>3.1.0-hw-ei-310003</hive.version>
		<commons-crypto.version>1.0.0-hw-20191105</commons-crypto.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.apache.hive</groupId>
			<artifactId>hive-exec</artifactId>
			<version>${hive.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.hadoop</groupId>
					<artifactId>hadoop-hdfs-client</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>calcite-core</artifactId>
					<groupId>org.apache.calcite</groupId>
				</exclusion>
				<exclusion>
					<artifactId>calcite-avatica</artifactId>
					<groupId>org.apache.calcite</groupId>
				</exclusion>
				<exclusion>
					<artifactId>derby</artifactId>
					<groupId>org.apache.derby</groupId>
				</exclusion>
				<exclusion>
					<groupId>org.xerial.snappy</groupId>
					<artifactId>snappy-java</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-logging</artifactId>
					<groupId>commons-logging</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-lang</artifactId>
					<groupId>commons-lang</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-cli</artifactId>
					<groupId>commons-cli</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-io</artifactId>
					<groupId>commons-io</groupId>
				</exclusion>
				<exclusion>
					<artifactId>junit</artifactId>
					<groupId>junit</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-codec</artifactId>
					<groupId>commons-codec</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-httpclient</artifactId>
					<groupId>commons-httpclient</groupId>
				</exclusion>
				<exclusion>
					<artifactId>groovy-all</artifactId>
					<groupId>org.codehaus.groovy</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-crypto</artifactId>
					<groupId>org.apache.commons</groupId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.hive</groupId>
					<artifactId>hive-metastore</artifactId>
				</exclusion>

				<exclusion>
					<artifactId>jline</artifactId>
					<groupId>jline</groupId>
				</exclusion>
				<exclusion>
					<artifactId>leveldbjni-all</artifactId>
					<groupId>org.fusesource.leveldbjni</groupId>
				</exclusion>
				<exclusion>
					<artifactId>gsjdbc4</artifactId>
					<groupId>com.huawei.gsjdbc4.bigdata</groupId>
				</exclusion>
				<exclusion>
					<artifactId>sqlline</artifactId>
					<groupId>sqlline</groupId>
				</exclusion>
				<exclusion>
					<artifactId>mssql-jdbc</artifactId>
					<groupId>com.microsoft.sqlserver</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-shaded-gson</artifactId>
					<groupId>org.apache.hbase.thirdparty</groupId>
				</exclusion>
				<exclusion>
					<artifactId>gson</artifactId>
					<groupId>com.google.code.gson</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hadoop-hdfs</artifactId>
					<groupId>org.apache.hadoop</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hive</groupId>
			<artifactId>hive-serde</artifactId>
			<version>${hive.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.hadoop</groupId>
					<artifactId>hadoop-yarn-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.xerial.snappy</groupId>
					<artifactId>snappy-java</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpcore</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-lang</artifactId>
					<groupId>commons-lang</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-cli</artifactId>
					<groupId>commons-cli</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-logging</artifactId>
					<groupId>commons-logging</groupId>
				</exclusion>
				<exclusion>
					<artifactId>apache-log4j-extras</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-codec</artifactId>
					<groupId>commons-codec</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jackson-core-asl</artifactId>
					<groupId>org.codehaus.jackson</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jackson-mapper-asl</artifactId>
					<groupId>org.codehaus.jackson</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jsr305</artifactId>
					<groupId>com.google.code.findbugs</groupId>
				</exclusion>
				<exclusion>
					<artifactId>parquet-hadoop-bundle</artifactId>
					<groupId>com.twitter</groupId>
				</exclusion>
				<exclusion>
					<groupId>jline</groupId>
					<artifactId>jline</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-crypto</artifactId>
			<version>${commons-crypto.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.parquet</groupId>
			<artifactId>parquet-hadoop</artifactId>
			<version>1.11.1</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<artifactId>groovy-all</artifactId>
			<groupId>org.codehaus.groovy</groupId>
			<version>2.4.4</version>
		</dependency>


		<!--for PARQUET-136 https://issues.apache.org/jira/browse/PARQUET-136 -->
		<dependency>
			<groupId>com.twitter</groupId>
			<artifactId>parquet-hadoop-bundle</artifactId>
			<version>1.6.0</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-parquet</artifactId>
			<version>${flink.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-orc</artifactId>
			<version>${flink.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-json</artifactId>
			<version>${flink.version}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.dtstack.chunjun</groupId>
			<artifactId>chunjun-connector-hdfs-base</artifactId>
			<version>2.0-SNAPSHOT</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<filters>
								<filter>
									<!--<artifact>org.apache.hive:hive-exec</artifact>-->
									<!--<excludes>-->
									<!--	<exclude>parquet/**</exclude>-->
									<!--</excludes>-->
								</filter>
								<filter>
									<artifact>*:*</artifact>
									<excludes>
										<exclude>META-INF/*.SF</exclude>
										<exclude>META-INF/*.DSA</exclude>
										<exclude>META-INF/*.RSA</exclude>
									</excludes>
								</filter>
							</filters>
							<relocations>
								<relocation>
									<pattern>org.apache.http</pattern>
									<shadedPattern>shaded.hdfs.org.apache.http</shadedPattern>
								</relocation>
								<relocation>
									<pattern>org.apache.hadoop.hive.ql.io.orc.OrcSplit</pattern>
									<shadedPattern>shaded.hdfshuawei.org.apache.hadoop.hive.ql.io.orc.OrcSplit</shadedPattern>
								</relocation>
							</relocations>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-resources</id>
						<!-- here the phase you need -->
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<tasks>
								<copy todir="${basedir}/../../${dist.dir}/connector/huaweihdfs"
									  file="${basedir}/target/${project.artifactId}-${project.version}.jar"/>
								<!--suppress UnresolvedMavenProperty -->
								<move file="${basedir}/../../${dist.dir}/connector/huaweihdfs/${project.artifactId}-${project.version}.jar"
									  tofile="${basedir}/../../${dist.dir}/connector/huaweihdfs/${project.artifactId}-${git.branch}.jar"/>
								<delete>
									<!--suppress UnresolvedMavenProperty -->
									<fileset dir="${basedir}/../../${dist.dir}/connector/huaweihdfs/"
											 includes="${project.artifactId}-*.jar"
											 excludes="${project.artifactId}-${git.branch}.jar"/>
								</delete>
							</tasks>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
