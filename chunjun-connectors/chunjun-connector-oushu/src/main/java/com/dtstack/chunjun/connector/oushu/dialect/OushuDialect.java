/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.oushu.dialect;

import com.dtstack.chunjun.conf.FlinkxCommonConf;
import com.dtstack.chunjun.connector.jdbc.conf.JdbcConf;
import com.dtstack.chunjun.connector.jdbc.statement.FieldNamedPreparedStatement;
import com.dtstack.chunjun.connector.oushu.converter.OushuRawTypeMapper;
import com.dtstack.chunjun.connector.oushu.converter.OushuSqlConverter;
import com.dtstack.chunjun.connector.oushu.converter.OushuSyncConverter;
import com.dtstack.chunjun.connector.postgresql.dialect.PostgresqlDialect;
import com.dtstack.chunjun.converter.AbstractRowConverter;
import com.dtstack.chunjun.converter.RawTypeMapper;

import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;

import io.vertx.sqlclient.Row;

import java.sql.ResultSet;
import java.util.Optional;

/**
 * company www.dtstack.com
 *
 * <AUTHOR>
 */
public class OushuDialect extends PostgresqlDialect {

    @Override
    public RawTypeMapper getRawTypeMapper() {
        return OushuRawTypeMapper::apply;
    }

    @Override
    public AbstractRowConverter<ResultSet, Row, FieldNamedPreparedStatement, LogicalType>
            getSyncConverter(RowType rowType, FlinkxCommonConf commonConf) {
        return new OushuSyncConverter(rowType, commonConf, getRawTypeMapper());
    }

    @Override
    public AbstractRowConverter<ResultSet, Row, FieldNamedPreparedStatement, LogicalType>
            getSqlConverter(RowType rowType, JdbcConf jdbcConf) {
        return new OushuSqlConverter(rowType);
    }

    @Override
    public boolean supportUpsert() {
        return true;
    }

    @Override
    public Optional<String> getUpsertStatement(
            String schema,
            String tableName,
            String[] fieldNames,
            String[] uniqueKeyFields,
            boolean allReplace) {
        return Optional.of(getInsertIntoStatement(schema, tableName, fieldNames));
    }

    @Override
    public Optional<String> getReplaceStatement(
            String schema, String tableName, String[] fieldNames) {
        return Optional.of(getInsertIntoStatement(schema, tableName, fieldNames));
    }
}
