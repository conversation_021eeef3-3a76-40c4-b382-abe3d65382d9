/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.oushu.sink;

import com.dtstack.chunjun.conf.SyncConf;
import com.dtstack.chunjun.connector.hdfs.enums.FileType;
import com.dtstack.chunjun.connector.hdfs.sink.HdfsSinkFactory;
import com.dtstack.chunjun.connector.hdfs.util.HdfsUtil;
import com.dtstack.chunjun.connector.jdbc.adapter.ConnectionAdapter;
import com.dtstack.chunjun.connector.jdbc.conf.ConnectionConf;
import com.dtstack.chunjun.connector.jdbc.conf.JdbcConf;
import com.dtstack.chunjun.connector.oushu.converter.OushuRawTypeMapper;
import com.dtstack.chunjun.connector.oushu.dialect.OushuDialect;
import com.dtstack.chunjun.converter.RawTypeMapper;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSink;
import org.apache.flink.table.data.RowData;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

/**
 * company www.dtstack.com
 *
 * <AUTHOR>
 */
public class OushuHdfsSinkFactory extends HdfsSinkFactory {

    private final JdbcConf jdbcConf;

    public OushuHdfsSinkFactory(SyncConf syncConf) {
        super(syncConf);
        Gson gson =
                new GsonBuilder()
                        .registerTypeAdapter(
                                ConnectionConf.class, new ConnectionAdapter("SinkConnectionConf"))
                        .addDeserializationExclusionStrategy(
                                new FieldNameExclusionStrategy("column"))
                        .create();
        com.dtstack.chunjun.common.util.GsonUtil.setTypeAdapter(gson);
        this.jdbcConf =
                gson.fromJson(gson.toJson(syncConf.getWriter().getParameter()), JdbcConf.class);
        jdbcConf.setColumn(syncConf.getWriter().getFieldList());
    }

    @Override
    public DataStreamSink<RowData> createSink(DataStream<RowData> dataSet, boolean hasData) {
        OushuHdfsOutputFormatBuilder builder = new OushuHdfsOutputFormatBuilder();
        builder.setJdbcConf(jdbcConf);
        builder.setHdfsConf(hdfsConf);
        builder.setJdbcDialect(new OushuDialect());
        builder.setRowConverter(
                HdfsUtil.createRowConverter(
                        isSyncJob, FileType.TEXT.name(), hdfsConf, getRawTypeMapper()));
        builder.setHasData(hasData);
        return createOutput(dataSet, builder.finish());
    }

    @Override
    public RawTypeMapper getRawTypeMapper() {
        return OushuRawTypeMapper::apply;
    }
}
