package com.dtstack.chunjun.connector.oushu.util;

import com.dtstack.chunjun.conf.FieldConf;
import com.dtstack.chunjun.connector.jdbc.conf.JdbcConf;
import com.dtstack.chunjun.sink.WriteMode;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.Statement;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

public class CommitterUtil {

    private static final Logger LOG = LoggerFactory.getLogger(CommitterUtil.class);

    public static void insertFromFile(
            String fileLocation, Connection connection, JdbcConf jdbcConf, String fieldDelimiter)
            throws Exception {
        String externalTable = UUID.randomUUID().toString();
        try (Statement statement = connection.createStatement()) {
            if (WriteMode.UPSERT.toString().equals(jdbcConf.getInsertSqlMode())) {
                statement.execute("set magma_conflict_mode = 'DO_REPLACE';");
            }
            String createTableSql =
                    buildCreateExternalTableSql(
                            externalTable, jdbcConf, fileLocation, fieldDelimiter);
            LOG.info("create external table: " + createTableSql);
            statement.execute(createTableSql);
            String insertSql = buildInsertSql(externalTable, jdbcConf);
            LOG.info("insert into internal table: " + insertSql);
            statement.execute(insertSql);
            String dropExternalSql = dropExternalTable(jdbcConf, externalTable);
            LOG.info("drop external table: " + dropExternalSql);
            statement.execute(dropExternalSql);
            connection.commit();
            LOG.info("File committed.");
        } catch (Exception e) {
            LOG.error("Failed to insert into table from external. ", e);
            connection.rollback();
            throw e;
        }
    }

    protected static String dropExternalTable(JdbcConf jdbcConf, String externalTable) {
        return "drop external table "
                + withQuotation(jdbcConf.getSchema())
                + "."
                + withQuotation(externalTable)
                + ";";
    }

    protected static String buildInsertSql(String externalTable, JdbcConf jdbcConf) {
        List<FieldConf> fields = jdbcConf.getColumn();
        String insertPart =
                fields.stream().map(FieldConf::getName).collect(Collectors.joining(","));

        return "insert into "
                + withQuotation(jdbcConf.getSchema())
                + "."
                + withQuotation(jdbcConf.getTable())
                + "("
                + insertPart
                + ")"
                + " select "
                + insertPart
                + " from "
                + withQuotation(jdbcConf.getSchema())
                + "."
                + withQuotation(externalTable)
                + ";";
    }

    private static String withQuotation(String val) {
        return String.format("\"%s\"", val);
    }

    protected static String buildCreateExternalTableSql(
            String externalTable, JdbcConf jdbcConf, String fileLocation, String fieldDelimiter) {
        StringBuilder sb = new StringBuilder();
        sb.append("create readable external table ");
        sb.append(withQuotation(jdbcConf.getSchema()));
        sb.append(".");
        sb.append(withQuotation(externalTable));
        List<FieldConf> fields = jdbcConf.getColumn();
        sb.append("(");
        for (int i = 0; i < fields.size(); ++i) {
            FieldConf fieldConf = fields.get(i);
            sb.append(fieldConf.getName()).append(" ").append(fieldConf.getType()).append(",");
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append(") location('");
        sb.append(fileLocation);
        sb.append("') format 'text' (DELIMITER '");
        sb.append(fieldDelimiter);
        sb.append("');");
        return sb.toString();
    }
}
