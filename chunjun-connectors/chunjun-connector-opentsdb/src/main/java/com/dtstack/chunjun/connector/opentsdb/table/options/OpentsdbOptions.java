package com.dtstack.chunjun.connector.opentsdb.table.options;

import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.configuration.ConfigOptions;

public class OpentsdbOptions {

    public static final ConfigOption<String> URL =
            ConfigOptions.key("url").stringType().noDefaultValue();

    public static final ConfigOption<String> METRIC =
            ConfigOptions.key("metric").stringType().noDefaultValue();

    public static final ConfigOption<String> START =
            ConfigOptions.key("start").stringType().noDefaultValue();

    public static final ConfigOption<String> END =
            ConfigOptions.key("end").stringType().noDefaultValue();

    public static final ConfigOption<String> FILTERS =
            ConfigOptions.key("filters").stringType().noDefaultValue();
}
