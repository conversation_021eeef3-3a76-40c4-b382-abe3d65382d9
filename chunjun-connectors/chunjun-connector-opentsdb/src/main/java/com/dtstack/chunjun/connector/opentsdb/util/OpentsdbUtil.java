/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.opentsdb.util;

import com.dtstack.chunjun.connector.opentsdb.module.DataPoint4TSDB;
import com.dtstack.chunjun.common.util.DateUtil;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/12 22:08 星期一
 * @email <EMAIL>
 * @company www.dtstack.com
 */
public class OpentsdbUtil {

    private static final Logger LOG = LoggerFactory.getLogger(OpentsdbUtil.class);

    private static final String TIME_ZONE = "GMT+8";

    private static final String STANDARD_DATETIME_FORMAT = "standardDatetimeFormatter";

    private static final String STANDARD_DATETIME_FORMAT_FOR_MILLISECOND =
            "standardDatetimeFormatterForMillisecond";

    private static final String UN_STANDARD_DATETIME_FORMAT = "unStandardDatetimeFormatter";

    private static final String DATE_FORMAT = "dateFormatter";

    private static final String TIME_FORMAT = "timeFormatter";

    private static final String YEAR_FORMAT = "yearFormatter";

    private static final String START_TIME = "1970-01-01";

    public static final String DATE_REGEX = "(?i)date";

    public static final String TIMESTAMP_REGEX = "(?i)timestamp";

    public static final String DATETIME_REGEX = "(?i)datetime";

    public static final int LENGTH_SECOND = 10;
    public static final int LENGTH_MILLISECOND = 13;
    public static final int LENGTH_MICROSECOND = 16;
    public static final int LENGTH_NANOSECOND = 19;

    public static ThreadLocal<Map<String, SimpleDateFormat>> datetimeFormatter =
            ThreadLocal.withInitial(
                    () -> {
                        TimeZone timeZone = TimeZone.getTimeZone(TIME_ZONE);

                        Map<String, SimpleDateFormat> formatterMap = new HashMap<>();
                        SimpleDateFormat standardDatetimeFormatter =
                                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        standardDatetimeFormatter.setTimeZone(timeZone);
                        formatterMap.put(STANDARD_DATETIME_FORMAT, standardDatetimeFormatter);

                        SimpleDateFormat unStandardDatetimeFormatter =
                                new SimpleDateFormat("yyyyMMddHHmmss");
                        unStandardDatetimeFormatter.setTimeZone(timeZone);
                        formatterMap.put(UN_STANDARD_DATETIME_FORMAT, unStandardDatetimeFormatter);

                        SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
                        dateFormatter.setTimeZone(timeZone);
                        formatterMap.put(DATE_FORMAT, dateFormatter);

                        SimpleDateFormat timeFormatter = new SimpleDateFormat("HH:mm:ss");
                        timeFormatter.setTimeZone(timeZone);
                        formatterMap.put(TIME_FORMAT, timeFormatter);

                        SimpleDateFormat yearFormatter = new SimpleDateFormat("yyyy");
                        yearFormatter.setTimeZone(timeZone);
                        formatterMap.put(YEAR_FORMAT, yearFormatter);

                        SimpleDateFormat standardDatetimeFormatterOfMillisecond =
                                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
                        standardDatetimeFormatterOfMillisecond.setTimeZone(timeZone);
                        formatterMap.put(
                                STANDARD_DATETIME_FORMAT_FOR_MILLISECOND,
                                standardDatetimeFormatterOfMillisecond);

                        return formatterMap;
                    });

    private OpentsdbUtil() {}

    public static long stringToTimestamp(String strDate) {
        if (strDate == null || strDate.trim().length() == 0) {
            throw new RuntimeException("can't parse date : " + strDate);
        }
        ParseException e;
        try {
            return datetimeFormatter.get().get(STANDARD_DATETIME_FORMAT).parse(strDate).getTime();
        } catch (ParseException ignored) {
            e = ignored;
        }

        try {
            return datetimeFormatter
                    .get()
                    .get(UN_STANDARD_DATETIME_FORMAT)
                    .parse(strDate)
                    .getTime();
        } catch (ParseException ignored) {
            e = ignored;
        }
        LOG.error(
                "can't parse date : {} , only support format [yyyy-MM-dd HH:mm:ss] or [yyyyMMddHHmmss]",
                ExceptionUtil.getErrorMessage(e));
        throw new RuntimeException(
                "can't parse date : "
                        + strDate
                        + ", only support format [yyyy-MM-dd HH:mm:ss] or [yyyyMMddHHmmss]");
    }

    public static Object getValueWithColumnName(
            DataPoint4TSDB datapoint, String columnName, String columnFormat)
            throws IllegalArgumentException {
        Object value = null;
        switch (columnName.toUpperCase()) {
            case "METRIC":
                value = datapoint.getMetric().trim();
                break;
            case "TS":
            case "TIMESTAMP":
                if (StringUtils.isNotBlank(columnFormat)) {
                    java.sql.Date date1 = DateUtil.columnToDate(datapoint.getTimestamp(), null);
                    String timestampToString = DateUtil.timestampToString(date1);
                    SimpleDateFormat format = new SimpleDateFormat(columnFormat);
                    value =
                            format.format(
                                    DateUtil.columnToTimestampByFormat(timestampToString, format));
                } else {
                    value = String.valueOf(datapoint.getTimestamp());
                }
                break;
            case "METRIC_VALUE":
            case "VALUE":
                value = String.valueOf(datapoint.getValue()).trim();
                break;
            case "TAGS":
                value = datapoint.getTags().toString();
                break;
            default:
                throw new IllegalArgumentException("Unsupported Column Name: " + columnName);
        }
        return value;
    }
}
