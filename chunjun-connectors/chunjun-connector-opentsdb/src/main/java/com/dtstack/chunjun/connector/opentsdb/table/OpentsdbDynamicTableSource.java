package com.dtstack.chunjun.connector.opentsdb.table;

import com.dtstack.chunjun.conf.FieldConf;
import com.dtstack.chunjun.connector.opentsdb.conf.OpentsdbConf;
import com.dtstack.chunjun.connector.opentsdb.converter.OpentsdbSqlConverter;
import com.dtstack.chunjun.connector.opentsdb.source.OpentsdbInputFormatBuilder;
import com.dtstack.chunjun.source.DtInputFormatSourceFunction;
import com.dtstack.chunjun.table.connector.source.ParallelSourceFunctionProvider;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.connector.ChangelogMode;
import org.apache.flink.table.connector.source.DynamicTableSource;
import org.apache.flink.table.connector.source.ScanTableSource;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.runtime.typeutils.InternalTypeInfo;
import org.apache.flink.table.types.logical.RowType;

import java.util.List;
import java.util.stream.Collectors;

public class OpentsdbDynamicTableSource implements ScanTableSource {

    private final OpentsdbConf conf;

    private final TableSchema schema;

    public OpentsdbDynamicTableSource(OpentsdbConf conf, TableSchema schema) {
        this.conf = conf;
        this.schema = schema;
    }

    @Override
    public ChangelogMode getChangelogMode() {
        return ChangelogMode.insertOnly();
    }

    @Override
    public ScanRuntimeProvider getScanRuntimeProvider(ScanContext runtimeProviderContext) {
        final RowType rowType = (RowType) schema.toRowDataType().getLogicalType();
        TypeInformation<RowData> typeInformation = InternalTypeInfo.of(rowType);

        OpentsdbInputFormatBuilder builder = new OpentsdbInputFormatBuilder();
        builder.setRowConverter(new OpentsdbSqlConverter(rowType, conf));
        builder.setOpentsdbConf(conf);

        List<FieldConf> fieldConfList =
                schema.getTableColumns().stream()
                        .map(
                                f -> {
                                    FieldConf fieldConf = new FieldConf();
                                    fieldConf.setName(f.getName());
                                    String name = f.getType().getConversionClass().getName();
                                    String[] fieldType = name.split("\\.");
                                    String type = fieldType[fieldType.length - 1];
                                    fieldConf.setType(type);
                                    return fieldConf;
                                })
                        .collect(Collectors.toList());
        conf.setColumn(fieldConfList);

        return ParallelSourceFunctionProvider.of(
                new DtInputFormatSourceFunction<>(builder.finish(), typeInformation), false, null);
    }

    @Override
    public DynamicTableSource copy() {
        return new OpentsdbDynamicTableSource(conf, schema);
    }

    @Override
    public String asSummaryString() {
        return "OpentsdbDynamicTableSource";
    }
}
