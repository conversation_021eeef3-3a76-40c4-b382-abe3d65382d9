/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dtstack.chunjun.connector.opentsdb.module;

import com.dtstack.chunjun.connector.opentsdb.util.TSDBUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/8 19:52 星期四
 * @email <EMAIL>
 * @company www.dtstack.com
 */
public class OpentsdbConnection {

    private String address;

    public OpentsdbConnection(String address) {
        this.address = address;
    }

    public String address() {
        return address;
    }

    public String version() {
        return TSDBUtils.version(address);
    }

    public String config() {
        return TSDBUtils.config(address);
    }

    public String[] getSupportVersionPrefix() {
        return new String[] {"2.3"};
    }
}
