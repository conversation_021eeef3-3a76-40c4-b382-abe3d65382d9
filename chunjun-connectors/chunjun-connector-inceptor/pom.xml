<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.dtstack.chunjun</groupId>
		<artifactId>chunjun-connectors</artifactId>
		<version>2.0-SNAPSHOT</version>
	</parent>

	<artifactId>chunjun-connector-inceptor</artifactId>
	<name>ChunJun : Connectors : Inceptor</name>

	<properties>
		<maven.compiler.source>${target.java.version}</maven.compiler.source>
		<maven.compiler.target>${target.java.version}</maven.compiler.target>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>
	<dependencies>
		<dependency>
			<groupId>com.dtstack.chunjun</groupId>
			<artifactId>chunjun-connector-inceptor-base</artifactId>
			<version>${project.version}</version>
		</dependency>
		<!--		hadoop start-->
		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-hdfs</artifactId>
			<version>${hadoop.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-mapreduce-client-core</artifactId>
			<version>${hadoop.version}</version>
		</dependency>
		<!--		hadoop end-->
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<createDependencyReducedPom>false</createDependencyReducedPom>
							<artifactSet>
								<excludes>
									<exclude>com.data-artisans:*</exclude>
									<exclude>org.scala-lang:*</exclude>
									<exclude>io.netty:*</exclude>
								</excludes>
							</artifactSet>
							<filters>
								<filter>
									<artifact>*:*</artifact>
									<excludes>
										<exclude>META-INF/*.SF</exclude>
										<exclude>META-INF/*.DSA</exclude>
										<exclude>META-INF/*.RSA</exclude>
										<exclude>org/apache/hadoop/hive/ql/parse/oracle/*</exclude>
									</excludes>
								</filter>
							</filters>
							<relocations>
<!--								Solve perjob mode, inceptor->hdfs orcSplit class conflict-->
								<relocation>
									<pattern>org.apache.hadoop.hive</pattern>
									<shadedPattern>shaded.inceptor.org.apache.hadoop.hive</shadedPattern>
								</relocation>
								<relocation>
									<pattern>com.esotericsoftware.kryo</pattern>
									<shadedPattern>shade.inceptorwriter.com.esotericsoftware.kryo</shadedPattern>
								</relocation>
							</relocations>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-resources</id>
						<!-- here the phase you need -->
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<tasks>
								<copy todir="${basedir}/../../${dist.dir}/connector/inceptor"
									  file="${basedir}/target/${project.artifactId}-${project.version}.jar"/>
								<!--suppress UnresolvedMavenProperty -->
								<move
									file="${basedir}/../../${dist.dir}/connector/inceptor/${project.artifactId}-${project.version}.jar"
									tofile="${basedir}/../../${dist.dir}/connector/inceptor/${project.artifactId}-${git.branch}.jar"/>
								<delete>
									<!--suppress UnresolvedMavenProperty -->
									<fileset dir="${basedir}/../../${dist.dir}/connector/inceptor/"
											 includes="${project.artifactId}-*.jar"
											 excludes="${project.artifactId}-${git.branch}.jar"/>
								</delete>
							</tasks>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
