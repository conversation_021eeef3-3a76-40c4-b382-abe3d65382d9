package com.dtstack.chunjun.connector.inceptor.sink;

import com.dtstack.chunjun.conf.SyncConf;
import com.dtstack.chunjun.connector.inceptor.util.InceptorDbUtil;
import com.dtstack.chunjun.converter.RawTypeMapper;
import com.dtstack.chunjun.sink.SinkFactory;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSink;
import org.apache.flink.table.data.RowData;

import org.apache.commons.lang3.StringUtils;

public class InceptorSinkFactory extends SinkFactory {
    SinkFactory sinkFactory;

    public InceptorSinkFactory(SyncConf syncConf) {
        super(syncConf);
        boolean useJdbc =
                !StringUtils.isNotBlank(
                        syncConf.getWriter().getParameter().getOrDefault("path", "").toString());
        // orc,text,parquet to hdfs.
        // hyperbase,search,view to jdbc.
        if (useJdbc || InceptorDbUtil.checkTableFormat(syncConf.getWriter().getParameter())) {
            this.sinkFactory = new InceptorJdbcSinkFactory(syncConf);
        } else {
            this.sinkFactory = new InceptorHdfsSinkFactory(syncConf);
        }
    }

    @Override
    public DataStreamSink<?> createSink(DataStream<RowData> dataSet) {
        return sinkFactory.createSink(dataSet);
    }

    @Override
    public RawTypeMapper getRawTypeMapper() {
        return sinkFactory.getRawTypeMapper();
    }
}
