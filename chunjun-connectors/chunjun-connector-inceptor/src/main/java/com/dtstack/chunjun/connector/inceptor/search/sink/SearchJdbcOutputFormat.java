/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.inceptor.search.sink;

import com.dtstack.chunjun.common.security.SecurityUtil;
import com.dtstack.chunjun.connector.inceptor.conf.InceptorJdbcConf;
import com.dtstack.chunjun.connector.inceptor.util.InceptorDbUtil;
import com.dtstack.chunjun.connector.jdbc.sink.JdbcOutputFormat;
import com.dtstack.chunjun.connector.jdbc.util.JdbcUtil;
import com.dtstack.chunjun.enums.EWriteMode;
import com.dtstack.chunjun.enums.Semantic;
import com.dtstack.security.SecurityConfigBuilder;
import com.dtstack.security.SecurityContext;
import com.dtstack.security.filesystem.FileManager;

import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.UUID;

/** <AUTHOR> 2022/2/24 */
public class SearchJdbcOutputFormat extends JdbcOutputFormat {
    protected SecurityContext securityContext;

    @Override
    protected void openInternal(int taskNumber, int numTasks) {
        try {
            this.securityContext = initSecurityContext();
            dbConn = getConnection();
            // 默认关闭事务自动提交，手动控制事务
            if (Semantic.EXACTLY_ONCE == semantic) {
                dbConn.setAutoCommit(false);
            }
            if (!EWriteMode.INSERT.name().equalsIgnoreCase(jdbcConf.getMode())) {
                throw new FlinkxRuntimeException(
                        String.format("inceptor search not support %s mode", jdbcConf.getMode()));
            }

            buildStatementExecutor();
            LOG.info("subTask[{}}] wait finished", taskNumber);
        } catch (SQLException sqe) {
            throw new IllegalArgumentException("open() failed.", sqe);
        } finally {
            JdbcUtil.commit(dbConn);
        }
    }

    @Override
    public Connection getConnection() {
        return securityContext.execute(
                null,
                t -> InceptorDbUtil.getConnection((InceptorJdbcConf) jdbcConf, null, null, null));
    }

    @Override
    public void closeInternal() {
        super.closeInternal();
        if (securityContext != null) {
            try {
                securityContext.close();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            securityContext = null;
        }
    }

    protected SecurityContext initSecurityContext() {
        if (this.securityContext == null) {
            SecurityConfigBuilder securityConfigBuilder = new SecurityConfigBuilder();

            String localRootPath =
                    FileManager.LOCAL_CACHE_DIR
                            + File.separator
                            + (StringUtils.isNotBlank(jobId) ? jobId : UUID.randomUUID())
                            + File.separator
                            + taskNumber;
            SecurityUtil.addKerberosConfig(
                    securityConfigBuilder, ((InceptorJdbcConf) jdbcConf).getHadoopConfig());
            SecurityUtil.addFileManagerConfig(
                    securityConfigBuilder,
                    ((InceptorJdbcConf) jdbcConf).getHadoopConfig(),
                    localRootPath);

            this.securityContext =
                    SecurityUtil.getSecurityContext(
                            securityConfigBuilder,
                            context == null
                                    ? SecurityUtil.createDistributedCacheFromContextClassLoader()
                                    : context.getDistributedCache());
        }
        return this.securityContext;
    }
}
