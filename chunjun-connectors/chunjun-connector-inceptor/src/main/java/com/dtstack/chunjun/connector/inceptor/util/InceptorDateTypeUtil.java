/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.inceptor.util;

import com.dtstack.chunjun.connector.jdbc.util.increment.IncrementKeyUtil;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hive.common.type.HiveDate;

import java.math.BigInteger;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;

/** <AUTHOR> 2022/6/23 */
public class InceptorDateTypeUtil extends IncrementKeyUtil<HiveDate, BigInteger> {

    @Override
    public HiveDate getSqlValueFromRs(ResultSet rs, int index) throws SQLException {
        return (HiveDate) rs.getObject(index);
    }

    @Override
    public HiveDate getSqlValueFromRs(ResultSet rs, String column) throws SQLException {
        return (HiveDate) rs.getObject(column);
    }

    @Override
    public void setPsWithSqlValue(PreparedStatement ps, int index, HiveDate value)
            throws SQLException {
        ps.setDate(index, value);
    }

    @Override
    public void setPsWithLocationStr(PreparedStatement ps, int index, String value)
            throws SQLException {
        ps.setDate(index, new HiveDate(Long.parseLong(value)));
    }

    @Override
    public BigInteger transToLocationValueInternal(Object value) {
        return BigInteger.valueOf(((HiveDate) value).getTime());
    }

    @Override
    public String transLocationStrToStatementValue(String locationStr) {
        return String.format("'%s'", new HiveDate(Long.parseLong(locationStr)));
    }

    @Override
    public String transToStatementValueInternal(Object sqlValue) {
        return String.format("'%s'", sqlValue);
    }

    @Override
    public HiveDate transLocationStrToSqlValue(String locationStr) {
        return new HiveDate(Long.parseLong(locationStr));
    }

    @Override
    public BigInteger getNullDefaultLocationValue() {
        return BigInteger.valueOf(Long.MIN_VALUE);
    }

    @Override
    public String checkAndFormatLocationStr(String originLocationStr) {
        if (!StringUtils.isNumeric(originLocationStr)) {
            try {
                return String.valueOf(Timestamp.valueOf(originLocationStr).getTime());
            } catch (Exception e) {
                throw new FlinkxRuntimeException(
                        String.format(
                                "failed cast locationStr[%s] to HiveDate,"
                                        + "Please check location is a valid timestamp string like yyyy-MM-dd HH:mm:ss",
                                originLocationStr));
            }
        }
        return originLocationStr;
    }
}
