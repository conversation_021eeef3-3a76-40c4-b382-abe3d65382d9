/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.inceptor.source;

import com.dtstack.chunjun.common.security.SecurityUtil;
import com.dtstack.chunjun.connector.inceptor.conf.InceptorJdbcConf;
import com.dtstack.chunjun.connector.inceptor.util.InceptorDbUtil;
import com.dtstack.chunjun.connector.jdbc.source.JdbcInputFormat;
import com.dtstack.chunjun.throwable.NoRestartException;
import com.dtstack.security.SecurityConfigBuilder;
import com.dtstack.security.SecurityContext;
import com.dtstack.security.filesystem.FileManager;

import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class InceptorJdbcInputFormat extends JdbcInputFormat {

    private SecurityContext securityContext;

    private Map<String, Object> hadoopConfig = new HashMap<>();

    @Override
    public void openInputFormat() throws IOException {
        initSecurityContext();
        securityContext.execute(
                () -> {
                    try {
                        super.openInputFormat();
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    return null;
                });
    }

    @Override
    public void closeInputFormat() {
        if (securityContext != null) {
            try {
                securityContext.close();
                securityContext = null;
            } catch (Exception e) {
                LOG.warn("close security context error", e);
            }
        }
        super.closeInputFormat();
    }

    @Override
    protected Connection getConnection() throws SQLException {
        initSecurityContext();
        return securityContext.execute(
                () -> {
                    try {
                        return InceptorDbUtil.getConnection(
                                (InceptorJdbcConf) jdbcConf, null, null, null);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                });
    }

    protected void initSecurityContext() {
        try {
            if (this.securityContext == null) {
                SecurityConfigBuilder securityConfigBuilder = new SecurityConfigBuilder();

                String localRootPath =
                        FileManager.LOCAL_CACHE_DIR
                                + File.separator
                                + (StringUtils.isNotBlank(jobId) ? jobId : UUID.randomUUID())
                                + File.separator
                                + indexOfSubTask;
                SecurityUtil.addKerberosConfig(securityConfigBuilder, hadoopConfig);
                SecurityUtil.addFileManagerConfig(
                        securityConfigBuilder, hadoopConfig, localRootPath);

                this.securityContext =
                        SecurityUtil.getSecurityContext(
                                securityConfigBuilder,
                                context == null ? null : context.getDistributedCache());
            }
        } catch (Exception e) {
            throw new NoRestartException("init securityContext failed ", e);
        }
    }
}
