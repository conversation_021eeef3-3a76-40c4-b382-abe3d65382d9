{"_comment": "支持 tdengine 增量同步的例子. ", "job": {"content": [{"reader": {"parameter": {"_comment_start": "增量同步配置开始", "startLocation": "2", "increColumn": "id", "_comment_end": "增量同步配置结束", "username": "root", "password": "password", "fetchSize": "1", "column": [{"name": "ts", "type": "TIMESTAMP"}, {"name": "id", "type": "int"}], "connection": [{"schema": "wz_test", "jdbcUrl": ["jdbc:TAOS-RS://tdengine01:6041/wz_test?user=root&password=password&timezone=UTC-8&charset=UTF-8&locale=en_US.UTF-8"], "table": ["startlocation"]}]}, "name": "tdenginereader"}, "writer": {"parameter": {"print": true}, "name": "streamwriter"}}], "setting": {"speed": {"channel": 1, "bytes": 0}}}}