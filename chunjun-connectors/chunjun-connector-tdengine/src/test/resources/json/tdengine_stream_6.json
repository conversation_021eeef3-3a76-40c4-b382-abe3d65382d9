{"job": {"content": [{"reader": {"parameter": {"password": "password", "fetchSize": 4096, "column": [{"name": "ts", "type": "timestamp"}, {"name": "c0", "type": "FLOAT"}, {"name": "c1", "type": "INT"}, {"name": "c2", "type": "FLOAT"}, {"name": "c3", "type": "bigint"}, {"name": "c4", "type": "int"}, {"name": "c5", "type": "int"}, {"name": "c6", "type": "int"}, {"name": "c7", "type": "int"}, {"name": "c8", "type": "int"}, {"name": "c9", "type": "int"}, {"name": "c10", "type": "int"}, {"name": "c11", "type": "int"}, {"name": "c12", "type": "int"}, {"name": "c13", "type": "int"}, {"name": "c14", "type": "int"}, {"name": "c15", "type": "int"}, {"name": "c16", "type": "int"}, {"name": "c17", "type": "int"}, {"name": "c18", "type": "int"}, {"name": "c19", "type": "int"}, {"name": "c20", "type": "int"}, {"name": "c21", "type": "int"}, {"name": "c22", "type": "int"}, {"name": "c23", "type": "int"}, {"name": "c24", "type": "int"}, {"name": "c25", "type": "int"}, {"name": "c26", "type": "int"}, {"name": "c27", "type": "int"}, {"name": "c28", "type": "int"}, {"name": "c29", "type": "int"}, {"name": "c30", "type": "int"}, {"name": "c31", "type": "int"}, {"name": "c32", "type": "int"}, {"name": "c33", "type": "int"}, {"name": "c34", "type": "int"}, {"name": "c35", "type": "int"}, {"name": "c36", "type": "int"}, {"name": "c37", "type": "int"}, {"name": "c38", "type": "int"}, {"name": "c39", "type": "int"}, {"name": "c40", "type": "int"}, {"name": "c41", "type": "int"}, {"name": "c42", "type": "int"}, {"name": "c43", "type": "int"}, {"name": "c44", "type": "int"}, {"name": "c45", "type": "int"}, {"name": "c46", "type": "int"}, {"name": "c47", "type": "int"}, {"name": "c48", "type": "int"}, {"name": "c49", "type": "int"}, {"name": "c50", "type": "int"}, {"name": "c51", "type": "int"}, {"name": "c52", "type": "int"}, {"name": "c53", "type": "int"}, {"name": "c54", "type": "int"}, {"name": "c55", "type": "int"}, {"name": "c56", "type": "int"}, {"name": "c57", "type": "int"}, {"name": "c58", "type": "int"}, {"name": "c59", "type": "int"}, {"name": "c60", "type": "int"}, {"name": "c61", "type": "int"}, {"name": "c62", "type": "int"}, {"name": "c63", "type": "int"}, {"name": "c64", "type": "int"}, {"name": "c65", "type": "int"}, {"name": "c66", "type": "int"}, {"name": "c67", "type": "int"}, {"name": "c68", "type": "int"}, {"name": "c69", "type": "int"}, {"name": "c70", "type": "int"}, {"name": "c71", "type": "int"}, {"name": "c72", "type": "int"}, {"name": "c73", "type": "int"}, {"name": "c74", "type": "int"}, {"name": "c75", "type": "int"}, {"name": "c76", "type": "int"}, {"name": "c77", "type": "int"}, {"name": "c78", "type": "int"}, {"name": "c79", "type": "int"}, {"name": "c80", "type": "int"}, {"name": "c81", "type": "int"}, {"name": "c82", "type": "int"}, {"name": "c83", "type": "int"}, {"name": "c84", "type": "int"}, {"name": "c85", "type": "int"}, {"name": "c86", "type": "int"}, {"name": "c87", "type": "int"}, {"name": "c88", "type": "int"}, {"name": "c89", "type": "int"}, {"name": "c90", "type": "int"}, {"name": "c91", "type": "int"}, {"name": "c92", "type": "int"}, {"name": "c93", "type": "int"}, {"name": "c94", "type": "int"}, {"name": "c95", "type": "int"}, {"name": "c96", "type": "int"}, {"name": "c97", "type": "int"}, {"name": "c98", "type": "int"}, {"name": "c99", "type": "int"}], "connection": [{"schema": "benchmark", "password": "password", "jdbcUrl": ["jdbc:TAOS-RS://172.16.84.4:6041/benchmark?timezone=UTC-8&charset=UTF-8&locale=en_US.UTF-8&batchfetch=true"], "table": ["d0"], "username": "root"}], "splitPk": "ts", "splitStrategy": "range", "username": "root"}, "name": "tdenginereader"}, "writer": {"parameter": {"print": false}, "name": "streamwriter"}}], "setting": {"restore": {"maxRowNumForCheckpoint": 0, "isRestore": false, "restoreColumnName": "", "restoreColumnIndex": 0}, "log": {"path": "", "level": "debug", "pattern": "", "isLogger": false}, "errorLimit": {"record": 1}, "speed": {"bytes": 0, "channel": 4}}}}