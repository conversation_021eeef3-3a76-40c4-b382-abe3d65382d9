package com.dtstack.chunjun.connector.tdengine.source;

import com.dtstack.chunjun.connector.jdbc.source.JdbcInputFormat;
import com.dtstack.chunjun.connector.jdbc.source.JdbcInputSplit;
import com.dtstack.chunjun.connector.tdengine.util.TdengineUtil;

import java.util.List;

/**
 * company www.dtstack.com
 *
 * <AUTHOR>
 */
public class TdengineInputFormat extends JdbcInputFormat {

    @Override
    protected String buildQuerySqlBySplit(JdbcInputSplit jdbcInputSplit, List<String> whereList) {
        return TdengineUtil.buildQuerySqlBySplit(
                jdbcConf, jdbcDialect, whereList, columnNameList, jdbcInputSplit);
    }
}
