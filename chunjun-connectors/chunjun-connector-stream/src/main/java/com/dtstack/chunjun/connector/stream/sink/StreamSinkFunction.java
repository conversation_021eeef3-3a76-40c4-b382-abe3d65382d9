/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.stream.sink;

import com.dtstack.chunjun.connector.stream.conf.StreamConf;
import com.dtstack.chunjun.converter.AbstractRowConverter;

import org.apache.flink.api.connector.sink2.Sink;
import org.apache.flink.api.connector.sink2.SinkWriter;
import org.apache.flink.api.connector.sink2.WriterInitContext;
import org.apache.flink.table.data.RowData;

import java.io.IOException;

public class StreamSinkFunction implements Sink<RowData> {
    // streamSinkConf属性
    private StreamConf streamConf;

    /** 数据类型转换器 */
    protected AbstractRowConverter converter;

    public StreamSinkFunction(StreamConf streamConf, AbstractRowConverter converter) {
        this.streamConf = streamConf;
        this.converter = converter;
    }

    @Override
    public SinkWriter<RowData> createWriter(WriterInitContext context) throws IOException {
        return new StreamSinkOutputWrite(streamConf, converter);
    }
}
