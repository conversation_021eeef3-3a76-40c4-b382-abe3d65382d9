package com.dtstack.chunjun.connector.postgresql.converter;

import com.dtstack.chunjun.connector.jdbc.converter.JdbcSqlConverter;
import com.dtstack.chunjun.converter.IDeserializationConverter;

import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.StringData;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;

import io.vertx.core.buffer.impl.BufferImpl;
import io.vertx.sqlclient.Row;

import java.util.ArrayList;

public class PostgresqlRowConverter extends JdbcSqlConverter {
    private static final long serialVersionUID = 1L;

    protected ArrayList<IDeserializationConverter> toAsyncInternalConverters;

    public PostgresqlRowConverter(RowType rowType) {
        super(rowType);
        toAsyncInternalConverters = new ArrayList<>(rowType.getFieldCount());
        for (int i = 0; i < rowType.getFieldCount(); i++) {
            toAsyncInternalConverters.add(
                    wrapIntoNullableInternalConverter(
                            createAsyncInternalConverter(rowType.getTypeAt(i))));
        }
    }

    protected IDeserializationConverter createAsyncInternalConverter(LogicalType type) {
        switch (type.getTypeRoot()) {
            case BINARY:
            case VARBINARY:
                return val -> {
                    if (val instanceof BufferImpl) {
                        return ((BufferImpl) val).getBytes();
                    }
                    return (byte[]) val;
                };
            case CHAR:
            case VARCHAR:
                return val -> StringData.fromString(val.toString());
            default:
                return createInternalConverter(type);
        }
    }

    @Override
    public RowData toInternalLookup(Row row) throws Exception {
        GenericRowData genericRowData = new GenericRowData(rowType.getFieldCount());
        for (int pos = 0; pos < rowType.getFieldCount(); pos++) {
            Object field = row.getValue(pos);
            genericRowData.setField(pos, toAsyncInternalConverters.get(pos).deserialize(field));
        }
        return genericRowData;
    }
}
