/*
 *
 *  * Licensed to the Apache Software Foundation (ASF) under one
 *  * or more contributor license agreements.  See the NOTICE file
 *  * distributed with this work for additional information
 *  * regarding copyright ownership.  The ASF licenses this file
 *  * to you under the Apache License, Version 2.0 (the
 *  * "License"); you may not use this file except in compliance
 *  * with the License.  You may obtain a copy of the License at
 *  *
 *  *     http://www.apache.org/licenses/LICENSE-2.0
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  * See the License for the specific language governing permissions and
 *  * limitations under the License.
 *
 */

package com.dtstack.chunjun.connector.vertica11.conf;

import com.dtstack.chunjun.connector.jdbc.conf.JdbcLookupConf;

import java.util.Map;

/** <AUTHOR> on 2022/8/1. */
public class Vertica11LookupConf extends JdbcLookupConf {
    /** vertx pool size */
    protected int asyncPoolSize = 5;

    protected Map<String, Object> poolConf;

    public static Vertica11LookupConf build() {
        return new Vertica11LookupConf();
    }

    public Map<String, Object> getPoolConf() {
        return poolConf;
    }

    public Vertica11LookupConf setPoolConf(Map<String, Object> poolConf) {
        this.poolConf = poolConf;
        return this;
    }

    public int getAsyncPoolSize() {
        return asyncPoolSize;
    }

    public Vertica11LookupConf setAsyncPoolSize(int asyncPoolSize) {
        this.asyncPoolSize = asyncPoolSize;
        return this;
    }
}
