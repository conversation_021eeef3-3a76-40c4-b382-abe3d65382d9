/*
 *
 *  * Licensed to the Apache Software Foundation (ASF) under one
 *  * or more contributor license agreements.  See the NOTICE file
 *  * distributed with this work for additional information
 *  * regarding copyright ownership.  The ASF licenses this file
 *  * to you under the Apache License, Version 2.0 (the
 *  * "License"); you may not use this file except in compliance
 *  * with the License.  You may obtain a copy of the License at
 *  *
 *  *     http://www.apache.org/licenses/LICENSE-2.0
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  * See the License for the specific language governing permissions and
 *  * limitations under the License.
 *
 */

package com.dtstack.chunjun.connector.vertica11.sink;

import com.dtstack.chunjun.common.throwble.FlinkxRuntimeException;
import com.dtstack.chunjun.conf.FieldConf;
import com.dtstack.chunjun.conf.TypeConfig;
import com.dtstack.chunjun.connector.jdbc.sink.JdbcOutputFormat;
import com.dtstack.chunjun.connector.jdbc.util.JdbcUtil;
import com.dtstack.chunjun.connector.vertica11.dialect.Vertica11Dialect;
import com.dtstack.chunjun.element.ColumnRowData;
import com.dtstack.chunjun.enums.DataSourcesType;
import com.dtstack.chunjun.enums.EWriteMode;
import com.dtstack.chunjun.throwable.WriteRecordException;
import com.dtstack.chunjun.common.util.JsonUtil;

import org.apache.flink.table.data.RowData;

import com.vertica.jdbc.VerticaConnection;
import com.vertica.jdbc.VerticaCopyStream;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/** <AUTHOR> on 2022/7/12. */
public class Vertica11OutputFormat extends JdbcOutputFormat {
    private VerticaCopyStream verticaCopyStream;

    private static final String DEFAULT_FIELD_DELIMITER = "\001";

    private static final String SPACE = "\u0000";

    private boolean enableCopyMode = true;

    private static final String LINE_DELIMITER = "\n";

    private static final String INSERT_SQL_MODE_TYPE = "copy";

    private String copySql = "";
    private static final String DEFAULT_NULL_VALUE = "\002";

    @Override
    protected void openInternal(int taskNumber, int numTasks) {
        try {
            dbConn = getConnection();
            dbConn.setAutoCommit(jdbcConf.isAutoCommit());

            initColumnList();
            enableCopyMode = INSERT_SQL_MODE_TYPE.equalsIgnoreCase(jdbcConf.getInsertSqlMode());
            if (!EWriteMode.INSERT.name().equalsIgnoreCase(jdbcConf.getMode())) {
                List<String> updateKey = jdbcConf.getUniqueKey();
                if (CollectionUtils.isEmpty(updateKey)) {
                    List<String> tableIndex =
                            JdbcUtil.getTablePrimaryKey(
                                    jdbcDialect.getTableIdentify(
                                            jdbcConf.getSchema(), jdbcConf.getSchema()),
                                    dbConn);
                    jdbcConf.setUniqueKey(tableIndex);
                    LOG.info("updateKey = {}", JsonUtil.toJson(tableIndex));
                }
            }

            buildStatementExecutor();
            if (EWriteMode.INSERT.name().equalsIgnoreCase(jdbcConf.getMode()) && enableCopyMode) {
                Vertica11Dialect vertica11Dialect = (Vertica11Dialect) jdbcDialect;
                copySql =
                        vertica11Dialect.getCopyStatement(
                                jdbcConf.getTable(),
                                columnNameList.toArray(new String[0]),
                                DEFAULT_FIELD_DELIMITER,
                                jdbcConf.getCopyMode());
                dbConn.createStatement().execute("set search_path to " + jdbcConf.getSchema());
                verticaCopyStream = new VerticaCopyStream((VerticaConnection) dbConn, copySql);
                verticaCopyStream.start();
            }
            LOG.info("subTask[{}}] wait finished", taskNumber);
        } catch (SQLException sqe) {
            throw new IllegalArgumentException("open() failed.", sqe);
        } finally {
            JdbcUtil.commit(dbConn);
        }
    }

    @Override
    protected String getUpsertStatement() {
        List<String> typeList =
                columnTypeList.stream().map(TypeConfig::getType).collect(Collectors.toList());
        return ((Vertica11Dialect) jdbcDialect)
                .getUpsertStatement(
                        jdbcConf.getSchema(),
                        jdbcConf.getTable(),
                        columnNameList.toArray(new String[0]),
                        typeList.toArray(new String[0]),
                        jdbcConf.getUniqueKey().toArray(new String[0]),
                        jdbcConf.isAllReplace())
                .get();
    }

    protected void initColumnList() {
        Pair<List<String>, List<TypeConfig>> pair = getTableMetaData();

        List<FieldConf> fieldList = jdbcConf.getColumn();
        List<String> fullColumnList = pair.getLeft();
        List<TypeConfig> fullColumnTypeList = pair.getRight();
        handleColumnList(fieldList, fullColumnList, fullColumnTypeList);
    }

    protected Pair<List<String>, List<TypeConfig>> getTableMetaData() {
        return jdbcDialect.getTableMetaData(dbConn, jdbcConf);
    }

    /**
     * detailed logic for handling column
     *
     * @param fieldList
     * @param fullColumnList
     * @param fullColumnTypeList
     */
    protected void handleColumnList(
            List<FieldConf> fieldList,
            List<String> fullColumnList,
            List<TypeConfig> fullColumnTypeList) {
        if (fieldList.size() == 1 && Objects.equals(fieldList.get(0).getName(), "*")) {
            columnNameList = fullColumnList;
            columnTypeList = fullColumnTypeList;
            return;
        }

        columnNameList = new ArrayList<>(fieldList.size());
        columnTypeList = new ArrayList<>(fieldList.size());
        for (FieldConf fieldConf : fieldList) {
            columnNameList.add(fieldConf.getName());
            for (int i = 0; i < fullColumnList.size(); i++) {
                if (fieldConf.getName().equalsIgnoreCase(fullColumnList.get(i))) {
                    columnTypeList.add(fullColumnTypeList.get(i));
                    break;
                }
            }
        }
    }

    @Override
    protected void flushSingle(RowData row) throws Exception {
        if (!enableCopyMode) {
            super.flushSingle(row);
        } else {
            throw new FlinkxRuntimeException(
                    "The copy mode does not support single write, and the performance will be lower！");
        }
    }

    @Override
    protected void flushBatch() throws Exception {
        if (!enableCopyMode) {
            super.flushBatch();
        } else {
            StringBuilder rowsStrBuilder = new StringBuilder(128);
            for (RowData row : rows) {
                ColumnRowData colRowData = (ColumnRowData) row;
                int lastIndex = row.getArity() - 1;
                StringBuilder rowStr = new StringBuilder(128);
                for (int index = 0; index < row.getArity(); index++) {
                    appendColumn(colRowData, index, rowStr, index == lastIndex);
                }
                String tempData = rowStr.toString();
                rowsStrBuilder.append(copyModeReplace(tempData)).append(LINE_DELIMITER);
            }
            String rowVal = rowsStrBuilder.toString();
            ByteArrayInputStream bi =
                    new ByteArrayInputStream(rowVal.getBytes(StandardCharsets.UTF_8));
            verticaCopyStream.addStream(bi);
            verticaCopyStream.execute();
        }
    }

    private void appendColumn(
            ColumnRowData colRowData, int pos, StringBuilder rowStr, boolean isLast) {
        if (colRowData.isNullAt(pos)) {
            rowStr.append(DEFAULT_NULL_VALUE);
        } else {
            Object col = colRowData.getField(pos);
            rowStr.append(col);
        }
        if (!isLast) {
            rowStr.append(DEFAULT_FIELD_DELIMITER);
        }
    }

    private String copyModeReplace(String rowStr) {
        if (rowStr.contains("\\")) {
            rowStr = rowStr.replace("\\\\", "\\\\\\\\");
        }
        if (rowStr.contains("\r")) {
            rowStr = rowStr.replace("\r", "\\\\r");
        }

        if (rowStr.contains("\n")) {
            rowStr = rowStr.replace("\n", "\\\\n");
        }

        // pg 字符串里含有\u0000 会报错 ERROR: invalid byte sequence for encoding "UTF8": 0x00
        if (rowStr.contains(SPACE)) {
            rowStr = rowStr.replace(SPACE, "");
        }
        return rowStr;
    }

    @Override
    public void closeInternal() {
        try {
            if (verticaCopyStream != null) {
                verticaCopyStream.finish();
            }
            super.closeInternal();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    protected synchronized void writeRecordInternal() {
        if (flushEnable.get()) {
            try {
                writeMultipleRecordsInternal();
                if (enableCopyMode) {
                    List<Long> rejects = verticaCopyStream.getRejects();
                    if (rejects.size() > 0) {
                        throw new RuntimeException("Copy mode insert failed.");
                    }
                }
                numWriteCounter.add(rows.size());
                bytesWriteCounter.add(rowsBytes);
            } catch (Exception e) {
                // copy模式下不转为单条写入,直接将失败条数计入脏数据
                if (enableCopyMode) {
                    List<Long> rejects = verticaCopyStream.getRejects();
                    for (int i = 0; i < rejects.size(); i++) {
                        bytesWriteCounter.add(-rowSizeCalculator.getObjectSize(rows.get(i)));
                        rows.remove(i);
                        dirtyManager.collect(
                                null,
                                new WriteRecordException(
                                        "Copy mode cannot capture write exception information and specific error data",
                                        e),
                                null,
                                DataSourcesType.OUTPUT_FORMAT);
                    }
                } else {
                    rows.forEach(item -> writeSingleRecord(item, numWriteCounter));
                }
            } finally {
                // Data is either recorded dirty data or written normally
                rows.clear();
                rowsBytes = 0;
            }
        }
    }
}
