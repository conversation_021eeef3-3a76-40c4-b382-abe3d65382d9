package com.dtstack.chunjun.connector.elasticsearch.enums;

import org.apache.commons.lang3.StringUtils;

/** <AUTHOR> linchen @Date: 2022/7/7 18:09 */
public enum ClusterVersionEnum {
    V5("5.x", "doc", true),
    V6("6.x", "_doc", true),
    V7("7.x", null, false);

    private final String name;
    private final String defaultType;
    private final boolean mustType;

    ClusterVersionEnum(String name, String defaultType, boolean mustType) {
        this.name = name;
        this.defaultType = defaultType;
        this.mustType = mustType;
    }

    public String getName() {
        return name;
    }

    public String getDefaultType() {
        return defaultType;
    }

    public boolean isMustType() {
        return mustType;
    }

    private static final String V5_START = "5.";
    private static final String V5_ONLY = "5";
    private static final String V6_START = "6.";
    private static final String V6_ONLY = "6";
    private static final String V7_START = "7.";
    private static final String V7_ONLY = "7";

    public static ClusterVersionEnum getByVersion(String version) {
        if (StringUtils.isBlank(version)) {
            throw new RuntimeException("集群版本为空");
        }
        String v = version.trim();
        if (v.startsWith(V5_START) || v.equals(V5_ONLY)) {
            return V5;
        } else if (v.startsWith(V6_START) || v.equals(V6_ONLY)) {
            return V6;
        } else if (v.startsWith(V7_START) || v.equals(V7_ONLY)) {
            return V7;
        }
        throw new RuntimeException("不支持的集群版本, 目前仅支持5/6/7版本");
    }

    public static void checkMustType(ClusterVersionEnum e, String type) {
        if (e.isMustType() && StringUtils.isBlank(type)) {
            throw new RuntimeException(e.getName() + "版本的集群必须填写type");
        } else if (!e.isMustType() && StringUtils.isNotBlank(type)) {
            throw new RuntimeException(e.getName() + "版本的集群已经移除type, 请填写为空");
        }
    }
}
