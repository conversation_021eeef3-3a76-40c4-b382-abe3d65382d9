/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.mysql.table;

import com.dtstack.chunjun.adaptor.OptionAdaptor;
import com.dtstack.chunjun.table.options.adaptor.LookupOptionsAdaptor;

import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.connector.jdbc.core.table.JdbcDynamicTableFactory;
import org.apache.flink.table.connector.sink.DynamicTableSink;
import org.apache.flink.table.connector.source.DynamicTableSource;

import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @program: chunjun
 * @author: wujuan
 * @create: 2024/12/20
 */
public class MysqlDynamicTableFactory extends JdbcDynamicTableFactory {

    /** 通过该值查找具体插件 */
    private static final String IDENTIFIER = "mysql-x";

    @Override
    public String factoryIdentifier() {
        return IDENTIFIER;
    }

    @Override
    public DynamicTableSink createDynamicTableSink(Context context) {
        OptionAdaptor.removeOptions(context.getCatalogTable().getOptions(), deprecatedOptions());
        OptionAdaptor.removeOptionsByPrefix(
                context.getCatalogTable().getOptions(), Set.of("vertx.", "druid."));
        return super.createDynamicTableSink(context);
    }

    @Override
    public DynamicTableSource createDynamicTableSource(Context context) {
        OptionAdaptor.removeOptions(context.getCatalogTable().getOptions(), deprecatedOptions());
        OptionAdaptor.removeOptionsByPrefix(
                context.getCatalogTable().getOptions(), Set.of("vertx.", "druid."));
        OptionAdaptor.adaptor(context.getCatalogTable().getOptions(), LookupOptionsAdaptor.options);
        return super.createDynamicTableSource(context);
    }

    private Set<ConfigOption> deprecatedOptions() {
        return Stream.of(
                        DeprecateOption.SCAN_PARTITION_STRATEGY,
                        DeprecateOption.SCAN_INCREMENT_COLUMN,
                        DeprecateOption.SCAN_INCREMENT_COLUMN_TYPE,
                        DeprecateOption.SCAN_POLLING_INTERVAL,
                        DeprecateOption.SCAN_START_LOCATION,
                        DeprecateOption.SCAN_PARALLELISM,
                        DeprecateOption.SCAN_QUERY_TIMEOUT,
                        DeprecateOption.SCAN_RESTORE_COLUMNNAME,
                        DeprecateOption.SCAN_RESTORE_COLUMNTYPE,
                        DeprecateOption.LOOKUP_CACHE_PERIOD,
                        DeprecateOption.LOOKUP_ERROR_LIMIT,
                        DeprecateOption.LOOKUP_FETCH_SIZE,
                        DeprecateOption.LOOKUP_ASYNC_TIMEOUT,
                        DeprecateOption.LOOKUP_PARALLELISM,
                        DeprecateOption.SINK_ALL_REPLACE,
                        DeprecateOption.SINK_SEMANTIC,
                        DeprecateOption.SINK_IGNORE_DELETE)
                .collect(Collectors.toSet());
    }
}
