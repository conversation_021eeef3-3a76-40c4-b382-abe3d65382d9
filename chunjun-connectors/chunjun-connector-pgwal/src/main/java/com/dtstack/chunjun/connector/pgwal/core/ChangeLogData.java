/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dtstack.chunjun.connector.pgwal.core;

import org.apache.flink.table.data.RowData;

/**
 * Date: 2020/06/01 Company: www.dtstack.com
 *
 * <AUTHOR>
 */
public class ChangeLogData {
    private final long lsn;
    private RowData data;

    private Exception e;

    private ChangeLogData(long lsn, RowData data) {
        this.lsn = lsn;
        this.data = data;
    }

    private ChangeLogData(long lsn, Exception e) {
        this.lsn = lsn;
        this.e = e;
    }

    public static ChangeLogData data(long lsn, RowData data) {
        return new ChangeLogData(lsn, data);
    }

    public static ChangeLogData error(long lsn, Exception e) {
        return new ChangeLogData(lsn, e);
    }

    public long getLsn() {
        return lsn;
    }

    public RowData getData() throws Exception {
        if (e != null) {
            throw e;
        }
        return data;
    }

    @Override
    public String toString() {
        return "QueueData{" + "scn=" + lsn + ", data=" + data + '}';
    }
}
