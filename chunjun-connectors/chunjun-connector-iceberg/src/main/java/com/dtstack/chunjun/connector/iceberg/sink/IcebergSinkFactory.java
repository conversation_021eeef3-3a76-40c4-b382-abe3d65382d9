/*
 *
 *  * Licensed to the Apache Software Foundation (ASF) under one
 *  * or more contributor license agreements.  See the NOTICE file
 *  * distributed with this work for additional information
 *  * regarding copyright ownership.  The ASF licenses this file
 *  * to you under the Apache License, Version 2.0 (the
 *  * "License"); you may not use this file except in compliance
 *  * with the License.  You may obtain a copy of the License at
 *  *
 *  * http://www.apache.org/licenses/LICENSE-2.0
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  * See the License for the specific language governing permissions and
 *  * limitations under the License.
 *
 */

package com.dtstack.chunjun.connector.iceberg.sink;

import com.dtstack.chunjun.conf.SyncConf;
import com.dtstack.chunjun.connector.iceberg.conf.IcebergConf;
import com.dtstack.chunjun.converter.RawTypeMapper;
import com.dtstack.chunjun.sink.SinkFactory;
import com.dtstack.chunjun.common.util.JsonUtil;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSink;
import org.apache.flink.table.data.RowData;

public class IcebergSinkFactory extends SinkFactory {
    private IcebergConf icebergConf;
    private SyncConf syncConf;

    public IcebergSinkFactory(SyncConf conf) {
        super(conf);
        this.syncConf = conf;
        icebergConf =
                JsonUtil.toObject(
                        JsonUtil.toJson(syncConf.getWriter().getParameter()), IcebergConf.class);
    }

    @Override
    public RawTypeMapper getRawTypeMapper() {
        return null;
    }

    @Override
    public DataStreamSink<RowData> createSink(DataStream<RowData> dataSet) {
        return createStreamSink(dataSet);
    }

    /* 数据还原任务 */
    private DataStreamSink<RowData> createStreamSink(DataStream<RowData> dataSet) {
        IcebergOutputFormat format = new IcebergOutputFormat();
        format.setIcebergConf(icebergConf);

        if (null != ddlConf) {
            format.setDdlConf(ddlConf);
        }

        if (null != stateRecoverConf) {
            format.setStateRecoverConf(stateRecoverConf);
        }

        if (null == syncConf.getCdcConf()) {
            format.setSyncMode(true);
        }

        return createOutput(dataSet, format);
    }
}
