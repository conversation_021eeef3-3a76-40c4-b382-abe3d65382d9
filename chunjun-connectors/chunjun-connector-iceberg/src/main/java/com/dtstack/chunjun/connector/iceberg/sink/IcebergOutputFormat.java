/*
 *
 *  * Licensed to the Apache Software Foundation (ASF) under one
 *  * or more contributor license agreements.  See the NOTICE file
 *  * distributed with this work for additional information
 *  * regarding copyright ownership.  The ASF licenses this file
 *  * to you under the Apache License, Version 2.0 (the
 *  * "License"); you may not use this file except in compliance
 *  * with the License.  You may obtain a copy of the License at
 *  *
 *  * http://www.apache.org/licenses/LICENSE-2.0
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  * See the License for the specific language governing permissions and
 *  * limitations under the License.
 *
 */

package com.dtstack.chunjun.connector.iceberg.sink;

import com.dtstack.chunjun.cdc.DdlRowData;
import com.dtstack.chunjun.cdc.ddl.DdlRowDataConverted;
import com.dtstack.chunjun.cdc.iceberg.IcebergTask;
import com.dtstack.chunjun.common.util.FileSystemUtil;
import com.dtstack.chunjun.connector.iceberg.conf.IcebergConf;
import com.dtstack.chunjun.enums.Semantic;
import com.dtstack.chunjun.restore.FormatState;
import com.dtstack.chunjun.sink.WriteMode;
import com.dtstack.chunjun.sink.format.BaseRichOutputFormat;
import com.dtstack.chunjun.throwable.WriteRecordException;
import com.dtstack.chunjun.util.PluginUtil;

import org.apache.flink.api.common.cache.DistributedCache;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.data.RowData;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.iceberg.Schema;
import org.apache.iceberg.Table;
import org.apache.iceberg.catalog.TableIdentifier;
import org.apache.iceberg.flink.CatalogLoader;
import org.apache.iceberg.flink.TableLoader;
import org.apache.iceberg.flink.sink.FlinkSink;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import static com.dtstack.chunjun.cdc.EventType.CREATE_TABLE;

public class IcebergOutputFormat extends BaseRichOutputFormat {
    protected IcebergConf icebergConf;

    private transient StreamExecutionEnvironment env;
    private transient CatalogLoader catalogLoader;

    private UserGroupInformation ugi;
    private ExecutorService ugiRefreshService;
    private boolean isSync = false;
    private boolean overwriteOnce = false;

    @Override
    protected void openInternal(int taskNumber, int numTasks) throws IOException {
        try {
            this.env = StreamExecutionEnvironment.createLocalEnvironment();
            if (FileSystemUtil.isOpenKerberos(icebergConf.getHadoopConfig())) {
                DistributedCache distributedCache;
                if (getRuntimeContext() == null) {
                    distributedCache = PluginUtil.createDistributedCacheFromContextClassLoader();
                } else {
                    distributedCache = getRuntimeContext().getDistributedCache();
                }

                Pair<UserGroupInformation, ExecutorService> pair =
                        FileSystemUtil.getRefreshUGI(
                                icebergConf.getHadoopConfig(),
                                icebergConf.getDefaultFS(),
                                distributedCache,
                                jobId,
                                String.valueOf(taskNumber));
                this.ugi = pair.getLeft();
                this.ugiRefreshService = pair.getRight();
            }

            this.catalogLoader = buildIcebergCatalog();
        } catch (Exception e) {
            throw e;
        }
    }

    /* 构建iceberg catalog */
    private CatalogLoader buildIcebergCatalog() {
        Map<String, String> icebergProps = new HashMap<>();
        icebergProps.put("warehouse", icebergConf.getWarehouse());
        icebergProps.put("uri", icebergConf.getUri());

        /* build hadoop configuration */
        Configuration configuration = new Configuration();
        icebergConf
                .getHadoopConfig()
                .entrySet()
                .forEach(
                        kv -> {
                            if (kv.getValue() instanceof String) {
                                configuration.set(kv.getKey(), (String) kv.getValue());
                            }
                        });

        try {
            return FileSystemUtil.runSecured(
                    this.ugi,
                    () -> {
                        return CatalogLoader.hive(
                                icebergConf.getDatabase(), configuration, icebergProps);
                    });
        } catch (Exception e) {
            throw new FlinkxRuntimeException(e);
        }
    }

    private TableLoader getTableLoader() {
        try {
            return FileSystemUtil.runSecured(
                    ugi,
                    () -> {
                        TableLoader tl =
                                TableLoader.fromCatalog(
                                        catalogLoader,
                                        TableIdentifier.of(
                                                icebergConf.getDatabase(), icebergConf.getTable()));

                        if (tl instanceof TableLoader.CatalogTableLoader) {
                            tl.open();
                        }
                        ;
                        return tl;
                    });
        } catch (Exception e) {
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    protected void writeSingleRecordInternal(RowData rowData) throws WriteRecordException {
        try {
            DataStream<RowData> ds = env.fromElements(rowData);
            dataStreamToIceberg(ds);
            commitLsnToState();
        } catch (Exception e) {
            throw new WriteRecordException("write record error", e);
        }
    }

    private void dataStreamToIceberg(DataStream<RowData> ds) throws Exception {
        FileSystemUtil.runSecured(
                ugi,
                () -> {
                    Table table =
                            catalogLoader
                                    .loadCatalog()
                                    .loadTable(
                                            TableIdentifier.of(
                                                    icebergConf.getDatabase(),
                                                    icebergConf.getTable()));
                    Schema schema = table.schema();
                    if (isSync) {
                        List<String> columns =
                                icebergConf.getColumn().stream()
                                        .map(col -> col.getName())
                                        .collect(Collectors.toList());

                        if (table.schema().columns().size() != columns.size()) {
                            throw new FlinkxRuntimeException(
                                    "The size of config column should equal iceberg columns size");
                        }

                        DataStream<RowData> convertedDs =
                                ds.map(new DMlRowDataMap(icebergConf.getColumn()));
                        boolean isOverwrite =
                                icebergConf
                                        .getWriteMode()
                                        .equalsIgnoreCase(WriteMode.OVERWRITE.name());

                        if (!overwriteOnce) {
                            if (isOverwrite) {
                                overwriteOnce = true;
                            }
                            FlinkSink.forRowData(convertedDs)
                                    .tableLoader(getTableLoader())
                                    .writeParallelism(1)
                                    .equalityFieldColumns(columns)
                                    .overwrite(isOverwrite)
                                    .append();
                        } else {
                            FlinkSink.forRowData(convertedDs)
                                    .tableLoader(getTableLoader())
                                    .writeParallelism(1)
                                    .equalityFieldColumns(columns)
                                    .overwrite(false)
                                    .append();
                        }
                        env.execute();
                    } else {
                        DataStream<RowData> convertedDs = ds.map(new DMlRowDataMap(schema));

                        List<String> columns =
                                schema.columns().stream()
                                        .map(ele -> ele.name())
                                        .collect(Collectors.toList());

                        FlinkSink.forRowData(convertedDs)
                                .tableLoader(getTableLoader())
                                .writeParallelism(1)
                                .equalityFieldColumns(columns)
                                .append();
                        env.execute();
                    }
                    return null;
                });
    }

    @Override
    protected void writeMultipleRecordsInternal() throws Exception {
        if (rows.size() > 0) {
            DataStream<RowData> ds = env.fromCollection(rows);
            dataStreamToIceberg(ds);
            commitLsnToState();
        }
    }

    /* 将iceberg已提交的lsn， 更新state */
    private void commitLsnToState() {
        if (enableStateRecover) {
            commitDataLsn();
        }
    }

    /** 累计dml数据，满足了batchSize, 提交 如果发生了ddl变更， 且dml数据集合不为空， 先提交，再进行ddl变更 */
    @Override
    public synchronized void writeRecord(RowData rowData) {
        checkTimerWriteException();
        int size = 0;

        /* 更新数据的lsn */
        if (enableStateRecover) {
            updateLsn(rowData);
        }

        if (rowData instanceof DdlRowData) {
            if (rows.size() > 0) {
                writeRecordInternal();
            }

            if (executeDdlAble) {
                executeDdlRowData((DdlRowData) rowData);
            }
        } else if (batchSize <= 1) {
            writeSingleRecord(rowData, numWriteCounter);
            size = 1;

        } else {
            rowsBytes += rowSizeCalculator.getObjectSize(rowData);
            rows.add(rowData);
            if (rows.size() >= batchSize) {
                writeRecordInternal();
                size = batchSize;
            }
        }

        updateDuration();
        if (checkpointEnabled) {
            snapshotWriteCounter.add(size);
        }
    }

    @Override
    protected void executeDdlRowData(DdlRowData ddlRowData) {
        String content = ((DdlRowDataConverted) ddlRowData).getSqlConverted();

        Gson gson = new Gson();

        /* 不支持的ddl， skip掉，且不影响程序运行 */
        try {
            IcebergTask task = gson.fromJson(content, IcebergTask.class);
        } catch (JsonSyntaxException exception) {
            LOG.error("skip ddl: {}", content);

            ddlHandler.updateDDLChange(
                    ddlRowData.getTableIdentifier(),
                    ddlRowData.getLsn(),
                    ddlRowData.getLsnSequence(),
                    2,
                    "iceberg skip ddl");

            commitLsnToState();

            return;
        }

        try {
            IcebergTask task = gson.fromJson(content, IcebergTask.class);

            if (task.getEventType() == CREATE_TABLE) {
                boolean tableExists =
                        FileSystemUtil.runSecured(
                                this.ugi,
                                () -> {
                                    IcebergDDLExecutor ddlExecutor =
                                            new IcebergDDLExecutor(
                                                    icebergConf, task, catalogLoader.loadCatalog());
                                    return ddlExecutor.tableExists();
                                });
                if (tableExists) {
                    ddlHandler.updateDDLChange(
                            ddlRowData.getTableIdentifier(),
                            ddlRowData.getLsn(),
                            ddlRowData.getLsnSequence(),
                            2,
                            "table exists, skip");
                    return;
                }
            }

            FileSystemUtil.runSecured(
                    this.ugi,
                    () -> {
                        IcebergDDLExecutor ddlExecutor =
                                new IcebergDDLExecutor(
                                        icebergConf, task, catalogLoader.loadCatalog());
                        ddlExecutor.execute();
                        return 0;
                    });

            ddlHandler.updateDDLChange(
                    ddlRowData.getTableIdentifier(),
                    ddlRowData.getLsn(),
                    ddlRowData.getLsnSequence(),
                    2,
                    null);

            commitLsnToState();
        } catch (Exception e) {
            LOG.error("execute iceberg action error: {}", ExceptionUtil.getErrorMessage(e));
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public synchronized FormatState getFormatState() throws Exception {
        if (Semantic.EXACTLY_ONCE == semantic) {
            if (rows.size() > 0) {
                writeRecordInternal();
            }
            flushEnable.compareAndSet(true, false);
        } else {
            writeRecordInternal();
        }
        // set metric after preCommit
        formatState.setNumberWrite(numWriteCounter.getLocalValue());
        formatState.setMetric(outputMetric.getMetricCounters());
        LOG.info("format state:{}", formatState.getState());
        return formatState;
    }

    @Override
    protected void closeInternal() throws IOException {
        if (ugiRefreshService != null) {
            ugiRefreshService.shutdown();
        }
    }

    public void setIcebergConf(IcebergConf icebergConf) {
        super.setConfig(icebergConf);
        this.icebergConf = icebergConf;
    }

    public void setSyncMode(boolean sync) {
        isSync = sync;
    }
}
