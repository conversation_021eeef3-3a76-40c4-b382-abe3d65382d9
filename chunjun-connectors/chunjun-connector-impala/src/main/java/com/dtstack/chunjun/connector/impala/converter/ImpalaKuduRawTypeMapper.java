package com.dtstack.chunjun.connector.impala.converter;

import com.dtstack.chunjun.conf.TypeConfig;
import com.dtstack.chunjun.throwable.UnsupportedTypeException;

import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.types.DataType;

/** <AUTHOR> @Company Dtstack @Date: 2022/4/26 5:00 PM */
public class ImpalaKuduRawTypeMapper {

    /**
     * @param type kudu original type
     * @return the type of flink table
     */
    public static DataType apply(TypeConfig type) {
        switch (type.getType()) {
            case "INT8":
            case "TINYINT":
                return DataTypes.TINYINT();
            case "BYTES":
            case "BINARY":
                return DataTypes.BINARY(Integer.MAX_VALUE);
            case "INT16":
            case "SMALLINT":
                return DataTypes.SMALLINT();
            case "INT":
            case "INT32":
            case "INTEGER":
                return DataTypes.INT();
            case "INT64":
            case "BIGINT":
            case "LONG":
            case "UNIXTIME_MICROS":
                return DataTypes.BIGINT();
            case "BOOL":
            case "BOOLEAN":
                return DataTypes.BOOLEAN();
            case "FLOAT":
                return DataTypes.FLOAT();
            case "DOUBLE":
                return DataTypes.DOUBLE();
            case "DECIMAL":
                return DataTypes.DECIMAL(38, 18);
            case "VARCHAR":
            case "STRING":
                return DataTypes.STRING();
            case "DATE":
                return DataTypes.DATE();
            case "TIMESTAMP":
                return DataTypes.TIMESTAMP();
            default:
                throw new UnsupportedTypeException(type);
        }
    }
}
