package com.dtstack.chunjun.connector.impala.converter;

import com.dtstack.chunjun.conf.FieldConf;
import com.dtstack.chunjun.connector.jdbc.conf.JdbcConf;
import com.dtstack.chunjun.connector.jdbc.dialect.JdbcDialect;
import com.dtstack.chunjun.converter.RawTypeMapper;

import com.google.common.collect.Lists;

import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.Properties;

/** <AUTHOR> @Company Dtstack @Date: 2022/4/27 11:18 AM */
public class ImpalaDialect implements JdbcDialect {

    // specific type which values need to be quoted
    private static final String[] NEED_QUOTE_TYPE = {"STRING", "TIMESTAMP", "VARCHAR"};

    @Override
    public String dialectName() {
        return "IMPALA";
    }

    @Override
    public boolean canHandle(String url) {
        return url.startsWith("jdbc:hive2:") || url.startsWith("jdbc:impala:");
    }

    @Override
    public RawTypeMapper getRawTypeMapper() {
        return ImpalaRawTypeMapper::apply;
    }

    @Override
    public Optional<String> defaultDriverName() {
        return Optional.of("com.cloudera.impala.jdbc41.Driver");
    }

    @Override
    public String quoteIdentifier(String identifier) {
        return "`" + identifier + "`";
    }

    public String getInsertPartitionIntoStatement(
            String schema,
            String tableName,
            String partitionKey,
            String partitionValue,
            String[] fieldNames) {
        List<String> columns = Lists.newLinkedList();
        List<String> placeholders = Lists.newLinkedList();

        for (int i = 0; i < fieldNames.length; i++) {
            columns.add(quoteIdentifier(fieldNames[i]));
            placeholders.add(":" + i);
        }

        return "INSERT INTO "
                + buildTableInfoWithSchema(schema, tableName)
                + " ("
                + String.join(", ", columns)
                + ") "
                + " PARTITION "
                + " ( "
                + quoteIdentifier(partitionKey)
                + "="
                + "'"
                + partitionValue
                + "'"
                + " ) "
                + " VALUES ("
                + String.join(", ", placeholders)
                + ")";
    }

    public String getInsertIntoStatement(
            String schema, String tableName, List<FieldConf> fieldConfList) {
        List<String> columns = new LinkedList<>();
        List<String> placeholders = new LinkedList<>();
        for (int i = 0; i < fieldConfList.size(); i++) {
            FieldConf field = fieldConfList.get(i);
            columns.add(quoteIdentifier(field.getName()));
            String type = field.getType();
            placeholders.add(generateSpecificPlaceholder(type, i));
        }

        return "INSERT INTO "
                + buildTableInfoWithSchema(schema, tableName)
                + " ("
                + String.join(", ", columns)
                + ")"
                + " VALUES ("
                + String.join(", ", placeholders)
                + ")";
    }

    private String generateSpecificPlaceholder(String type, int typeIndex) {
        for (String needQuoteType : NEED_QUOTE_TYPE) {
            if (type.toUpperCase(Locale.ROOT).contains(needQuoteType)) {
                return String.format("cast(:%s as %s)", typeIndex, needQuoteType);
            }
        }
        return ":" + typeIndex;
    }

    @Override
    public void putWriterExtParam(JdbcConf jdbcConf) {
        Properties properties = jdbcConf.getProperties();
        if (properties == null) {
            properties = new Properties();
        }
        properties.putIfAbsent("rewriteBatchedStatements", "true");
        jdbcConf.setProperties(properties);
    }
}
