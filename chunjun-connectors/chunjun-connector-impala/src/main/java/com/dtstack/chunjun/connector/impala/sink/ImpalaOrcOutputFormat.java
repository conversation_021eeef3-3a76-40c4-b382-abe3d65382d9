package com.dtstack.chunjun.connector.impala.sink;

import com.dtstack.chunjun.connector.hdfs.sink.HdfsOrcOutputFormat;
import com.dtstack.chunjun.connector.impala.conf.ImpalaConf;
import com.dtstack.chunjun.connector.impala.util.HiveDbUtil;
import com.dtstack.chunjun.connector.impala.util.ImpalaUtil;

import org.apache.commons.collections.CollectionUtils;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

/** <AUTHOR> @Company Dtstack @Date: 2022/4/25 11:43 AM */
public class ImpalaOrcOutputFormat extends HdfsOrcOutputFormat {

    protected List<String> preSql;
    protected List<String> postSql;
    protected HiveDbUtil.ConnectionInfo connectionInfo;

    public ImpalaOrcOutputFormat(
            List<String> preSql, List<String> postSql, HiveDbUtil.ConnectionInfo connectionInfo) {
        this.preSql = preSql;
        this.postSql = postSql;
        this.connectionInfo = connectionInfo;
    }

    @Override
    protected void openInternal(int taskNumber, int numTasks) throws IOException {
        if (taskNumber == 0) {
            try {
                securityContext.execute(
                        null,
                        t -> {
                            if (CollectionUtils.isNotEmpty(preSql)) {
                                try (Connection connection =
                                        HiveDbUtil.getConnection(connectionInfo)) {
                                    HiveDbUtil.executeBatchSqlWithoutResultSet(
                                            preSql, connection, connectionInfo);
                                } catch (SQLException e) {
                                    throw new RuntimeException(e);
                                }
                            }
                            return null;
                        });
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        super.openInternal(taskNumber, numTasks);
    }

    @Override
    public void finalizeGlobal(int parallelism) {
        super.finalizeGlobal(parallelism);
        this.securityContext = initSecurityContext();
        try {
            securityContext.execute(
                    null,
                    t -> {
                        try (Connection connection = HiveDbUtil.getConnection(connectionInfo)) {
                            // 写完 HDFS 文件后，要刷新 Impala 数据.
                            ImpalaConf impalaConf = (ImpalaConf) getHdfsConf();
                            ImpalaUtil.refresh(
                                    connectionInfo,
                                    impalaConf.getSchema(),
                                    impalaConf.getTable(),
                                    connection);

                            if (postSql != null && postSql.size() > 0) {
                                HiveDbUtil.executeBatchSqlWithoutResultSet(
                                        postSql, connection, connectionInfo);
                            }
                            return null;
                        } catch (SQLException e) {
                            throw new RuntimeException(e);
                        }
                    });
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (this.securityContext != null) {
                try {
                    this.securityContext.close();
                    this.securityContext = null;
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }
}
