package com.dtstack.chunjun.connector.impala.lookup;

import com.dtstack.chunjun.common.security.SecurityUtil;
import com.dtstack.chunjun.connector.impala.conf.ImpalaJdbcConf;
import com.dtstack.chunjun.connector.impala.util.ImpalaUtil;
import com.dtstack.chunjun.connector.jdbc.dialect.JdbcDialect;
import com.dtstack.chunjun.connector.jdbc.lookup.JdbcAllLookupFunction;
import com.dtstack.chunjun.lookup.conf.LookupConf;
import com.dtstack.chunjun.throwable.NoRestartException;
import com.dtstack.security.SecurityConfigBuilder;
import com.dtstack.security.SecurityContext;
import com.dtstack.security.filesystem.FileManager;

import org.apache.flink.api.common.cache.DistributedCache;
import org.apache.flink.streaming.api.operators.StreamingRuntimeContext;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.types.logical.RowType;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/** <AUTHOR> @Company Dtstack @Date: 2022/5/20 4:16 PM */
public class ImpalaAllLookupFunction extends JdbcAllLookupFunction {

    private static final Logger LOG = LoggerFactory.getLogger(ImpalaAllLookupFunction.class);

    private final ImpalaJdbcConf impalaJdbcConf;

    private SecurityContext securityContext;

    public ImpalaAllLookupFunction(
            ImpalaJdbcConf impalaJdbcConf,
            JdbcDialect jdbcDialect,
            LookupConf lookupConf,
            String[] fieldNames,
            String[] keyNames,
            int[] keyIndexes,
            RowType rowType) {
        super(impalaJdbcConf, jdbcDialect, lookupConf, fieldNames, keyNames, keyIndexes, rowType);
        this.impalaJdbcConf = impalaJdbcConf;
    }

    @Override
    public void open(FunctionContext context) throws Exception {
        securityContext = initSecurityContext(context);
        super.open(context);
    }

    @Override
    protected void loadData(Object cacheRef) {
        Map<String, List<Map<String, Object>>> tmpCache =
                (Map<String, List<Map<String, Object>>>) cacheRef;
        Connection connection = null;
        try {
            connection =
                    securityContext.execute(null, t -> ImpalaUtil.getConnection(impalaJdbcConf));
            queryAndFillData(tmpCache, connection);
        } catch (Exception e) {
            LOG.error("", e);
            throw new RuntimeException(e);
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    LOG.error("", e);
                }
            }
        }
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (securityContext != null) {
            securityContext.close();
            securityContext = null;
        }
    }

    protected SecurityContext initSecurityContext(FunctionContext context) {
        try {
            if (this.securityContext == null) {
                SecurityConfigBuilder securityConfigBuilder = new SecurityConfigBuilder();

                String localRootPath =
                        FileManager.LOCAL_CACHE_DIR
                                + File.separator
                                + UUID.randomUUID()
                                + File.separator
                                + UUID.randomUUID();
                SecurityUtil.addKerberosConfig(
                        securityConfigBuilder, impalaJdbcConf.getHadoopConfig());
                SecurityUtil.addFileManagerConfig(
                        securityConfigBuilder, impalaJdbcConf.getHadoopConfig(), localRootPath);

                Field field = FunctionContext.class.getDeclaredField("context");
                field.setAccessible(true);
                Object o = field.get(context);
                DistributedCache distributedCache;
                if (o instanceof StreamingRuntimeContext) {
                    distributedCache = ((StreamingRuntimeContext) o).getDistributedCache();
                } else {
                    distributedCache = SecurityUtil.createDistributedCacheFromContextClassLoader();
                }
                this.securityContext =
                        SecurityUtil.getSecurityContext(securityConfigBuilder, distributedCache);
            }
            return this.securityContext;
        } catch (Exception e) {
            throw new NoRestartException("init securityContext failed ", e);
        }
    }
}
