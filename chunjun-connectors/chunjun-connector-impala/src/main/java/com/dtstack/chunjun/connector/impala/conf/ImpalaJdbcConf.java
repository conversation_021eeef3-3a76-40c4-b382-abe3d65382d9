package com.dtstack.chunjun.connector.impala.conf;

import com.dtstack.chunjun.connector.jdbc.conf.JdbcConf;

import java.util.Map;

/** <AUTHOR> @Company Dtstack @Date: 2022/5/18 10:02 AM */
public class ImpalaJdbcConf extends JdbcConf {

    private String storeType;

    private Integer authMech;

    private String updateMode;

    private String partition;

    private String partitionType;

    public String getPartition() {
        return partition;
    }

    public void setPartition(String partition) {
        this.partition = partition;
    }

    public String getPartitionType() {
        return partitionType;
    }

    public void setPartitionType(String partitionType) {
        this.partitionType = partitionType;
    }

    private Map<String, Object> hadoopConfig;

    public String getStoreType() {
        return storeType;
    }

    public void setStoreType(String storeType) {
        this.storeType = storeType;
    }

    public Integer getAuthMech() {
        return authMech;
    }

    public void setAuthMech(Integer authMech) {
        this.authMech = authMech;
    }

    public String getUpdateMode() {
        return updateMode;
    }

    public void setUpdateMode(String updateMode) {
        this.updateMode = updateMode;
    }

    public Map<String, Object> getHadoopConfig() {
        return hadoopConfig;
    }

    public void setHadoopConfig(Map<String, Object> hadoopConfig) {
        this.hadoopConfig = hadoopConfig;
    }
}
