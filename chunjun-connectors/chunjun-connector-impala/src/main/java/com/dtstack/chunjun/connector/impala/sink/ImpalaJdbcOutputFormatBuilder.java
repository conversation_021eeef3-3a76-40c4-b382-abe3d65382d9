package com.dtstack.chunjun.connector.impala.sink;

import com.dtstack.chunjun.connector.jdbc.conf.JdbcConf;
import com.dtstack.chunjun.connector.jdbc.sink.JdbcOutputFormatBuilder;
import com.dtstack.chunjun.converter.AbstractRowConverter;

import org.apache.commons.lang.StringUtils;

/** <AUTHOR> @Company Dtstack @Date: 2022/5/18 2:34 PM */
public class ImpalaJdbcOutputFormatBuilder extends JdbcOutputFormatBuilder {
    public ImpalaJdbcOutputFormatBuilder() {
        super(new ImpalaJdbcOutputFormat());
    }

    @Override
    public void setRowConverter(AbstractRowConverter converter) {
        format.setRowConverter(converter);
    }

    @Override
    protected void checkFormat() {
        JdbcConf jdbcConf = format.getJdbcConf();
        StringBuilder sb = new StringBuilder(256);
        if (StringUtils.isBlank(jdbcConf.getJdbcUrl())) {
            sb.append("No jdbc url supplied;\n");
        }
        if (sb.length() > 0) {
            throw new IllegalArgumentException(sb.toString());
        }
    }
}
