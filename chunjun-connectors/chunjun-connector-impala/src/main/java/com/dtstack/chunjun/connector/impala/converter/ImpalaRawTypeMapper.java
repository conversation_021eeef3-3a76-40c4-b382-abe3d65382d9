package com.dtstack.chunjun.connector.impala.converter;

import com.dtstack.chunjun.conf.TypeConfig;
import com.dtstack.chunjun.throwable.UnsupportedTypeException;

import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.types.DataType;

/** <AUTHOR> @Company Dtstack @Date: 2022/4/26 8:14 PM */
public class ImpalaRawTypeMapper {

    public static DataType apply(TypeConfig type) {
        switch (type.getType()) {
            case "BOOLEAN":
                return DataTypes.BOOLEAN();
            case "TINYINT":
                return DataTypes.TINYINT();
            case "SMALLINT":
                return DataTypes.SMALLINT();
            case "INT":
                return DataTypes.INT();
            case "BIGINT":
                return DataTypes.BIGINT();
            case "FLOAT":
                return DataTypes.FLOAT();
            case "DECIMAL":
                return type.toDecimalDataType();
            case "DOUBLE":
                return DataTypes.DOUBLE();
            case "CHAR":
            case "VARCHAR":
            case "STRING":
                return DataTypes.STRING();
            case "TIMESTAMP":
                return type.toTimestampDataType(6);
            default:
                throw new UnsupportedTypeException(type);
        }
    }
}
