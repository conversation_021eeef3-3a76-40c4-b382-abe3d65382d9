/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.kudu.format;

import com.dtstack.chunjun.connector.kudu.connector.KuduFilterInfo;
import com.dtstack.chunjun.connector.kudu.connector.KuduTableInfo;
import com.dtstack.chunjun.connector.kudu.connector.converter.RowResultConverter;
import com.dtstack.chunjun.connector.kudu.connector.reader.KuduReaderConfig;

import org.apache.flink.annotation.PublicEvolving;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;

import java.util.List;

/** InputFormat based on the row object type. */
@PublicEvolving
public class KuduRowInputFormat extends AbstractKuduInputFormat<Row> {

    public KuduRowInputFormat(
            KuduReaderConfig readerConfig,
            RowResultConverter<Row> rowResultConverter,
            KuduTableInfo tableInfo) {
        super(readerConfig, rowResultConverter, tableInfo);
    }

    public KuduRowInputFormat(
            KuduReaderConfig readerConfig,
            RowResultConverter<Row> rowResultConverter,
            KuduTableInfo tableInfo,
            List<String> tableProjections) {
        super(readerConfig, rowResultConverter, tableInfo, tableProjections);
    }

    public KuduRowInputFormat(
            KuduReaderConfig readerConfig,
            RowResultConverter<Row> rowResultConverter,
            KuduTableInfo tableInfo,
            List<KuduFilterInfo> tableFilters,
            List<String> tableProjections) {
        super(readerConfig, rowResultConverter, tableInfo, tableFilters, tableProjections);
    }

    @Override
    public TypeInformation<Row> getProducedType() {
        return TypeInformation.of(Row.class);
    }
}
