/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.http.client;

import com.dtstack.chunjun.connector.http.common.HttpRestConfig;
import com.dtstack.chunjun.connector.http.common.HttpUtil;
import com.dtstack.chunjun.connector.http.common.MetaParam;
import com.dtstack.chunjun.connector.http.entity.HttpResponseData;
import com.dtstack.chunjun.util.ExceptionUtil;
import com.dtstack.security.SecurityContext;

import org.apache.http.Header;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.List;

public class HbaseHttpClient extends HttpClient {
    public HbaseHttpClient(
            HttpRestConfig httpRestConfig,
            List<MetaParam> originalBodyList,
            List<MetaParam> originalParamList,
            List<MetaParam> originalHeaderList,
            SecurityContext securityContext) {
        super(
                httpRestConfig,
                originalBodyList,
                originalParamList,
                originalHeaderList,
                securityContext);
    }

    @Override
    public void doExecute(int retryTime, Exception exception) {

        // 重试次数到了 就直接任务结束
        if (retryTime < 0) {
            processData(
                    new HttpResponseData(
                            new RuntimeException(
                                    "the maximum number of retries has been reached，task closed， httpClient value is "
                                            + this)));
            running = false;
            return;
        }

        // 执行请求
        String responseValue = null;
        int responseStatus;
        Header[] headers;
        try {

            HttpUriRequest request =
                    HttpUtil.getRequest(
                            restConfig.getRequestMode(),
                            currentParam.getBody(),
                            currentParam.getParam(),
                            currentParam.getHeader(),
                            restConfig.getUrl());
            // for http_hbase
            request.addHeader("Accept", "text/xml");
            CloseableHttpResponse httpResponse =
                    securityContext.execute(
                            request,
                            t -> {
                                try {
                                    return httpClient.execute(request);
                                } catch (IOException e) {
                                    throw new RuntimeException(e);
                                }
                            });
            if (!(httpResponse.getStatusLine().getStatusCode() == HttpStatus.SC_OK
                    || httpResponse.getStatusLine().getStatusCode() == HttpStatus.SC_NO_CONTENT)) {
                LOG.warn(
                        "httpStatus is {} and is not 200 ,try retry",
                        httpResponse.getStatusLine().getStatusCode());
                doExecute(
                        --requestRetryTime,
                        new RuntimeException(
                                "httpStatus is " + httpResponse.getStatusLine().getStatusCode()));
                return;
            }
            if (httpResponse.getStatusLine().getStatusCode() == HttpStatus.SC_NO_CONTENT) {
                responseValue = "";
                responseStatus = httpResponse.getStatusLine().getStatusCode();
                headers = httpResponse.getAllHeaders();
            } else {
                responseValue = EntityUtils.toString(httpResponse.getEntity());
                responseStatus = httpResponse.getStatusLine().getStatusCode();
                headers = httpResponse.getAllHeaders();
            }

        } catch (Exception e) {
            // 只要本次请求中出现了异常 都会进行重试，如果重试次数达到了就真正结束任务
            LOG.warn(
                    "httpClient value is {}, error info is {}",
                    this,
                    ExceptionUtil.getErrorMessage(e));
            doExecute(--requestRetryTime, e);
            return;
        }
        updateResponseInfo(currentParam, responseValue, headers);
        // 业务处理
        if (responseStatus == 204) {
            reachEnd = true;
            running = false;
        }
        processData(
                new HttpResponseData(
                        HttpRequestParam.copy(currentParam),
                        prevResponseHeader,
                        responseStatus,
                        responseValue));
        if (-1 != restConfig.getCycles() && requestNumber >= restConfig.getCycles()) {
            reachEnd = true;
            running = false;
        }

        prevParam = currentParam;
        prevResponse = responseValue;
    }
}
