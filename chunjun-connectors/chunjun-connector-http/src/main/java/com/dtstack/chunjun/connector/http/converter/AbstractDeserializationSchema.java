/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.http.converter;

import com.dtstack.chunjun.connector.http.client.CsvResponseParse;
import com.dtstack.chunjun.connector.http.client.HbaseXmlResponseParse;
import com.dtstack.chunjun.connector.http.client.JsonResponseParse;
import com.dtstack.chunjun.connector.http.client.ResponseParse;
import com.dtstack.chunjun.connector.http.client.TextResponseParse;
import com.dtstack.chunjun.connector.http.client.XmlResponseParse;
import com.dtstack.chunjun.connector.http.common.HttpRestConfig;
import com.dtstack.chunjun.connector.http.entity.HttpResponseData;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.table.data.DecimalData;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.StringData;
import org.apache.flink.table.types.logical.DecimalType;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.util.Collector;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalQueries;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.dtstack.chunjun.connector.http.common.ConstantValue.CSV_DECODE;
import static com.dtstack.chunjun.connector.http.common.ConstantValue.TEXT_DECODE;
import static com.dtstack.chunjun.connector.http.common.ConstantValue.XML_DECODE;

public abstract class AbstractDeserializationSchema implements DeserializationSchema<RowData> {
    public static Logger LOG = LoggerFactory.getLogger(AbstractDeserializationSchema.class);

    protected final TypeInformation<RowData> rowDataTypeInformation;
    protected final RowType rowType;
    protected final HttpRestConfig restConfig;
    protected ResponseParse responseParse;
    protected List<IDeserializationConverter> converters;

    public AbstractDeserializationSchema(
            HttpRestConfig restConfig,
            RowType rowType,
            TypeInformation<RowData> rowDataTypeInformation) {
        this.rowDataTypeInformation = rowDataTypeInformation;
        this.rowType = rowType;
        this.restConfig = restConfig;
        this.converters = new ArrayList<>(rowType.getFieldCount());
        for (int i = 0; i < rowType.getFieldCount(); i++) {
            converters.add(
                    wrapIntoNullableConverter(createInternalConverter(rowType.getTypeAt(i))));
        }
    }

    @Override
    public void open() {
        this.responseParse = getResponseParse(restConfig);
        responseParse.open();
    }

    @Override
    public TypeInformation<RowData> getProducedType() {
        return rowDataTypeInformation;
    }

    @Override
    public RowData deserialize(HttpResponseData document) throws IOException {
        throw new RuntimeException(
                "Please invoke DeserializationSchema#deserialize(byte[], Collector<RowData>) instead.");
    }

    public void deserialize(HttpResponseData document, Collector<RowData> out) throws IOException {
        responseParse.parse(
                document.getResponseContent(), document.getCode(), document.getRequestParam());
        while (responseParse.hasNext()) {
            try {
                Map<String, Object> data = responseParse.next();
                if (data != null) {
                    RowData rowData = createRowData(data);
                    if (rowData != null) {
                        out.collect(rowData);
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    protected abstract RowData createRowData(Map<String, Object> data) throws Exception;

    protected ResponseParse getResponseParse(HttpRestConfig restConfig) {
        if (StringUtils.isNotBlank(restConfig.getSourceType())
                && "hbase".equalsIgnoreCase(restConfig.getSourceType())) {
            return new HbaseXmlResponseParse(restConfig, rowType.getFieldNames());
        }

        switch (restConfig.getDecode()) {
            case CSV_DECODE:
                return new CsvResponseParse(restConfig, rowType.getFieldNames());
            case XML_DECODE:
                return new XmlResponseParse(restConfig, rowType.getFieldNames());
            case TEXT_DECODE:
                return new TextResponseParse(restConfig, rowType.getFieldNames());
            default:
                return new JsonResponseParse(restConfig, rowType.getFieldNames());
        }
    }

    protected IDeserializationConverter createInternalConverter(LogicalType type) {
        switch (type.getTypeRoot()) {
            case NULL:
                return val -> null;
            case BOOLEAN:
                return val ->
                        val instanceof Boolean ? ((Boolean) val) : Boolean.valueOf(val.toString());
            case INTEGER:
                return val -> Integer.valueOf(val.toString());
            case BIGINT:
                return val -> Long.valueOf(val.toString());
            case DATE:
                return (IDeserializationConverter<String, Integer>)
                        val -> {
                            LocalDate date =
                                    DateTimeFormatter.ISO_LOCAL_DATE
                                            .parse(val)
                                            .query(TemporalQueries.localDate());
                            return (int) date.toEpochDay();
                        };
            case FLOAT:
                return val -> Float.valueOf(val.toString());
            case DOUBLE:
                return val -> Double.valueOf(val.toString());
            case CHAR:
            case VARCHAR:
                return val -> StringData.fromString(val.toString());
            case DECIMAL:
                return (IDeserializationConverter<Object, DecimalData>)
                        val -> {
                            final int precision = ((DecimalType) type).getPrecision();
                            final int scale = ((DecimalType) type).getScale();
                            return DecimalData.fromBigDecimal(
                                    new BigDecimal(val.toString()), precision, scale);
                        };
            default:
                throw new UnsupportedOperationException("Unsupported type: " + type);
        }
    }

    public IDeserializationConverter wrapIntoNullableConverter(
            IDeserializationConverter deserializationConverter) {
        return val -> {
            if (val == null) {
                return null;
            } else {
                try {
                    return deserializationConverter.deserialize(val);
                } catch (Exception e) {
                    LOG.error("value [{}] convert failed ", val);
                    throw e;
                }
            }
        };
    }
}
