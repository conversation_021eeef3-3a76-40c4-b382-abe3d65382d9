/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.http.sink;

import com.dtstack.chunjun.connector.http.common.HttpWriterConfig;
import com.dtstack.chunjun.connector.http.converter.HttpSerializationSchema;

import org.apache.flink.util.InstantiationUtil;

import static org.apache.flink.util.Preconditions.checkNotNull;
import static org.apache.flink.util.Preconditions.checkState;

public class HttpSinkBuilder<IN> {

    private HttpSerializationSchema<IN> serializationSchema;
    private HttpWriterConfig config;

    public HttpSinkBuilder<IN> setSerializationSchema(
            HttpSerializationSchema<IN> serializationSchema) {
        checkNotNull(serializationSchema);
        checkState(
                InstantiationUtil.isSerializable(serializationSchema),
                "The mongo serialization schema must be serializable.");
        this.serializationSchema = serializationSchema;
        return this;
    }

    public HttpSinkBuilder<IN> setConfig(HttpWriterConfig config) {
        checkNotNull(config);
        this.config = config;
        return this;
    }

    public HttpSinkFunction<IN> build() {
        checkNotNull(serializationSchema, "The serialization schema must be supplied");
        checkNotNull(config, "The config must be supplied");
        return new HttpSinkFunction<>(serializationSchema, config);
    }
}
