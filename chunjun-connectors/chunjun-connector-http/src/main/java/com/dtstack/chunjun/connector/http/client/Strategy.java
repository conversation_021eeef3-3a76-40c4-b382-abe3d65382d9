/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.http.client;

import com.dtstack.chunjun.connector.http.common.ConstantValue;

import java.io.Serializable;

/**
 * 返回结果处理策略
 *
 * <AUTHOR>
 */
public class Strategy implements Serializable {

    /** param */
    private String key;
    /** scene */
    private String value;
    /** 比较方式 */
    private String compareMode = ConstantValue.COMPARE_STRATEGY_EQUAL;
    /** way */
    private String handle;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getHandle() {
        return handle;
    }

    public void setHandle(String handle) {
        this.handle = handle;
    }

    public String getCompareMode() {
        return compareMode;
    }

    public void setCompareMode(String compareMode) {
        this.compareMode = compareMode;
    }

    @Override
    public String toString() {
        return "Strategy{"
                + "key='"
                + key
                + '\''
                + ", value='"
                + value
                + '\''
                + ", compareMode='"
                + compareMode
                + '\''
                + ", handle='"
                + handle
                + '\''
                + '}';
    }
}
