package com.dtstack.chunjun.connector.http.converter;

import com.dtstack.chunjun.connector.http.common.HttpRestConfig;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.RowType;

import java.util.List;
import java.util.Map;

public class HttpDeserializationSchema extends AbstractDeserializationSchema {
    public HttpDeserializationSchema(
            HttpRestConfig restConfig,
            RowType rowType,
            TypeInformation<RowData> rowDataTypeInformation) {
        super(restConfig, rowType, rowDataTypeInformation);
    }

    @Override
    protected RowData createRowData(Map<String, Object> data) throws Exception {
        GenericRowData genericRowData = new GenericRowData(rowType.getFieldCount());
        List<String> columns = rowType.getFieldNames();
        for (int pos = 0; pos < columns.size(); pos++) {
            genericRowData.setField(
                    pos, converters.get(pos).deserialize(data.get(columns.get(pos))));
        }
        return genericRowData;
    }
}
