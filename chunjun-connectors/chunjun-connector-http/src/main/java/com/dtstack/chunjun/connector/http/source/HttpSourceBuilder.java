/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.http.source;

import com.dtstack.chunjun.connector.http.common.HttpRestConfig;
import com.dtstack.chunjun.connector.http.converter.DeserializationSchema;

import static org.apache.flink.util.Preconditions.checkNotNull;

public class HttpSourceBuilder<T> {
    private DeserializationSchema<T> deserializationSchema;

    private HttpRestConfig httpRestConfig;

    HttpSourceBuilder() {}

    public HttpSourceBuilder<T> setDeserializationSchema(
            DeserializationSchema<T> deserializationSchema) {
        checkNotNull(deserializationSchema, "The deserialization schema must not be null");
        this.deserializationSchema = deserializationSchema;
        return this;
    }

    public HttpSourceBuilder<T> setHttpConfig(HttpRestConfig httpRestConfig) {
        checkNotNull(httpRestConfig, "The httpRestConfig must not be null");
        this.httpRestConfig = httpRestConfig;
        return this;
    }

    public HttpSource<T> build() {
        checkNotNull(deserializationSchema, "The deserialization schema must be supplied");
        checkNotNull(httpRestConfig, "The httpRestConfig  must be supplied");
        return new HttpSource<>(deserializationSchema, httpRestConfig);
    }
}
