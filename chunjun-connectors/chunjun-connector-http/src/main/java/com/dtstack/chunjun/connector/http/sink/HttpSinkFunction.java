/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.http.sink;

import com.dtstack.chunjun.connector.http.common.HttpWriterConfig;
import com.dtstack.chunjun.connector.http.converter.HttpSerializationSchema;

import org.apache.flink.api.connector.sink2.Sink;
import org.apache.flink.api.connector.sink2.SinkWriter;
import org.apache.flink.api.connector.sink2.WriterInitContext;

import java.io.IOException;

public class HttpSinkFunction<IN> implements Sink<IN> {

    private final HttpSerializationSchema<IN> serializationSchema;
    private final HttpWriterConfig config;

    HttpSinkFunction(HttpSerializationSchema<IN> serializationSchema, HttpWriterConfig config) {
        this.serializationSchema = serializationSchema;
        this.config = config;
    }

    public static <IN> HttpSinkBuilder<IN> builder() {
        return new HttpSinkBuilder<>();
    }

    @Override
    public SinkWriter<IN> createWriter(WriterInitContext initContext) throws IOException {
        return new HttpWriter<>(serializationSchema, config, initContext);
    }
}
