/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.http.source.split;

import com.dtstack.chunjun.connector.http.client.HttpRequestParam;

import org.apache.flink.api.connector.source.SourceSplit;

public class HttpSplit implements SourceSplit {
    public static String SPLIT_ID = "http-split";
    /** last request param */
    private HttpRequestParam prevParam;

    /** last response body */
    private String prevResponse;

    protected final String splitId;

    public HttpSplit(String id, HttpRequestParam prevParam, String prevResponse) {
        this.prevParam = prevParam;
        this.prevResponse = prevResponse;
        this.splitId = id;
    }

    public HttpSplit(HttpRequestParam prevParam, String prevResponse) {
        this.prevParam = prevParam;
        this.prevResponse = prevResponse;
        this.splitId = SPLIT_ID;
    }

    @Override
    public String splitId() {
        return splitId;
    }

    public HttpRequestParam getPrevParam() {
        return prevParam;
    }

    public String getPrevResponse() {
        return prevResponse;
    }

    @Override
    public String toString() {
        return "HttpSplit{"
                + "prevParam="
                + prevParam
                + ", prevResponse='"
                + prevResponse
                + '\''
                + ", splitId='"
                + splitId
                + '\''
                + '}';
    }
}
