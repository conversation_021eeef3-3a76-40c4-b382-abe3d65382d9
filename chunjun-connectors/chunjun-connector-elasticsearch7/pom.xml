<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>chunjun-connectors</artifactId>
		<groupId>com.dtstack.chunjun</groupId>
		<version>2.0-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>chunjun-connector-elasticsearch7</artifactId>
	<name>ChunJun : Connectors : ElasticSearch7</name>

	<properties>
		<connector.dir>elasticsearch7</connector.dir>
		<version></version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.17.0</version>
			<scope>compile</scope>
		</dependency>
		<!-- ES 强依赖 log4j -->
		<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
			<version>1.2.17</version>
			<scope>compile</scope>
		</dependency>

<!--		<dependency>
			<groupId>org.elasticsearch.client</groupId>
			<artifactId>elasticsearch-rest-high-level-client</artifactId>
			<version>7.5.1</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-connector-elasticsearch7</artifactId>
			<version>${flink.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>log4j-to-slf4j</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.dtstack.chunjun</groupId>
			<artifactId>chunjun-connector-elasticsearch-base</artifactId>
			<version>${project.version}</version>
		</dependency>-->


		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-connector-elasticsearch7</artifactId>
			<version>3.0.1-1.17</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<createDependencyReducedPom>false</createDependencyReducedPom>
							<artifactSet>
								<excludes>
									<exclude>com.data-artisans:*</exclude>
									<exclude>org.scala-lang:*</exclude>
									<exclude>io.netty:*</exclude>
									<exclude>org.slf4j:slf4j-api</exclude>
									<exclude>ch.qos.logback:*</exclude>
								</excludes>
							</artifactSet>
							<filters>
								<filter>
									<artifact>*:*</artifact>
									<excludes>
										<exclude>META-INF/*.SF</exclude>
										<exclude>META-INF/*.DSA</exclude>
										<exclude>META-INF/*.RSA</exclude>
									</excludes>
								</filter>
							</filters>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
			</plugin>
		</plugins>
	</build>


</project>
