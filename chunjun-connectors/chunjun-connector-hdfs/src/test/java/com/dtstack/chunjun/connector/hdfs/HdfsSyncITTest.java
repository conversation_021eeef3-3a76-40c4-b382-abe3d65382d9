package com.dtstack.chunjun.connector.hdfs;

import com.dtstack.chunjun.Main;
import com.dtstack.chunjun.conf.ContentConf;
import com.dtstack.chunjun.conf.OperatorConf;
import com.dtstack.chunjun.conf.SyncConf;
import com.dtstack.chunjun.connector.hdfs.util.HDFSClusterUtil;
import com.dtstack.chunjun.common.util.GsonUtil;

import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.LocatedFileStatus;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.fs.RemoteIterator;
import org.apache.hadoop.fs.permission.FsAction;
import org.apache.hadoop.fs.permission.FsPermission;
import org.apache.hadoop.hdfs.MiniDFSCluster;
import org.junit.Assert;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.ClassOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestClassOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/8 19:34 星期一
 * @email <EMAIL>
 * @company www.dtstack.com
 */
@TestClassOrder(ClassOrderer.OrderAnnotation.class)
public class HdfsSyncITTest {

    public static Logger LOG = LoggerFactory.getLogger(HdfsSyncITTest.class);

    private static MiniDFSCluster miniDFSCluster;
    private static FileSystem fs;
    private static String textSourcePath = "/textSourcePath";
    private static String textSinkPath = "/textSinkPath";
    private static String orcSourcePath = "/orcSourcePath";
    private static String orcSinkPath = "/orcSinkPath";
    private static String parquetSourcePath = "/parquetSourcePath";
    private static String parquetSinkPath = "/parquetSinkPath";

    @BeforeAll
    public static void createMiniDFSCluster() {
        try {
            System.setProperty("user.name", "root");
            miniDFSCluster = HDFSClusterUtil.createMiniDFSCluster();
            // 创建文件系统
            fs = HDFSClusterUtil.getMiniDFSClusterFileSystem();
            // 构建 HDFS 路径
            List<String> paths = new ArrayList<>();
            paths.add(textSourcePath);
            paths.add(textSinkPath);
            paths.add(orcSourcePath);
            paths.add(orcSinkPath);
            paths.add(parquetSourcePath);
            paths.add(parquetSinkPath);
            // 创建 HDFS 上面的所有路径
            paths.forEach(
                    path -> {
                        try {
                            fs.mkdirs(
                                    new Path(path),
                                    new FsPermission(
                                            FsAction.READ_WRITE,
                                            FsAction.READ_WRITE,
                                            FsAction.READ_WRITE));
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                    });
        } catch (Throwable e) {
            e.printStackTrace();
            Assert.fail("Test failed " + e.getMessage());
        }
    }

    @Test
    public void testSyncStreamToHdfsText() throws Exception {
        runFlinkTaskOnLocal(
                "/json/text/stream_to_hdfs_text.json",
                (configuration, contentConf) -> {
                    String v1 = configuration.get("fs.defaultFS");
                    String v2 = configuration.get("dfs.namenode.rpc-address");

                    OperatorConf writer = contentConf.getWriter();
                    Map<String, Object> writerParameter = writer.getParameter();
                    writerParameter.put("path", HDFSClusterUtil.getMiniDFSUri() + textSourcePath);
                    writerParameter.put("defaultFS", v1);
                    LinkedTreeMap writerHadoopConfig =
                            (LinkedTreeMap) writerParameter.get("hadoopConfig");
                    if (writerHadoopConfig != null) {
                        writerHadoopConfig.put("fs.defaultFS", v1);
                        writerHadoopConfig.put("dfs.namenode.rpc-address", v2);
                    }
                });
    }

    @Test
    public void testSyncHdfsTextToStream() throws Exception {
        runFlinkTaskOnLocal(
                "/json/text/hdfs_text_to_stream.json",
                (configuration, contentConf) -> {
                    String v1 = configuration.get("fs.defaultFS");
                    String v2 = configuration.get("dfs.namenode.rpc-address");

                    OperatorConf reader = contentConf.getReader();
                    Map<String, Object> readerParameter = reader.getParameter();
                    readerParameter.put("path", HDFSClusterUtil.getMiniDFSUri() + textSourcePath);
                    readerParameter.put("defaultFS", v1);
                    LinkedTreeMap readerHadoopConfig =
                            (LinkedTreeMap) readerParameter.get("hadoopConfig");
                    if (readerHadoopConfig != null) {
                        readerHadoopConfig.put("fs.defaultFS", v1);
                        readerHadoopConfig.put("dfs.namenode.rpc-address", v2);
                    }
                });
    }

    @Test
    public void testSyncStreamToHdfsOrc() throws Exception {
        runFlinkTaskOnLocal(
                "/json/orc/stream_to_hdfs_orc.json",
                (configuration, contentConf) -> {
                    String v1 = configuration.get("fs.defaultFS");
                    String v2 = configuration.get("dfs.namenode.rpc-address");

                    OperatorConf writer = contentConf.getWriter();
                    Map<String, Object> writerParameter = writer.getParameter();
                    writerParameter.put("path", HDFSClusterUtil.getMiniDFSUri() + orcSourcePath);
                    writerParameter.put("defaultFS", v1);
                    LinkedTreeMap writerHadoopConfig =
                            (LinkedTreeMap) writerParameter.get("hadoopConfig");
                    if (writerHadoopConfig != null) {
                        writerHadoopConfig.put("fs.defaultFS", v1);
                        writerHadoopConfig.put("dfs.namenode.rpc-address", v2);
                    }
                });
    }

    @Test
    public void testSyncHdfsOrcToStream() throws Exception {
        runFlinkTaskOnLocal(
                "/json/orc/hdfs_orc_to_stream.json",
                (configuration, contentConf) -> {
                    String v1 = configuration.get("fs.defaultFS");
                    String v2 = configuration.get("dfs.namenode.rpc-address");

                    OperatorConf reader = contentConf.getReader();
                    Map<String, Object> readerParameter = reader.getParameter();
                    readerParameter.put("path", HDFSClusterUtil.getMiniDFSUri() + orcSourcePath);
                    readerParameter.put("defaultFS", v1);
                    LinkedTreeMap readerHadoopConfig =
                            (LinkedTreeMap) readerParameter.get("hadoopConfig");
                    if (readerHadoopConfig != null) {
                        readerHadoopConfig.put("fs.defaultFS", v1);
                        readerHadoopConfig.put("dfs.namenode.rpc-address", v2);
                    }
                });
    }

    @Test
    public void testSyncStreamToHdfsParquet() throws Exception {
        runFlinkTaskOnLocal(
                "/json/parquet/stream_to_hdfs_parquet.json",
                (configuration, contentConf) -> {
                    String v1 = configuration.get("fs.defaultFS");
                    String v2 = configuration.get("dfs.namenode.rpc-address");

                    OperatorConf writer = contentConf.getWriter();
                    Map<String, Object> writerParameter = writer.getParameter();
                    writerParameter.put(
                            "path", HDFSClusterUtil.getMiniDFSUri() + parquetSourcePath);
                    writerParameter.put("defaultFS", v1);
                    LinkedTreeMap writerHadoopConfig =
                            (LinkedTreeMap) writerParameter.get("hadoopConfig");
                    if (writerHadoopConfig != null) {
                        writerHadoopConfig.put("fs.defaultFS", v1);
                        writerHadoopConfig.put("dfs.namenode.rpc-address", v2);
                    }
                });
    }

    /**
     * 本地运行 Flink Task.
     *
     * @param taskJson 需要运行的 JSON 脚本
     * @param replaceValue 将脚本 taskJson 的值替换为 MiniDFSCluster 的配置的钩子
     * @throws Exception
     */
    private static void runFlinkTaskOnLocal(
            String taskJson, ReplaceParameter<Configuration, ContentConf> replaceValue)
            throws Exception {
        Configuration configuration = miniDFSCluster.getConfiguration(0);
        Properties confProperties = new Properties();
        confProperties.setProperty("flink.checkpoint.interval", "10000");
        //        confProperties.setProperty("state.backend","ROCKSDB");
        //        confProperties.setProperty("state.checkpoints.num-retained", "10");
        //        confProperties.setProperty("state.checkpoints.dir", "file:///ck");
        String userDir = System.getProperty("user.dir");
        String jobPath =
                Objects.requireNonNull(HdfsSyncITTest.class.getResource(taskJson)).getPath();
        String flinkxDistDir = userDir + "/chunjun-dist";
        String s = "";

        // 任务配置参数
        List<String> argsList = new ArrayList<>();
        argsList.add("-mode");
        argsList.add("local");
        String content = readFile(jobPath);

        SyncConf config = SyncConf.parseJob(content);
        ContentConf contentConf = config.getJob().getContent().get(0);
        // 替换参数
        replaceValue.replace(configuration, contentConf);

        content = GsonUtil.GSON.toJson(config);
        if (StringUtils.endsWith(jobPath, "json")) {
            argsList.add("-jobType");
            argsList.add("sync");
            argsList.add("-job");
            argsList.add(URLEncoder.encode(content, StandardCharsets.UTF_8.name()));
            argsList.add(GsonUtil.GSON.toJson(confProperties));
        } else if (StringUtils.endsWith(jobPath, "sql")) {
            argsList.add("-jobType");
            argsList.add("sql");
            argsList.add("-job");
            argsList.add(URLEncoder.encode(content, StandardCharsets.UTF_8.name()));
            //            argsList.add("-flinkConfDir");
            //            argsList.add("/opt/dtstack/flink-1.12.2/conf/");
            argsList.add("-jobName");
            argsList.add("flinkStreamSQLLocalTest");
            argsList.add("-flinkxDistDir");
            argsList.add(flinkxDistDir);
            argsList.add("-remoteFlinkxDistDir");
            argsList.add(flinkxDistDir);
            argsList.add("-pluginLoadMode");
            argsList.add("LocalTest");
            //            argsList.add("-confProp");
        }
        Main.main(argsList.toArray(new String[0]));
        LOG.info(
                " ======================  testHdfsOrcToStream() SUCCESS ====================================");
    }

    @FunctionalInterface
    public interface ReplaceParameter<C, T> {
        void replace(C c, T t);
    }

    private static String readFile(String sqlPath) {
        try {
            byte[] array = Files.readAllBytes(Paths.get(sqlPath));
            return new String(array, StandardCharsets.UTF_8);
        } catch (IOException ioe) {
            // LOG.error("Can not get the job info !!!", ioe);
            throw new RuntimeException(ioe);
        }
    }

    @AfterAll
    public static void printHDFSFile() throws IOException {
        System.out.println(
                "=====================================================================================");
        RemoteIterator<LocatedFileStatus> locatedFileStatusRemoteIterator =
                fs.listFiles(new Path("/"), true);
        while (locatedFileStatusRemoteIterator.hasNext()) {
            System.out.println(locatedFileStatusRemoteIterator.next());
        }
        System.out.println(
                "=====================================================================================");
    }
}
