package com.dtstack.chunjun.connector.hdfs.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/23 10:43 星期二
 * @email <EMAIL>
 * @company www.dtstack.com
 */
public class TestFileUtil {

    public static Logger LOG = LoggerFactory.getLogger(TestFileUtil.class);

    public static String readFile(String sqlPath) {
        try {
            byte[] array = Files.readAllBytes(Paths.get(sqlPath));
            return new String(array, StandardCharsets.UTF_8);
        } catch (IOException ioe) {
            LOG.error("Can not get the job info !!!", ioe);
        }
        return null;
    }
}
