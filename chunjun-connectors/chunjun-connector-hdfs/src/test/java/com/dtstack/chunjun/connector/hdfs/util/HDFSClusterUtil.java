package com.dtstack.chunjun.connector.hdfs.util;

import com.dtstack.chunjun.connector.hdfs.HdfsSqlITTest;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.hdfs.MiniDFSCluster;
import org.junit.Assert;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/22 15:54 星期一
 * @email <EMAIL>
 * @company www.dtstack.com
 */
public class HDFSClusterUtil {

    public static Logger LOG = LoggerFactory.getLogger(HdfsSqlITTest.class);
    private static String miniDFSUri;
    private static volatile MiniDFSCluster miniDFSCluster = null;
    private static final String userName;

    static {
        System.setProperty("user.name", "root");
        userName = System.getProperty("user.name");
    }

    public static MiniDFSCluster createMiniDFSCluster() {
        return checkHDFSClusterInitial();
    }

    private static MiniDFSCluster getMiniDFSCluster() throws Exception {
        try {
            Configuration configuration = new Configuration();
            // 启动 HDFS Mini 集群。
            File baseDir = new File("./target/hdfs/hdfsTest").getAbsoluteFile();
            // FileUtil.fullyDelete(baseDir);
            configuration.set(MiniDFSCluster.HDFS_MINIDFS_BASEDIR, baseDir.getAbsolutePath());
            configuration.set(
                    "dfs.block.size", String.valueOf(1048576)); // this is the minimum we can set.
            MiniDFSCluster.Builder builder = new MiniDFSCluster.Builder(configuration);
            miniDFSCluster = builder.build();
            InitMiniDFSUri();
            return miniDFSCluster;
        } catch (Throwable e) {
            Assert.fail("Test failed " + e.getMessage());
            throw new Exception("MiniDFSCluster initial fail.", e);
        }
    }

    public static MiniDFSCluster getHDFSCluster() {
        checkHDFSClusterInitial();
        return miniDFSCluster;
    }

    public static Configuration getMiniDFSClusterConfiguration() {
        checkHDFSClusterInitial();
        return miniDFSCluster.getConfiguration(0);
    }

    public static FileSystem getMiniDFSClusterFileSystem() {
        checkHDFSClusterInitial();
        FileSystem fs;
        try {
            fs = FileSystem.get(new URI(miniDFSUri), getMiniDFSClusterConfiguration(), userName);
        } catch (IOException | URISyntaxException | InterruptedException e) {
            throw new RuntimeException(e);
        }
        return fs;
    }

    private static MiniDFSCluster checkHDFSClusterInitial() {
        // double check
        if (miniDFSCluster == null) {
            synchronized (HDFSClusterUtil.class) {
                if (miniDFSCluster == null) {
                    try {
                        return getMiniDFSCluster();
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }
        return miniDFSCluster;
    }

    private static void InitMiniDFSUri() {
        miniDFSUri =
                "hdfs://"
                        + miniDFSCluster.getURI().getHost()
                        + ":"
                        + miniDFSCluster.getNameNodePort()
                        + "/";
    }

    public static String getUserName() {
        return userName;
    }

    public static String getMiniDFSUri() {
        checkHDFSClusterInitial();
        return miniDFSUri;
    }
}
