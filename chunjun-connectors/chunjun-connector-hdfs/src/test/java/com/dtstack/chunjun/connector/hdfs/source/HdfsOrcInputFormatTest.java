package com.dtstack.chunjun.connector.hdfs.source;

import com.dtstack.chunjun.connector.hdfs.InputSplit.HdfsOrcInputSplit;
import com.dtstack.chunjun.connector.hdfs.conf.HdfsConf;

import org.apache.flink.testutils.TestFileUtils;
import org.apache.flink.util.StringUtils;

import org.junit.Assert;
import org.junit.Rule;
import org.junit.jupiter.api.Test;
import org.junit.rules.ExpectedException;
import org.junit.rules.TemporaryFolder;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/8 17:59 星期一
 * @email <EMAIL>
 * @company www.dtstack.com
 */
public class HdfsOrcInputFormatTest {

    private static final Random RND = new Random();

    protected org.apache.hadoop.fs.FileSystem hdfs;

    @Rule public TemporaryFolder temporaryFolder = new TemporaryFolder();

    @Rule public final ExpectedException exception = ExpectedException.none();

    @Test
    public void testCreateFileSplit() throws Exception {

        String tempFile =
                TestFileUtils.createTempFile(
                        "1,false,1,2,3,4,5.0,6.0,7,8,9,0                                                                                                                                                                                                                                                              ,\\N,2022-07-11 16:37:17,2022-07-11");
        try {
            HdfsConf hdfsConf = mock(HdfsConf.class);
            when(hdfsConf.getDefaultFS()).thenReturn("file:///");
            when(hdfsConf.getFileType()).thenReturn("text");
            when(hdfsConf.getWriteMode()).thenReturn("APPEND");
            when(hdfsConf.getFieldDelimiter()).thenReturn(",");
            when(hdfsConf.getFilterRegex()).thenReturn("");
            // when(hdfsConf.getPath()).thenReturn("/Users/<USER>/Desktop/DTStack/flinkx_1.12/flinkx_release/flinkx/flinkx-connectors/flinkx-connector-hdfs/src/test/resources/data/000000_0");
            when(hdfsConf.getPath()).thenReturn(tempFile);
            when(hdfsConf.getFileName()).thenReturn("");
            when(hdfsConf.getEncoding()).thenReturn("utf-8");

            HdfsOrcInputFormat hdfsOrcInputFormat = new HdfsOrcInputFormat();
            hdfsOrcInputFormat.setHdfsConf(hdfsConf);
            HdfsOrcInputSplit[] hdfsSplit = hdfsOrcInputFormat.createHdfsSplit(1);

            Assert.assertEquals(1, hdfsSplit.length);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static String randomName() {
        return StringUtils.getRandomString(RND, 16, 16, 'a', 'z');
    }

    // @Test
    public void testCreateHdfsSplit() throws Exception {
        try {

            HdfsConf hdfsConf = mock(HdfsConf.class);

            Map<String, Object> hadoopConfig = hdfsConf.getHadoopConfig();
            String defaultFS = hdfsConf.getDefaultFS();

            when(hdfsConf.getHadoopConfig()).thenReturn(new HashMap<>());
            when(hdfsConf.getDefaultFS()).thenReturn("hdfs://ns");
            when(hdfsConf.getFilterRegex()).thenReturn("");

            when(hdfsConf.getPath()).thenReturn("");
            when(hdfsConf.getFileName()).thenReturn("");

            HdfsOrcInputFormat hdfsOrcInputFormat = new HdfsOrcInputFormat();
            hdfsOrcInputFormat.setHdfsConf(hdfsConf);

            HdfsOrcInputSplit[] hdfsSplit = hdfsOrcInputFormat.createHdfsSplit(1);

            // hdfsOrcInputFormat.createInputSplits(1);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
