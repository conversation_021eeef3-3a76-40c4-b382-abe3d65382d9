package com.dtstack.chunjun.connector.hdfs.source;

import com.dtstack.chunjun.conf.SyncConf;
import com.dtstack.chunjun.environment.MyLocalStreamEnvironment;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.data.RowData;

import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/22 20:07 星期一
 * @email <EMAIL>
 * @company www.dtstack.com
 */
class HdfsSourceFactoryTest {

    @Test
    public void testCreateSource() throws Exception {

        String content =
                "{\n"
                        + "  \"job\": {\n"
                        + "    \"content\": [\n"
                        + "      {\n"
                        + "        \"reader\" : {\n"
                        + "          \"parameter\" : {\n"
                        + "            \"path\" : \"hdfs://localhost:9000/test_orc_source\",\n"
                        + "            \"hadoopConfig\": {\n"
                        + "              \"hadoop.user.name\": \"root\",\n"
                        + "              \"dfs.namenode.rpc-address\": \"localhost:9999\",\n"
                        + "              \"fs.defaultFS\": \"hdfs://localhost:9999\"\n"
                        + "            },\n"
                        + "            \"fullColumnName\" : [\"id\",\"col_boolean\",\"col_tinyint\",\"col_smallint\",\"col_int\",\"col_bigint\",\"col_float\",\"col_double\",\"col_decimal\",\"col_string\",\"col_varchar\",\"col_char\",\"col_binary\",\"col_timestamp\",\"col_date\"],\n"
                        + "            \"fullColumnType\" : [\"int\",\"boolean\",\"tinyint\",\"int\",\"int\",\"bigint\",\"float\",\"double\",\"decimal\",\"string\",\"varchar(255)\",\"char(255)\",\"binary\",\"timestamp\",\"date\"],\n"
                        + "            \"defaultFS\": \"hdfs://localhost:9999\",\n"
                        + "            \"encoding\" : \"utf-8\",\n"
                        + "            \"fileType\" : \"orc\",\n"
                        + "            \"column\": [\n"
                        + "              {\n"
                        + "                \"name\": \"id\",\n"
                        + "                \"type\": \"int\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_boolean\",\n"
                        + "                \"type\": \"boolean\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_tinyint\",\n"
                        + "                \"type\": \"tinyint\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_smallint\",\n"
                        + "                \"type\": \"int\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_int\",\n"
                        + "                \"type\": \"int\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_bigint\",\n"
                        + "                \"type\": \"bigint\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_float\",\n"
                        + "                \"type\": \"float\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_double\",\n"
                        + "                \"type\": \"double\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_decimal\",\n"
                        + "                \"type\": \"decimal\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_string\",\n"
                        + "                \"type\": \"string\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_varchar\",\n"
                        + "                \"type\": \"varchar(255)\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_char\",\n"
                        + "                \"type\": \"char(255)\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_binary\",\n"
                        + "                \"type\": \"binary\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_timestamp\",\n"
                        + "                \"type\": \"timestamp\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_date\",\n"
                        + "                \"type\": \"date\"\n"
                        + "              }\n"
                        + "            ]\n"
                        + "          },\n"
                        + "          \"name\" : \"hdfsreader\"\n"
                        + "        },\n"
                        + "        \"writer\": {\n"
                        + "          \"name\": \"hdfswriter\",\n"
                        + "          \"parameter\": {\n"
                        + "            \"path\": \"hdfs://localhost:9000/test_orc_sink\",\n"
                        + "            \"defaultFS\": \"hdfs://localhost:9999\",\n"
                        + "            \"fileType\": \"orc\",\n"
                        + "            \"maxFileSize\": 10485760,\n"
                        + "            \"nextCheckRows\": 20000,\n"
                        + "            \"fieldDelimiter\": \",\",\n"
                        + "            \"encoding\": \"utf-8\",\n"
                        + "            \"fileName\": \"\",\n"
                        + "            \"writeMode\": \"overwrite\",\n"
                        + "            \"hadoopConfig\": {\n"
                        + "              \"hadoop.user.name\": \"root\",\n"
                        + "              \"dfs.namenode.rpc-address\": \"localhost:9999\",\n"
                        + "              \"fs.defaultFS\": \"hdfs://localhost:9999\"\n"
                        + "            },\n"
                        + "            \"fullColumnName\" : [\"id\",\"col_boolean\",\"col_tinyint\",\"col_smallint\",\"col_int\",\"col_bigint\",\"col_float\",\"col_double\",\"col_decimal\",\"col_string\",\"col_varchar\",\"col_char\",\"col_binary\",\"col_timestamp\",\"col_date\"],\n"
                        + "            \"fullColumnType\" : [\"int\",\"boolean\",\"tinyint\",\"int\",\"int\",\"bigint\",\"float\",\"double\",\"decimal\",\"string\",\"varchar(255)\",\"char(255)\",\"binary\",\"timestamp\",\"date\"],\n"
                        + "            \"column\": [\n"
                        + "              {\n"
                        + "                \"name\": \"id\",\n"
                        + "                \"type\": \"int\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_boolean\",\n"
                        + "                \"type\": \"boolean\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_tinyint\",\n"
                        + "                \"type\": \"tinyint\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_smallint\",\n"
                        + "                \"type\": \"int\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_int\",\n"
                        + "                \"type\": \"int\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_bigint\",\n"
                        + "                \"type\": \"bigint\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_float\",\n"
                        + "                \"type\": \"float\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_double\",\n"
                        + "                \"type\": \"double\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_decimal\",\n"
                        + "                \"type\": \"decimal\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_string\",\n"
                        + "                \"type\": \"string\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_varchar\",\n"
                        + "                \"type\": \"varchar(255)\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_char\",\n"
                        + "                \"type\": \"char(255)\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_binary\",\n"
                        + "                \"type\": \"binary\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_timestamp\",\n"
                        + "                \"type\": \"timestamp\"\n"
                        + "              },\n"
                        + "              {\n"
                        + "                \"name\": \"col_date\",\n"
                        + "                \"type\": \"date\"\n"
                        + "              }\n"
                        + "            ]\n"
                        + "          }\n"
                        + "        }\n"
                        + "      }\n"
                        + "    ],\n"
                        + "    \"setting\": {\n"
                        + "      \"speed\": {\n"
                        + "        \"channel\": 1,\n"
                        + "        \"bytes\": 0\n"
                        + "      }\n"
                        + "    }\n"
                        + "  }\n"
                        + "}";

        SyncConf config = SyncConf.parseJob(content);

        Configuration flinkConf = new Configuration();
        MyLocalStreamEnvironment env = new MyLocalStreamEnvironment(flinkConf);

        HdfsSourceFactory sourceFactory = new HdfsSourceFactory(config, env);
        DataStream<RowData> source = sourceFactory.createSource();
    }
}
