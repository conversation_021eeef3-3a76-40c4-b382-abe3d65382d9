package com.dtstack.chunjun.connector.hdfs.source;

import com.dtstack.chunjun.connector.hdfs.conf.HdfsConf;

import org.apache.flink.core.io.InputSplit;
import org.apache.flink.testutils.TestFileUtils;

import org.junit.Assert;
import org.junit.jupiter.api.Test;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/9 14:33 星期二
 * @email <EMAIL>
 * @company www.dtstack.com
 */
class HdfsTextInputFormatTest {

    @Test
    public void testCreateFileSplit() throws Exception {
        String tempFile =
                TestFileUtils.createTempFile(
                        "1,false,1,2,3,4,5.0,6.0,7,8,9,0                                                                                                                                                                                                                                                              ,\\N,2022-07-11 16:37:17,2022-07-11");
        System.out.println(tempFile);
        try {
            HdfsConf hdfsConf = mock(HdfsConf.class);
            when(hdfsConf.getDefaultFS()).thenReturn("file:///");
            when(hdfsConf.getFileType()).thenReturn("text");
            when(hdfsConf.getWriteMode()).thenReturn("APPEND");
            when(hdfsConf.getFieldDelimiter()).thenReturn(",");
            when(hdfsConf.getFilterRegex()).thenReturn("");
            when(hdfsConf.getPath()).thenReturn(tempFile);
            when(hdfsConf.getFileName()).thenReturn("");
            when(hdfsConf.getEncoding()).thenReturn("utf-8");

            HdfsTextInputFormat hdfsTextInputFormat = new HdfsTextInputFormat();
            hdfsTextInputFormat.setHdfsConf(hdfsConf);
            InputSplit[] hdfsSplit = hdfsTextInputFormat.createHdfsSplit(1);
            Assert.assertEquals(1, hdfsSplit.length);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
