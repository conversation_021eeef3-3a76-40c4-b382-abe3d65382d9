{"job": {"content": [{"reader": {"parameter": {"sliceRecordCount": ["5"], "column": [{"name": "id", "type": "int"}, {"name": "col_boolean", "type": "boolean"}, {"name": "col_tinyint", "type": "tinyint"}, {"name": "col_smallint", "type": "int"}, {"name": "col_int", "type": "int"}, {"name": "col_bigint", "type": "bigint"}, {"name": "col_float", "type": "float"}, {"name": "col_double", "type": "double"}, {"name": "col_decimal", "type": "decimal"}, {"name": "col_string", "type": "string"}, {"name": "col_varchar", "type": "<PERSON><PERSON><PERSON>(255)"}, {"name": "col_char", "type": "char(255)"}, {"name": "col_binary", "type": "binary"}, {"name": "col_timestamp", "type": "timestamp"}, {"name": "col_date", "type": "date"}]}, "name": "streamreader"}, "writer": {"name": "hdfswriter", "parameter": {"path": "hdfs://localhost:9000/test_orc_target", "defaultFS": "hdfs://localhost:9999", "fileType": "orc", "maxFileSize": 10485760, "nextCheckRows": 20000, "fieldDelimiter": ",", "encoding": "utf-8", "fileName": "", "writeMode": "overwrite", "hadoopConfig": {"hadoop.user.name": "root", "dfs.namenode.rpc-address": "localhost:9999", "fs.defaultFS": "hdfs://localhost:9999"}, "fullColumnName": ["id", "col_boolean", "col_tinyint", "col_smallint", "col_int", "col_bigint", "col_float", "col_double", "col_decimal", "col_string", "col_varchar", "col_char", "col_binary", "col_timestamp", "col_date"], "fullColumnType": ["int", "boolean", "tinyint", "int", "int", "bigint", "float", "double", "decimal", "string", "<PERSON><PERSON><PERSON>(255)", "char(255)", "binary", "timestamp", "date"], "column": [{"name": "id", "type": "int"}, {"name": "col_boolean", "type": "boolean"}, {"name": "col_tinyint", "type": "tinyint"}, {"name": "col_smallint", "type": "int"}, {"name": "col_int", "type": "int"}, {"name": "col_bigint", "type": "bigint"}, {"name": "col_float", "type": "float"}, {"name": "col_double", "type": "double"}, {"name": "col_decimal", "type": "decimal"}, {"name": "col_string", "type": "string"}, {"name": "col_varchar", "type": "<PERSON><PERSON><PERSON>(255)"}, {"name": "col_char", "type": "char(255)"}, {"name": "col_binary", "type": "binary"}, {"name": "col_timestamp", "type": "timestamp"}, {"name": "col_date", "type": "date"}]}}}], "setting": {"speed": {"channel": 1, "bytes": 0}}}}