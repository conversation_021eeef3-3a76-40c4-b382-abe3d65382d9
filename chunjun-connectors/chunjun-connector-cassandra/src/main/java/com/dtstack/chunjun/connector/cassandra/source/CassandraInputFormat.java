/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.cassandra.source;

import com.dtstack.chunjun.connector.cassandra.conf.CassandraSourceConf;
import com.dtstack.chunjun.connector.cassandra.util.CassandraService;
import com.dtstack.chunjun.throwable.ReadRecordException;

import org.apache.flink.core.io.InputSplit;
import org.apache.flink.table.data.RowData;

import com.datastax.driver.core.ConsistencyLevel;
import com.datastax.driver.core.Row;
import com.datastax.driver.core.Session;
import com.datastax.driver.core.querybuilder.QueryBuilder;
import com.datastax.driver.core.querybuilder.Select;
import com.google.common.base.Preconditions;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.Optional;

import static com.dtstack.chunjun.connector.cassandra.util.CassandraService.quoteColumn;

public class CassandraInputFormat extends BaseRichInputFormat {

    protected transient Iterator<Row> cursor;
    private CassandraSourceConf sourceConf;
    private transient Session session;

    @Override
    protected InputSplit[] createInputSplitsInternal(int minNumSplits) {
        ArrayList<CassandraInputSplit> splits = new ArrayList<>();

        try {
            Preconditions.checkNotNull(sourceConf.getTableName(), "table must not null");
            return CassandraService.splitJob(sourceConf, minNumSplits, splits);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            CassandraService.close(session);
        }
    }

    @Override
    protected void openInternal(InputSplit inputSplit) {
        LOG.info(
                "Execute openInternal: splitNumber = {}, indexOfSubtask  = {}",
                inputSplit.getSplitNumber(),
                indexOfSubTask);

        CassandraInputSplit split = (CassandraInputSplit) inputSplit;

        String tableName = sourceConf.getTableName();
        String keyspaces = sourceConf.getKeyspaces();

        Optional<String> where = Optional.ofNullable(sourceConf.getWhere());

        Preconditions.checkNotNull(tableName, "table must not null");

        sourceConf
                .getColumn()
                .forEach(fieldConf -> columnNameList.add(quoteColumn(fieldConf.getName())));

        session = CassandraService.session(sourceConf);

        String consistency = sourceConf.getConsistency();
        ConsistencyLevel consistencyLevel = CassandraService.consistencyLevel(consistency);

        Select select = QueryBuilder.select(columnNameList.toArray()).from(keyspaces, tableName);
        where.ifPresent(
                clause ->
                        cursor =
                                session.execute(dealWhereClause(select, clause)).all().stream()
                                        .iterator());
        select.setConsistencyLevel(consistencyLevel);
        // TODO where ? group by ? order by ?

        LOG.info("split: {}, {}", split.getMinToken(), split.getMaxToken());
        if (cursor == null) {
            cursor = session.execute(select).all().stream().iterator();
        }
    }

    public String dealWhereClause(Select select, String whereClause) {
        String queryString = select.getQueryString();
        StringBuilder sb = new StringBuilder(queryString);

        // remove ";"
        sb.deleteCharAt(queryString.length() - 1);

        return sb.append(" WHERE ").append(whereClause).toString();
    }

    @Override
    protected RowData nextRecordInternal(RowData rowData) throws ReadRecordException {
        try {
            Row cqlRow = cursor.next();
            rowData = converter.toInternal(cqlRow);

        } catch (Exception e) {
            throw new ReadRecordException("Cassandra next record error!", e, -1, rowData);
        }

        return rowData;
    }

    @Override
    protected void closeInternal() {
        CassandraService.close(session);
    }

    @Override
    public boolean reachedEnd() {
        return !cursor.hasNext();
    }

    public CassandraSourceConf getSourceConf() {
        return sourceConf;
    }

    public void setSourceConf(CassandraSourceConf sourceConf) {
        this.sourceConf = sourceConf;
    }
}
