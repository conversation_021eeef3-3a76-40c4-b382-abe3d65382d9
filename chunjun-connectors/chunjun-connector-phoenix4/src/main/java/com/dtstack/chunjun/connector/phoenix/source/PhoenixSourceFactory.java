/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.phoenix.source;

import com.dtstack.chunjun.conf.SyncConf;
import com.dtstack.chunjun.connector.jdbc.conf.JdbcConf;
import com.dtstack.chunjun.connector.jdbc.source.JdbcInputFormatBuilder;
import com.dtstack.chunjun.connector.jdbc.source.JdbcSourceFactory;
import com.dtstack.chunjun.connector.phoenix.PhoenixDialect;
import com.dtstack.chunjun.connector.phoenix.conf.PhoenixConf;
import com.dtstack.chunjun.connector.phoenix.converter.PhoenixRawTypeMapper;
import com.dtstack.chunjun.connector.phoenix.util.PhoenixUtil;
import com.dtstack.chunjun.converter.RawTypeMapper;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.data.RowData;

import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/9 16:01 星期五
 * @email <EMAIL>
 * @company www.dtstack.com
 */
public class PhoenixSourceFactory extends JdbcSourceFactory {

    private final PhoenixConf phoenixConf;

    public PhoenixSourceFactory(SyncConf syncConf, StreamExecutionEnvironment env) {
        super(syncConf, env, new PhoenixDialect());
        phoenixConf = (PhoenixConf) jdbcConf;
        if (!phoenixConf.isReadFromHbase()) {
            if (jdbcConf.isPolling()
                    && StringUtils.isEmpty(jdbcConf.getStartLocation())
                    && jdbcConf.getFetchSize() == 0) {
                jdbcConf.setFetchSize(1000);
            }
        }
    }

    @Override
    protected Class<? extends JdbcConf> getConfClass() {
        return PhoenixConf.class;
    }

    @Override
    public DataStream<RowData> createSource() {
        if (phoenixConf.isReadFromHbase()) {
            HBaseInputFormatBuilder builder = new HBaseInputFormatBuilder();
            builder.setPhoenix5Conf(phoenixConf);
            // set sync task or sql task.
            phoenixConf.setSyncTaskType(isSyncJob);
            return createInput(builder.finish());
        } else {
            return super.createSource();
        }
    }

    @Override
    protected JdbcInputFormatBuilder getBuilder() {
        return new PhoenixInputFormatBuilder(new PhoenixInputFormat());
    }

    @Override
    public RawTypeMapper getRawTypeMapper() {
        return PhoenixRawTypeMapper::apply;
    }

    @Override
    protected Connection getConn() {
        return PhoenixUtil.getConnection(
                jdbcDialect
                        .defaultDriverName()
                        .orElseThrow(
                                () ->
                                        new IllegalArgumentException(
                                                "Can not find any driver name in "
                                                        + jdbcDialect.dialectName())),
                phoenixConf);
    }
}
