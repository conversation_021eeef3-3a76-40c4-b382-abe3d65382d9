/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.phoenix.sink;

import com.dtstack.chunjun.conf.SyncConf;
import com.dtstack.chunjun.connector.jdbc.sink.JdbcOutputFormatBuilder;
import com.dtstack.chunjun.connector.jdbc.sink.JdbcSinkFactory;
import com.dtstack.chunjun.connector.phoenix.PhoenixDialect;
import com.dtstack.chunjun.connector.phoenix.converter.PhoenixRawTypeMapper;
import com.dtstack.chunjun.connector.phoenix.util.PhoenixUtil;
import com.dtstack.chunjun.converter.RawTypeMapper;

import java.sql.Connection;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/9 16:01 星期五
 * @email <EMAIL>
 * @company www.dtstack.com
 */
public class PhoenixSinkFactory extends JdbcSinkFactory {

    private static final String ALL_TABLE = "*";

    public PhoenixSinkFactory(SyncConf syncConf) {
        super(syncConf, new PhoenixDialect());
    }

    @Override
    protected JdbcOutputFormatBuilder getBuilder() {
        return new PhoenixOutputFormatBuilder(new PhoenixOutputFormat());
    }

    @Override
    public RawTypeMapper getRawTypeMapper() {
        return PhoenixRawTypeMapper::apply;
    }

    @Override
    protected Connection getConn() {
        return PhoenixUtil.getConnection(jdbcDialect.defaultDriverName().get(), jdbcConf);
    }
}
