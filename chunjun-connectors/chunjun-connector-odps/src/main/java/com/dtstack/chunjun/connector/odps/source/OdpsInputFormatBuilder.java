/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.odps.source;

import com.dtstack.chunjun.connector.odps.conf.OdpsConfig;
import com.dtstack.chunjun.connector.odps.conf.OdpsSourceConf;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

public class OdpsInputFormatBuilder extends BaseRichInputFormatBuilder<OdpsInputFormat> {

    public OdpsInputFormatBuilder() {
        super.format = new OdpsInputFormat();
    }

    public void setOdpsSourceConf(OdpsSourceConf conf) {
        super.setConfig(conf);
        format.setConf(conf);
    }

    @Override
    protected void checkFormat() {
        OdpsSourceConf conf = format.getConf();
        StringBuilder errorMsg = new StringBuilder();
        if (Objects.isNull(conf.getOdpsConfig())) {
            errorMsg.append(
                    "odpsConfig can not empty, you should config  [accessId]  or  [accessKey]  or [project]");
        } else {
            OdpsConfig odpsConfig = conf.getOdpsConfig();
            if (StringUtils.isBlank(odpsConfig.getProject())) {
                errorMsg.append("odpsConfig  should config [project]");
            }
        }
        if (errorMsg.length() > 0) {
            throw new IllegalArgumentException(errorMsg.toString());
        }
    }
}
