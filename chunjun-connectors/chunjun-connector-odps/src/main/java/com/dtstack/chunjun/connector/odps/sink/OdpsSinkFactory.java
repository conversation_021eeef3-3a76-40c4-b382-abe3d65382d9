/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.odps.sink;

import com.dtstack.chunjun.conf.FieldConf;
import com.dtstack.chunjun.conf.SyncConf;
import com.dtstack.chunjun.connector.odps.conf.OdpsSinkConf;
import com.dtstack.chunjun.connector.odps.converter.OdpsRawTypeMapper;
import com.dtstack.chunjun.connector.odps.converter.OdpsSyncConverter;
import com.dtstack.chunjun.converter.RawTypeMapper;
import com.dtstack.chunjun.sink.SinkFactory;
import com.dtstack.chunjun.common.util.JsonUtil;
import com.dtstack.chunjun.util.TableUtil;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSink;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.RowType;

import java.util.List;

public class OdpsSinkFactory extends SinkFactory {

    private final OdpsSinkConf sinkConf;

    public OdpsSinkFactory(SyncConf syncConf) {
        super(syncConf);

        sinkConf =
                JsonUtil.toObject(
                        JsonUtil.toJson(syncConf.getWriter().getParameter()), OdpsSinkConf.class);
        if (sinkConf.getBufferSize() == null) {
            sinkConf.setBufferSize(String.valueOf(64 * 1024 * 1024));
        } else {
            sinkConf.setBufferSize(
                    String.valueOf(Long.parseLong(sinkConf.getBufferSize()) * 1024 * 1024));
        }

        sinkConf.setColumn(syncConf.getWriter().getFieldList());
        super.initFlinkxCommonConf(sinkConf);
    }

    @Override
    public RawTypeMapper getRawTypeMapper() {
        return OdpsRawTypeMapper::apply;
    }

    @Override
    public DataStreamSink<RowData> createSink(DataStream<RowData> dataSet) {
        OdpsOutputFormatBuilder builder = new OdpsOutputFormatBuilder();

        builder.setConf(sinkConf);
        List<FieldConf> fieldConfList = sinkConf.getColumn();

        final RowType rowType = TableUtil.createRowType(fieldConfList, getRawTypeMapper());
        builder.setRowConverter(new OdpsSyncConverter(rowType, sinkConf), isSyncJob);
        return createOutput(dataSet, builder.finish());
    }
}
