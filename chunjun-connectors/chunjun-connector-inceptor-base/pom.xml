<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>chunjun-connectors</artifactId>
		<groupId>com.dtstack.chunjun</groupId>
		<version>2.0-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>chunjun-connector-inceptor-base</artifactId>
	<name>ChunJun : Connectors : Inceptor Base</name>

	<dependencies>
		<dependency>
			<groupId>com.dtstack.chunjun</groupId>
			<artifactId>chunjun-connector-jdbc-base</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-cli</groupId>
			<artifactId>commons-cli</artifactId>
			<version>1.3.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.hive</groupId>
			<artifactId>inceptor-streaming</artifactId>
			<version>3.1.3</version>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.parquet</groupId>
			<artifactId>parquet-hadoop</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
			<version>1.8.3</version>
		</dependency>
		<dependency>
			<groupId>io.transwarp</groupId>
			<artifactId>inceptor-driver</artifactId>
			<version>6.0.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.hive</groupId>
			<artifactId>inceptor-serde</artifactId>
			<version>8.0.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.hive</groupId>
			<artifactId>inceptor-exec</artifactId>
			<version>8.0.1</version>
		</dependency>
		<dependency>
			<groupId>io.transwarp.wrdecimal</groupId>
			<artifactId>wrdecimal-library</artifactId>
			<version>0.1</version>
		</dependency>
	</dependencies>


</project>
