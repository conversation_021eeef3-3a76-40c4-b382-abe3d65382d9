<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>chunjun-connectors</artifactId>
		<groupId>com.dtstack.chunjun</groupId>
		<version>2.0-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>chunjun-connector-hive-tbds</artifactId>
	<name>ChunJun : Connectors : Hive TBDS</name>

	<properties>
		<hadoop.tbds.version>2.7.2-TBDS-5.1.3.0</hadoop.tbds.version>
	</properties>


	<dependencies>
		<!-- connector exclude gson -->
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>${gson.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.dtstack.chunjun</groupId>
			<artifactId>chunjun-connector-hdfs-tbds</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>hive-serde</artifactId>
					<groupId>org.apache.hive</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hive-exec</artifactId>
					<groupId>org.apache.hive</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-connector-hive_${scala.binary.version}</artifactId>
			<version>${flink.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>flink-connector-files</artifactId>
					<groupId>org.apache.flink</groupId>
				</exclusion>
				<exclusion>
					<artifactId>force-shading</artifactId>
					<groupId>org.apache.flink</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hive</groupId>
			<artifactId>hive-exec</artifactId>
			<version>2.1.0</version>
			<exclusions>
				<exclusion>
					<artifactId>log4j-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-core</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-slf4j-impl</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<!-- TODO: janino and commons-compiler could be included when submit job using multiprocess. -->
				<exclusion>
					<artifactId>janino</artifactId>
					<groupId>org.codehaus.janino</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-compiler</artifactId>
					<groupId>org.codehaus.janino</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-1.2-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<!--those classes are existed in flink-table-planner-blink -->
				<exclusion>
					<artifactId>calcite-core</artifactId>
					<groupId>org.apache.calcite</groupId>
				</exclusion>
				<exclusion>
					<artifactId>calcite-avatica</artifactId>
					<groupId>org.apache.calcite</groupId>
				</exclusion>
				<exclusion>
					<artifactId>gson</artifactId>
					<groupId>com.google.code.gson</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hive</groupId>
			<artifactId>hive-serde</artifactId>
			<version>2.1.0</version>
			<exclusions>
				<exclusion>
					<artifactId>commons-cli</artifactId>
					<groupId>commons-cli</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-core</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-slf4j-impl</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-web</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-1.2-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hive</groupId>
			<artifactId>hive-jdbc</artifactId>
			<version>2.1.0</version>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-slf4j-impl</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-web</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-core</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-1.2-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>netty-all</artifactId>
					<groupId>io.netty</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hive-common</artifactId>
					<groupId>org.apache.hive</groupId>
				</exclusion>
				<exclusion>
					<artifactId>parquet-hadoop-bundle</artifactId>
					<groupId>org.apache.parquet</groupId>
				</exclusion>
				<exclusion>
					<groupId>xerces</groupId>
					<artifactId>xercesImpl</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-client</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>curator-framework</artifactId>
					<groupId>org.apache.curator</groupId>
				</exclusion>
				<exclusion>
					<artifactId>zookeeper</artifactId>
					<groupId>org.apache.zookeeper</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpcore</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-cli</artifactId>
					<groupId>commons-cli</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-compress</artifactId>
					<groupId>org.apache.commons</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-lang</artifactId>
					<groupId>commons-lang</groupId>
				</exclusion>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
				<exclusion>
					<artifactId>gson</artifactId>
					<groupId>com.google.code.gson</groupId>
				</exclusion>
				<exclusion>
					<artifactId>avro</artifactId>
					<groupId>org.apache.avro</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-common</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-hadoop2-compat</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-server</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>tephra-hbase-compat-1.0</artifactId>
					<groupId>co.cask.tephra</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-hadoop-compat</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hive-metastore</artifactId>
					<groupId>org.apache.hive</groupId>
				</exclusion>
				<!--				<exclusion>-->
				<!--					<artifactId>hive-service</artifactId>-->
				<!--					<groupId>org.apache.hive</groupId>-->
				<!--				</exclusion>-->
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hive</groupId>
			<artifactId>hive-metastore</artifactId>
			<version>1.2.2</version>
			<exclusions>
				<exclusion>
					<artifactId>jsr305</artifactId>
					<groupId>com.google.code.findbugs</groupId>
				</exclusion>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-cli</artifactId>
					<groupId>commons-cli</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-codec</artifactId>
					<groupId>commons-codec</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-collections</artifactId>
					<groupId>commons-collections</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-lang</artifactId>
					<groupId>commons-lang</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-logging</artifactId>
					<groupId>commons-logging</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>avro</artifactId>
					<groupId>org.apache.avro</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-compress</artifactId>
					<groupId>org.apache.commons</groupId>
				</exclusion>
				<exclusion>
					<artifactId>curator-framework</artifactId>
					<groupId>org.apache.curator</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hadoop-annotations</artifactId>
					<groupId>org.apache.hadoop</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hadoop-yarn-api</artifactId>
					<groupId>org.apache.hadoop</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hadoop-yarn-common</artifactId>
					<groupId>org.apache.hadoop</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpcore</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>zookeeper</artifactId>
					<groupId>org.apache.zookeeper</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
			<version>3.2.2</version>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-common</artifactId>
			<version>${hadoop.tbds.version}</version>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-slf4j-impl</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-web</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-core</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-1.2-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>netty-all</artifactId>
					<groupId>io.netty</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hive-common</artifactId>
					<groupId>org.apache.hive</groupId>
				</exclusion>
				<exclusion>
					<artifactId>parquet-hadoop-bundle</artifactId>
					<groupId>org.apache.parquet</groupId>
				</exclusion>
				<exclusion>
					<groupId>xerces</groupId>
					<artifactId>xercesImpl</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-client</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>curator-framework</artifactId>
					<groupId>org.apache.curator</groupId>
				</exclusion>
				<exclusion>
					<artifactId>zookeeper</artifactId>
					<groupId>org.apache.zookeeper</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpcore</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-cli</artifactId>
					<groupId>commons-cli</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-compress</artifactId>
					<groupId>org.apache.commons</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-lang</artifactId>
					<groupId>commons-lang</groupId>
				</exclusion>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
				<exclusion>
					<artifactId>gson</artifactId>
					<groupId>com.google.code.gson</groupId>
				</exclusion>
				<exclusion>
					<artifactId>avro</artifactId>
					<groupId>org.apache.avro</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-common</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-hadoop2-compat</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-server</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>tephra-hbase-compat-1.0</artifactId>
					<groupId>co.cask.tephra</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-hadoop-compat</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hive-metastore</artifactId>
					<groupId>org.apache.hive</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-hdfs</artifactId>
			<version>${hadoop.tbds.version}</version>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-slf4j-impl</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-web</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-core</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-1.2-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>netty-all</artifactId>
					<groupId>io.netty</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hive-common</artifactId>
					<groupId>org.apache.hive</groupId>
				</exclusion>
				<exclusion>
					<artifactId>parquet-hadoop-bundle</artifactId>
					<groupId>org.apache.parquet</groupId>
				</exclusion>
				<exclusion>
					<groupId>xerces</groupId>
					<artifactId>xercesImpl</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-client</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>curator-framework</artifactId>
					<groupId>org.apache.curator</groupId>
				</exclusion>
				<exclusion>
					<artifactId>zookeeper</artifactId>
					<groupId>org.apache.zookeeper</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpcore</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-cli</artifactId>
					<groupId>commons-cli</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-compress</artifactId>
					<groupId>org.apache.commons</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-lang</artifactId>
					<groupId>commons-lang</groupId>
				</exclusion>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
				<exclusion>
					<artifactId>gson</artifactId>
					<groupId>com.google.code.gson</groupId>
				</exclusion>
				<exclusion>
					<artifactId>avro</artifactId>
					<groupId>org.apache.avro</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-common</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-hadoop2-compat</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-server</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>tephra-hbase-compat-1.0</artifactId>
					<groupId>co.cask.tephra</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-hadoop-compat</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hive-metastore</artifactId>
					<groupId>org.apache.hive</groupId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>


	<repositories>
		<repository>
			<id>tbds_public</id>
			<name>tbds_public_repository</name>
			<url>https://tbdsrepo.cloud.tencent.com/repository/tbds/</url>
			<layout>default</layout>
		</repository>
	</repositories>


	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<createDependencyReducedPom>false</createDependencyReducedPom>
							<filters>
								<filter>
									<artifact>*:*</artifact>
									<excludes>
										<exclude>META-INF/*.SF</exclude>
										<exclude>META-INF/*.DSA</exclude>
										<exclude>META-INF/*.RSA</exclude>
									</excludes>
								</filter>
							</filters>
							<relocations>
								<relocation>
									<pattern>com.google.protobuf</pattern>
									<shadedPattern>com.dtstack.chunjun.connector.hive.com.google.protobuf</shadedPattern>
								</relocation>
								<relocation>
									<pattern>org.apache.hive.service.cli</pattern>
									<shadedPattern>shade.hive.org.apache.hive.service.cli</shadedPattern>
								</relocation>
							</relocations>
							<transformers>
								<transformer
									implementation="org.apache.maven.plugins.shade.resource.DontIncludeResourceTransformer">
									<resource>META-INF/services/java.sql.Driver</resource>
								</transformer>
								<transformer
									implementation="org.apache.maven.plugins.shade.resource.IncludeResourceTransformer">
									<resource>META-INF/services</resource>
									<file>java.sql.hive2.Driver</file>
								</transformer>
							</transformers>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-resources</id>
						<!-- here the phase you need -->
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<tasks>
								<copy todir="${basedir}/../../${dist.dir}/connector/tbdshive"
									  file="${basedir}/target/${project.artifactId}-${project.version}.jar"/>
								<!--suppress UnresolvedMavenProperty -->
								<move
									file="${basedir}/../../${dist.dir}/connector/tbdshive/${project.artifactId}-${project.version}.jar"
									tofile="${basedir}/../../${dist.dir}/connector/tbdshive/${project.artifactId}-${git.branch}.jar"/>
								<delete>
									<!--suppress UnresolvedMavenProperty -->
									<fileset dir="${basedir}/../../${dist.dir}/connector/tbdshive/"
											 includes="${project.artifactId}-*.jar"
											 excludes="${project.artifactId}-${git.branch}.jar"/>
								</delete>
							</tasks>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
