/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dtstack.chunjun.connector.binlog.listener;

import com.dtstack.chunjun.cdc.DdlRowData;
import com.dtstack.chunjun.cdc.DdlRowDataBuilder;
import com.dtstack.chunjun.cdc.EventType;
import com.dtstack.chunjun.connector.binlog.converter.JdbcColumnConverter;
import com.dtstack.chunjun.connector.binlog.inputformat.BinlogInputFormat;
import com.dtstack.chunjun.connector.binlog.util.SqlUtil;
import com.dtstack.chunjun.converter.AbstractCDCRowConverter;
import com.dtstack.chunjun.element.ColumnRowData;
import com.dtstack.chunjun.element.ErrorMsgRowData;
import com.dtstack.chunjun.element.column.BigDecimalColumn;
import com.dtstack.chunjun.element.column.NullColumn;
import com.dtstack.chunjun.element.column.StringColumn;
import com.dtstack.chunjun.throwable.WriteRecordException;
import com.dtstack.chunjun.common.util.ClassUtil;
import com.dtstack.chunjun.common.util.RetryUtil;

import org.apache.flink.table.data.RowData;
import org.apache.flink.types.RowKind;

import com.alibaba.otter.canal.common.AbstractCanalLifeCycle;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.alibaba.otter.canal.protocol.position.EntryPosition;
import com.alibaba.otter.canal.sink.exception.CanalSinkException;
import com.google.protobuf.InvalidProtocolBufferException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.dtstack.chunjun.connector.binlog.util.SqlUtil.GET_CURRENT_LSN;
import static com.dtstack.chunjun.constants.CDCConstantValue.DATABASE;
import static com.dtstack.chunjun.constants.CDCConstantValue.LSN;
import static com.dtstack.chunjun.constants.CDCConstantValue.OP_TIME;
import static com.dtstack.chunjun.constants.CDCConstantValue.SCHEMA;
import static com.dtstack.chunjun.constants.CDCConstantValue.TABLE;
import static com.dtstack.chunjun.constants.CDCConstantValue.TS;
import static com.dtstack.chunjun.constants.CDCConstantValue.TYPE;

/** <AUTHOR> */
public class BinlogEventSink extends AbstractCanalLifeCycle
        implements com.alibaba.otter.canal.sink.CanalEventSink<List<CanalEntry.Entry>> {

    private static final Logger LOG = LoggerFactory.getLogger(BinlogEventSink.class);

    private final BinlogInputFormat format;
    private final LinkedBlockingDeque<RowData> queue;
    private final AbstractCDCRowConverter converter;

    private final String OFFSET_LENGTH;

    private Pattern pattern;

    /* 全量同步完后， 消费的lsn位置 */
    private EntryPosition historyEntryPosition;

    /* 断点续传恢复, 如果消费的事件位置小于，忽视该事件 */
    private EntryPosition statePositon;

    public BinlogEventSink(BinlogInputFormat format) {
        this.format = format;
        // 不指定的话 队列无限大，如果上游数据很多
        // 会导致数据缓存在queue里，state更新的信息是最新解析的数据信息，但是实际上queue里积攒了大量的数据还没消费，导致checkpoint恢复丢失数据
        // 改为1 checkpoint恢复是至少一次
        this.queue = new LinkedBlockingDeque<>(1);
        this.converter = format.getSqlConverter();
        this.OFFSET_LENGTH = "%0" + this.format.getBinlogConf().getOffsetLength() + "d";
        if (!format.getBinlogConf().isDdlSkip()
                && StringUtils.isNotBlank(format.getBinlogConf().getFilter())) {
            pattern = Pattern.compile(format.getBinlogConf().getFilter());
        }
    }

    public void initialTableStructData(List<String> pattern) {

        if (CollectionUtils.isNotEmpty(pattern)) {

            Set<String> schemas = new HashSet<>();

            pattern.forEach(
                    i -> {
                        String[] split = i.split("\\.");
                        if (split.length == 2) {
                            schemas.add(split[0]);
                        }
                    });

            List<Pattern> patterns =
                    pattern.stream().map(Pattern::compile).collect(Collectors.toList());

            Connection connection = getConnection();

            ArrayList<Pair<String, String>> tables = new ArrayList<>();
            schemas.forEach(
                    i -> {
                        try (final ResultSet rs =
                                connection
                                        .getMetaData()
                                        .getTables(i, null, null, new String[] {"TABLE"})) {
                            while (rs.next()) {
                                final String catalogName = rs.getString(1);
                                final String tableName = rs.getString(3);
                                if (patterns.stream()
                                        .anyMatch(
                                                f ->
                                                        f.matcher(catalogName + "." + tableName)
                                                                .matches())) {
                                    tables.add(Pair.of(catalogName, tableName));
                                }
                            }
                        } catch (SQLException e) {
                            throw new RuntimeException(e);
                        }
                    });

            tables.forEach(
                    i -> {
                        try {
                            PreparedStatement preparedStatement =
                                    connection.prepareStatement(
                                            String.format(
                                                    "show create table %s.%s",
                                                    i.getLeft(), i.getRight()));
                            ResultSet resultSet = preparedStatement.executeQuery();
                            resultSet.next();
                            String ddl = resultSet.getString(2);
                            DdlRowData ddlData =
                                    DdlRowDataBuilder.builder()
                                            .setDatabaseName(null)
                                            .setSchemaName(i.getLeft())
                                            .setTableName(i.getRight())
                                            .setContent(ddl)
                                            .setType(EventType.CREATE_TABLE.name())
                                            .setLsn("-1")
                                            .setLsnSequence("0")
                                            .setSnapShot(true)
                                            .build();
                            queue.put(ddlData);

                        } catch (SQLException | InterruptedException e) {
                            throw new RuntimeException(e);
                        }
                    });
            if (CollectionUtils.isNotEmpty(tables)) {
                LOG.info(
                        "snapshot table struct {}",
                        tables.stream()
                                .map(i -> "[" + i.getLeft() + "." + i.getRight() + "]")
                                .collect(Collectors.joining(",")));
            }
        }
    }

    @Override
    public boolean sink(
            List<CanalEntry.Entry> entries, InetSocketAddress inetSocketAddress, String s)
            throws CanalSinkException {
        for (CanalEntry.Entry entry : entries) {
            /* 断点续传恢复, 如果消费的事件位置小于state里面的位置，忽视该事件 */
            if (statePositon != null) {
                if (statePositon.getPosition() != null
                        && entry.getHeader().getLogfileOffset() <= statePositon.getPosition()) {
                    continue;
                }
            }

            CanalEntry.EntryType entryType = entry.getEntryType();
            if (entryType != CanalEntry.EntryType.ROWDATA) {
                continue;
            }
            CanalEntry.RowChange rowChange = null;
            try {
                rowChange = CanalEntry.RowChange.parseFrom(entry.getStoreValue());
            } catch (InvalidProtocolBufferException e) {
                LOG.error("parser data[{}] error:{}", entry, ExceptionUtil.getErrorMessage(e));
            }

            if (rowChange == null) {
                return false;
            }

            CanalEntry.Header header = entry.getHeader();
            String schema = header.getSchemaName();
            String table = header.getTableName();
            long executeTime = header.getExecuteTime();
            String lsn = buildLastPosition(entry);
            try {
                processRowChange(rowChange, schema, table, executeTime, lsn);
            } catch (WriteRecordException e) {
                // todo 脏数据记录
                if (LOG.isDebugEnabled()) {
                    LOG.debug(
                            "write error rowData, rowData = {}, e = {}",
                            e.getRowData().toString(),
                            ExceptionUtil.getErrorMessage(e));
                }
            }
        }
        return true;
    }

    /**
     * 处理RowData数据
     *
     * @param rowChange 解析后的RowData数据
     * @param schema schema
     * @param table table
     * @param executeTime 变更数据的执行时间
     */
    @SuppressWarnings("unchecked")
    private void processRowChange(
            CanalEntry.RowChange rowChange,
            String schema,
            String table,
            long executeTime,
            String lsn)
            throws WriteRecordException {
        String eventType = rowChange.getEventType().toString();
        List<String> categories = format.getCategories();
        if (CollectionUtils.isNotEmpty(categories) && !categories.contains(eventType)) {
            return;
        }
        BinlogEventRow binlogEventRow =
                new BinlogEventRow(rowChange, schema, table, executeTime, lsn);
        // cancel对于某些ddl语句没有按照正则过滤掉 所以需要手动再过滤一次
        if ((rowChange.getIsDdl() || rowChange.getEventType().equals(CanalEntry.EventType.QUERY))
                && null != pattern) {
            StringBuilder stringBuilder = new StringBuilder();
            if (StringUtils.isNotBlank(schema)) {
                stringBuilder.append(schema).append(".");
            }

            if (StringUtils.isNotBlank(table)) {
                stringBuilder.append(table);
            }
            if (!pattern.matcher(stringBuilder.toString()).matches()) {
                return;
            }
        }
        LinkedList<RowData> rowDatalist;
        try {
            rowDatalist = converter.doToInternal(binlogEventRow);
        } catch (Exception e) {
            throw new WriteRecordException("", e, 0, binlogEventRow);
        }
        RowData rowData = null;
        try {
            while (rowDatalist != null && (rowData = rowDatalist.poll()) != null) {
                queue.put(rowData);
            }
        } catch (InterruptedException e) {
            LOG.error(
                    "put rowData[{}] into queue interrupted error:{}",
                    rowData,
                    ExceptionUtil.getErrorMessage(e));
        }
    }

    /**
     * 从队列中获取RowData数据，对于异常情况需要把异常抛出并停止任务
     *
     * @return
     */
    public RowData takeRowDataFromQueue() {
        RowData rowData = null;
        try {
            // 最多阻塞100ms
            rowData = queue.poll(100, TimeUnit.MILLISECONDS);
            if (rowData instanceof ErrorMsgRowData) {
                throw new RuntimeException(rowData.toString());
            }
        } catch (InterruptedException e) {
            LOG.error(
                    "takeRowDataFromQueue interrupted error:{}", ExceptionUtil.getErrorMessage(e));
        }
        return rowData;
    }

    /**
     * 处理异常数据
     *
     * @param rowData
     */
    public void processErrorMsgRowData(ErrorMsgRowData rowData) {
        try {
            queue.put(rowData);
        } catch (InterruptedException e) {
            LOG.error(
                    "processErrorMsgRowData interrupted rowData:{} error:{}",
                    rowData,
                    ExceptionUtil.getErrorMessage(e));
        }
    }

    protected String buildLastPosition(CanalEntry.Entry entry) {
        String pos = String.format(OFFSET_LENGTH, entry.getHeader().getLogfileOffset());
        return entry.getHeader().getLogfileName() + "/" + pos;
    }

    @Override
    public void interrupt() {
        LOG.warn("BinlogEventSink is interrupted");
    }

    private Connection getConnection() {
        String jdbcUrl = format.getBinlogConf().getJdbcUrl();
        String username = format.getBinlogConf().getUsername();
        String password = format.getBinlogConf().getPassword();
        Properties prop = new Properties();
        if (StringUtils.isNotBlank(username)) {
            prop.put("user", username);
        }
        if (StringUtils.isNotBlank(password)) {
            prop.put("password", password);
        }

        Connection connection;
        synchronized (ClassUtil.LOCK_STR) {
            connection =
                    RetryUtil.executeWithRetry(
                            () -> DriverManager.getConnection(jdbcUrl, prop), 3, 2000, false);
        }
        return connection;
    }

    /**
     * 对存量数据进行处理 获取一个table-level锁 开启一个Session REPEATABLE READ事务 获取当前表结构，并将create table ddl发到下游
     * 根据当前的执行时间，创建EntryPosition, 留给canel设置同步起点 释放锁 扫描表数据， 将数据转换成column row data发到下游 提交事物
     */
    public void processSingleTableHistoryData() {
        // only support single table
        List<String> tables = format.getBinlogConf().getTable();
        if (tables.size() != 1) {
            throw new FlinkxRuntimeException("only support one table");
        }

        try (Connection connection = getConnection();
                /* 流式读取 */
                Statement stmt =
                        connection.createStatement(
                                java.sql.ResultSet.TYPE_FORWARD_ONLY,
                                java.sql.ResultSet.CONCUR_READ_ONLY)) {

            stmt.setFetchSize(Integer.MIN_VALUE);

            String tbnWithSchema = tables.get(0);
            String schema = connection.getCatalog();
            String tbn = tbnWithSchema;

            /* support schema.table */
            if (tbnWithSchema.contains(".")) {
                schema = tbnWithSchema.substring(0, tbnWithSchema.indexOf('.'));
                tbn = tbnWithSchema.substring(tbnWithSchema.indexOf('.') + 1);
            }

            /* 获取一个table-level的锁 */
            stmt.execute(SqlUtil.buildLockTableSql(schema, tbn));

            /* 获取当前lsn */
            setCurrentEntryPosition(stmt);

            /* 开启一个Session REPEATABLE READ事务 */
            stmt.execute(SqlUtil.SET_REPEATABLE_READ_TRANSACTION);
            stmt.execute(SqlUtil.START_TRANSACTION);

            /* 获取当前表结构，并将create table ddl发到下游 */
            ResultSet resultSet = stmt.executeQuery(SqlUtil.buildShowCreateTable(schema, tbn));
            resultSet.next();
            String ddl = resultSet.getString(2);
            DdlRowData ddlData =
                    DdlRowDataBuilder.builder()
                            .setDatabaseName(null)
                            .setSchemaName(schema)
                            .setTableName(tbn)
                            .setContent(ddl)
                            .setType(EventType.CREATE_TABLE.name())
                            .setLsn("-1")
                            .setLsnSequence("0")
                            .setSnapShot(true)
                            .build();
            queue.put(ddlData);

            resultSet = stmt.executeQuery(SqlUtil.buildGetColumnInfoSql(schema, tbn));
            List<ColumnInfo> columnInfos = new ArrayList<>();

            while (resultSet.next()) {
                String field = resultSet.getString("Field");
                String type = resultSet.getString("Type");
                String isNullable = resultSet.getString("Null");
                String defaultValue = resultSet.getString("Default");

                ColumnInfo columnInfo =
                        new ColumnInfo(field, type, isNullable.equals("YES"), defaultValue);
                columnInfos.add(columnInfo);
            }

            /* 释放锁 */
            stmt.execute(SqlUtil.RELEASE_TABLE_LOCK);

            /* 扫描历史数据 */
            String scanSql =
                    SqlUtil.buildScanHistorySql(
                            schema, tbn, format.getBinlogConf().getFetchFilterSql());
            scanHistoryData(stmt, tbn, schema, scanSql, columnInfos);

            /* 提交事务 */
            stmt.execute(SqlUtil.COMMIT_TRANSACTION);
        } catch (Exception e) {
            throw new FlinkxRuntimeException(e);
        }
    }

    public void deleteTargetTable() {
        List<String> tables = format.getBinlogConf().getTable();
        if (tables.size() != 1) {
            throw new FlinkxRuntimeException("only support one table");
        }

        try (Connection connection = getConnection()) {
            String tbnWithSchema = tables.get(0);
            String schema = connection.getCatalog();
            String tbn = tbnWithSchema;

            /* support schema.table */
            if (tbnWithSchema.contains(".")) {
                schema = tbnWithSchema.substring(0, tbnWithSchema.indexOf('.'));
                tbn = tbnWithSchema.substring(tbnWithSchema.indexOf('.') + 1);
            }

            String dropTableDDl = SqlUtil.buildDeleteTargetTable(schema, tbn);
            DdlRowData ddlData =
                    DdlRowDataBuilder.builder()
                            .setDatabaseName(null)
                            .setSchemaName(schema)
                            .setTableName(tbn)
                            .setContent(dropTableDDl)
                            .setType(EventType.DROP_TABLE.name())
                            .setLsn("-1")
                            .setLsnSequence("0")
                            .setSnapShot(true)
                            .build();
            queue.put(ddlData);
        } catch (Exception e) {
            throw new FlinkxRuntimeException(e);
        }
    }

    private void scanHistoryData(
            Statement stmt,
            String tbn,
            String schema,
            String querySql,
            List<ColumnInfo> columnInfos) {
        try (ResultSet rs = stmt.executeQuery(querySql)) {
            while (rs.next()) {
                // 7: database, schema, table, ts, opTime，type, ls
                ColumnRowData columnRowData =
                        new ColumnRowData(
                                RowKind.INSERT, 7 + columnInfos.size() + columnInfos.size());
                columnRowData.addField(new NullColumn());
                columnRowData.addHeader(DATABASE);
                columnRowData.addExtHeader(DATABASE);
                columnRowData.addField(new StringColumn(schema));
                columnRowData.addHeader(SCHEMA);
                columnRowData.addExtHeader(SCHEMA);
                columnRowData.addField(new StringColumn(tbn));
                columnRowData.addHeader(TABLE);
                columnRowData.addExtHeader(TABLE);
                columnRowData.addField(new BigDecimalColumn(0));
                columnRowData.addHeader(TS);
                columnRowData.addExtHeader(TS);

                // history data, lsn set -1
                columnRowData.addField(new StringColumn("-1"));
                columnRowData.addHeader(LSN);
                columnRowData.addExtHeader(LSN);

                columnRowData.addField(new StringColumn(""));
                columnRowData.addHeader(OP_TIME);
                columnRowData.addExtHeader(OP_TIME);

                columnRowData.addField(new StringColumn("INSERT"));
                columnRowData.addHeader(TYPE);
                columnRowData.addExtHeader(TYPE);

                for (ColumnInfo columnInfo : columnInfos) {
                    Object value = rs.getObject(columnInfo.getName());
                    columnRowData.addHeader(columnInfo.getName());
                    columnRowData.addField(
                            JdbcColumnConverter.convertJdbcType(columnInfo.getType(), value));
                }
                queue.add(columnRowData);
            }
        } catch (Exception e) {
            throw new FlinkxRuntimeException(e);
        }
    }

    private void setCurrentEntryPosition(Statement stmt) {
        try (ResultSet rs = stmt.executeQuery(GET_CURRENT_LSN)) {
            historyEntryPosition = new EntryPosition();
            while (rs.next()) {
                String journal = rs.getString("File");
                String position = rs.getString("Position");
                historyEntryPosition.setJournalName(journal);
                historyEntryPosition.setPosition(Long.parseLong(position));
            }
        } catch (Exception e) {
            throw new FlinkxRuntimeException(e);
        }
    }

    public EntryPosition getHistoryEntryPosition() {
        return historyEntryPosition;
    }

    public void setStatePosition(EntryPosition statePositon) {
        this.statePositon = statePositon;
    }
}
