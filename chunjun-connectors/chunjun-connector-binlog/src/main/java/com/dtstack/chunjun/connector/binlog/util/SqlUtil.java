package com.dtstack.chunjun.connector.binlog.util;

public class SqlUtil {

    public static String SET_REPEATABLE_READ_TRANSACTION =
            "set session transaction isolation level REPEATABLE READ";

    public static String START_TRANSACTION = "START TRANSACTION";

    public static String COMMIT_TRANSACTION = "commit";

    public static String RELEASE_TABLE_LOCK = "unlock tables";

    public static final String GET_CURRENT_LSN = "show master status ;";

    public static String buildLockTableSql(String schema, String tbn) {
        return String.format("lock tables %s.%s read", schema, tbn);
    }

    public static String buildShowCreateTable(String schema, String tbn) {
        return String.format("show create table %s.%s", schema, tbn);
    }

    public static String buildDeleteTargetTable(String schema, String tbn) {
        return String.format("drop table %s.%s", schema, tbn);
    }

    public static String buildGetColumnInfoSql(String schema, String tbn) {
        return String.format("show fields from %s.%s", schema, tbn);
    }

    public static String buildScanHistorySql(String schema, String tbn, String filterSql) {
        StringBuilder sb = new StringBuilder();
        sb.append("select * from ");
        sb.append(schema);
        sb.append(".");
        sb.append(tbn);

        if (filterSql != null) {
            sb.append(" where ");
            sb.append(filterSql);
        }
        return sb.toString();
    }
}
