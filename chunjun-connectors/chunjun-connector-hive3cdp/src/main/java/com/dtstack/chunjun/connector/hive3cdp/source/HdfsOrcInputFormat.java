/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.hive3cdp.source;

import com.dtstack.chunjun.conf.FieldConf;
import com.dtstack.chunjun.connector.hive3cdp.inputSplit.HdfsOrcInputSplit;
import com.dtstack.chunjun.connector.hive3cdp.util.ConverterUtil;
import com.dtstack.chunjun.constants.ConstantValue;
import com.dtstack.chunjun.throwable.ReadRecordException;

import org.apache.flink.core.io.InputSplit;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.RowData;

import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.hive.ql.io.orc.OrcFile;
import org.apache.hadoop.hive.ql.io.orc.OrcInputFormat;
import org.apache.hadoop.hive.ql.io.orc.OrcSerde;
import org.apache.hadoop.hive.ql.io.orc.OrcSplit;
import org.apache.hadoop.hive.ql.io.orc.OrcStruct;
import org.apache.hadoop.hive.serde2.objectinspector.StructField;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.mapred.InputFormat;
import org.apache.hadoop.mapred.Reporter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicBoolean;

/** <AUTHOR> 2022/3/23 */
public class HdfsOrcInputFormat extends BaseHdfsInputFormat {
    protected transient FileSystem fs;

    protected transient String[] fullColNames;

    protected transient StructObjectInspector inspector;

    protected transient List<? extends StructField> fields;

    protected static final String COMPLEX_FIELD_TYPE_SYMBOL_REGEX = ".*(<|>|\\{|}|[|]).*";

    private final AtomicBoolean isInit = new AtomicBoolean(false);

    @Override
    protected InputSplit[] createHdfsSplit(int minNumSplits) throws IOException {
        initHadoopJobConf();
        // 非事务表创建分片
        org.apache.hadoop.mapred.FileInputFormat.setInputPaths(hadoopJobConf, hdfsConf.getPath());
        org.apache.hadoop.mapred.FileInputFormat.setInputPathFilter(
                hadoopJobConf, HdfsPathFilter.class);

        OrcInputFormat orcInputFormat = new OrcInputFormat();
        org.apache.hadoop.mapred.InputSplit[] splits =
                orcInputFormat.getSplits(hadoopJobConf, minNumSplits);

        if (splits != null) {
            List<HdfsOrcInputSplit> list = new ArrayList<>(splits.length);
            int i = 0;
            for (org.apache.hadoop.mapred.InputSplit split : splits) {
                OrcSplit orcSplit = (OrcSplit) split;
                if (orcSplit.getLength() > 49) {
                    list.add(new HdfsOrcInputSplit(orcSplit, i));
                    i++;
                }
            }
            return list.toArray(new HdfsOrcInputSplit[i]);
        }
        return null;
    }

    @Override
    public InputFormat<NullWritable, OrcStruct> createMapredInputFormat() {
        return new OrcInputFormat();
    }

    @Override
    protected void openInternal(InputSplit inputSplit) throws IOException {
        securityContext.execute(
                null,
                t -> {
                    try {
                        orcOpenInternal(inputSplit);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    return null;
                });
    }

    protected void orcOpenInternal(InputSplit inputSplit) throws IOException {
        OrcSplit orcSplit = ((HdfsOrcInputSplit) inputSplit).getOrcSplit();
        fs = FileSystem.get(hadoopJobConf);
        init(orcSplit);
        openOrcReader(inputSplit);
    }

    @Override
    protected RowData nextRecordInternal(RowData rowData) throws ReadRecordException {
        try {
            GenericRowData genericRowData =
                    securityContext.execute(
                            null,
                            t -> {
                                List<FieldConf> fieldConfList = hdfsConf.getColumn();
                                GenericRowData finalGenericRowData =
                                        new GenericRowData(
                                                Math.max(
                                                        fieldConfList.size(), fullColNames.length));
                                if (fieldConfList.size() == 1
                                        && ConstantValue.STAR_SYMBOL.equals(
                                                fieldConfList.get(0).getName())) {

                                    for (int i = 0; i < fullColNames.length; i++) {
                                        Object col =
                                                inspector.getStructFieldData(value, fields.get(i));
                                        if (col != null) {
                                            col = ConverterUtil.getWritableValue(col);
                                        }
                                        finalGenericRowData.setField(i, col);
                                    }
                                } else {
                                    for (int i = 0; i < fieldConfList.size(); i++) {
                                        FieldConf fieldConf = fieldConfList.get(i);
                                        Object val = null;
                                        if (fieldConf.getValue() != null) {
                                            val = fieldConf.getValue();
                                        } else if (fieldConf.getIndex() != -1) {
                                            val =
                                                    inspector.getStructFieldData(
                                                            value,
                                                            fields.get(fieldConf.getIndex()));
                                        }

                                        if (val != null) {
                                            val = ConverterUtil.getWritableValue(val);
                                        }

                                        finalGenericRowData.setField(i, val);
                                    }
                                }
                                return finalGenericRowData;
                            });
            return converter.toInternal(genericRowData);
        } catch (Exception e) {
            throw new ReadRecordException("", e, 0, rowData);
        }
    }

    protected List<String> parseColumnAndType(String typeStruct) {
        List<String> cols = new ArrayList<>();
        List<String> splits = Arrays.asList(typeStruct.split(","));
        Iterator<String> it = splits.iterator();
        while (it.hasNext()) {
            StringBuilder current = new StringBuilder(it.next());
            if (!current.toString().contains("(") && !current.toString().contains(")")) {
                cols.add(current.toString());
                continue;
            }

            if (current.toString().contains("(") && current.toString().contains(")")) {
                cols.add(current.toString());
                continue;
            }

            if (current.toString().contains("(") && !current.toString().contains(")")) {
                while (it.hasNext()) {
                    String next = it.next();
                    current.append(",").append(next);
                    if (next.contains(")")) {
                        break;
                    }
                }

                cols.add(current.toString());
            }
        }

        return cols;
    }

    public void init(OrcSplit orcSplit) throws IOException {
        try {
            if (!isInit.get()) {
                init(orcSplit.getPath());
                isInit.set(true);
            }
        } catch (Exception e) {
            throw new IOException("init [inspector] error", e);
        }
    }

    private void openOrcReader(InputSplit inputSplit) throws IOException {
        numReadCounter = getRuntimeContext().getLongCounter("numRead");
        HdfsOrcInputSplit hdfsOrcInputSplit = (HdfsOrcInputSplit) inputSplit;
        OrcSplit orcSplit = hdfsOrcInputSplit.getOrcSplit();
        findCurrentPartition(orcSplit.getPath());
        recordReader = inputFormat.getRecordReader(orcSplit, hadoopJobConf, Reporter.NULL);
        key = recordReader.createKey();
        value = recordReader.createValue();
        fields = inspector.getAllStructFieldRefs();
    }

    private void init(Path path) throws Exception {
        OrcFile.ReaderOptions readerOptions = OrcFile.readerOptions(hadoopJobConf);
        readerOptions.filesystem(fs);

        org.apache.hadoop.hive.ql.io.orc.Reader reader = OrcFile.createReader(path, readerOptions);

        StructObjectInspector structObjectInspector =
                (StructObjectInspector) reader.getObjectInspector();
        List<StructField> columnList =
                (List<StructField>) structObjectInspector.getAllStructFieldRefs();

        fullColNames = new String[columnList.size()];
        String[] fullColTypes = new String[columnList.size()];

        for (int i = 0; i < columnList.size(); ++i) {
            fullColNames[i] = columnList.get(i).getFieldName();
            fullColTypes[i] = columnList.get(i).getFieldObjectInspector().getTypeName();
        }

        Properties p = new Properties();
        p.setProperty("columns", StringUtils.join(fullColNames, ConstantValue.COMMA_SYMBOL));
        p.setProperty("columns.types", StringUtils.join(fullColTypes, ConstantValue.COLON_SYMBOL));

        OrcSerde orcSerde = new OrcSerde();
        orcSerde.initialize(hadoopJobConf, p);

        this.inspector = (StructObjectInspector) orcSerde.getObjectInspector();
    }
}
