<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>chunjun-connectors</artifactId>
		<groupId>com.dtstack.chunjun</groupId>
		<version>2.0-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>chunjun-connector-starrocks</artifactId>
	<name>ChunJun : Connectors : StarRocks</name>

	<properties>
		<maven.compiler.source>${target.java.version}</maven.compiler.source>
		<maven.compiler.target>${target.java.version}</maven.compiler.target>
		<arrow.version>5.0.0</arrow.version>
	</properties>

	<dependencies>

		<dependency>
			<groupId>com.starrocks</groupId>
			<artifactId>flink-connector-starrocks</artifactId>
			<version>1.2.10_flink-2.0-SNAPSHOT-dt-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>5.1.49</version>
		</dependency>

		<!--		<dependency>-->
		<!--			<groupId>io.vertx</groupId>-->
		<!--			<artifactId>vertx-core</artifactId>-->
		<!--			<version>3.4.2</version>-->
		<!--		</dependency>-->

		<!--		<dependency>-->
		<!--			<groupId>io.vertx</groupId>-->
		<!--			<artifactId>vertx-web-client</artifactId>-->
		<!--			<version>3.4.2</version>-->
		<!--		</dependency>-->

		<dependency>
			<groupId>com.starrocks</groupId>
			<artifactId>starrocks-thrift-sdk</artifactId>
			<version>1.0.1</version>
		</dependency>

		<dependency>
			<groupId>org.apache.arrow</groupId>
			<artifactId>arrow-vector</artifactId>
			<version>${arrow.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.arrow</groupId>
			<artifactId>arrow-memory-netty</artifactId>
			<version>${arrow.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>${http.version}</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<createDependencyReducedPom>false</createDependencyReducedPom>
							<relocations>
								<relocation>
									<pattern>io.netty</pattern>
									<shadedPattern>com.dtstack.chunjun.connector.starrocks.shaded.io.netty</shadedPattern>
								</relocation>
								<relocation>
									<pattern>org.apache.arrow</pattern>
									<shadedPattern>com.dtstack.chunjun.connector.starrocks.shaded.org.apache.arrow</shadedPattern>
								</relocation>
								<relocation>
									<pattern>com.google.flatbuffers</pattern>
									<shadedPattern>com.dtstack.chunjun.connector.starrocks.shaded.com.google.flatbuffers</shadedPattern>
								</relocation>
								<relocation>
									<pattern>com.fasterxml.jackson.core</pattern>
									<shadedPattern>com.dtstack.chunjun.connector.starrocks.shaded.com.fasterxml.jackson.core</shadedPattern>
								</relocation>
								<relocation>
									<pattern>com.fasterxml.jackson.databind</pattern>
									<shadedPattern>com.dtstack.chunjun.connector.starrocks.shaded.com.fasterxml.jackson.databind</shadedPattern>
								</relocation>
								<relocation>
									<pattern>com.fasterxml.jackson.annotation</pattern>
									<shadedPattern>com.dtstack.chunjun.connector.starrocks.shaded.com.fasterxml.jackson.annotation</shadedPattern>
								</relocation>
							</relocations>
							<filters>
								<filter>
									<artifact>com.starrocks:flink-connector-starrocks</artifact>
									<excludes>
										<exclude>META-INF/services/org.apache.flink.table.factories.Factory</exclude>
									</excludes>
								</filter>
							</filters>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-resources</id>
						<!-- here the phase you need -->
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<tasks>
								<copy todir="${basedir}/../../${dist.dir}/connector/starrocks"
									  file="${basedir}/target/${project.artifactId}-${project.version}.jar"/>
								<move file="${basedir}/../../${dist.dir}/connector/starrocks/${project.artifactId}-${project.version}.jar"
									  tofile="${basedir}/../../${dist.dir}/connector/starrocks/${project.artifactId}-${git.branch}.jar"/>
								<delete>
									<fileset dir="${basedir}/../../${dist.dir}/connector/starrocks/"
											 includes="${project.artifactId}-*.jar"
											 excludes="${project.artifactId}-${git.branch}.jar"/>
								</delete>
							</tasks>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>com.diffplug.spotless</groupId>
				<artifactId>spotless-maven-plugin</artifactId>
			</plugin>
		</plugins>

	</build>

</project>
