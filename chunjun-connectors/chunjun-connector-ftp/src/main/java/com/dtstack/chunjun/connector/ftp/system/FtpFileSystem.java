package com.dtstack.chunjun.connector.ftp.system;

import com.dtstack.chunjun.connector.ftp.system.writer.FtpRecoverableWriter;

import org.apache.flink.core.fs.RecoverableWriter;
import org.apache.flink.runtime.fs.hdfs.HadoopFileSystem;

import org.apache.hadoop.fs.FileSystem;

import java.io.IOException;

public class FtpFileSystem extends HadoopFileSystem {
    private final FileSystem fs;
    /**
     * Wraps the given Hadoop File System object as a Flink File System object. The given Hadoop
     * file system object is expected to be initialized already.
     *
     * @param hadoopFileSystem The Hadoop FileSystem that will be used under the hood.
     */
    public FtpFileSystem(FileSystem hadoopFileSystem) {
        super(hadoopFileSystem);
        fs = hadoopFileSystem;
    }

    @Override
    public RecoverableWriter createRecoverableWriter() throws IOException {
        return new FtpRecoverableWriter(fs);
    }
}
