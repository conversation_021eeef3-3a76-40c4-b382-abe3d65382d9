/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.argo.source;

import com.dtstack.chunjun.connector.argo.config.ArgoConfig;
import com.dtstack.chunjun.connector.argo.lookup.ArgoAllLookupFunction;
import com.dtstack.chunjun.connector.argo.lookup.ArgoLruLookupFunction;
import com.dtstack.chunjun.connector.jdbc.dialect.JdbcDialect;
import com.dtstack.chunjun.connector.jdbc.source.JdbcDynamicTableSource;
import com.dtstack.chunjun.connector.jdbc.source.JdbcInputFormatBuilder;
import com.dtstack.chunjun.connector.jdbc.util.increment.IncrementKeyUtil;
import com.dtstack.chunjun.enums.CacheType;
import com.dtstack.chunjun.lookup.conf.LookupConf;
import com.dtstack.chunjun.lookup.util.LookupUtil;
import com.dtstack.chunjun.table.connector.source.ParallelAsyncLookupFunctionProvider;
import com.dtstack.chunjun.table.connector.source.ParallelLookupFunctionProvider;

import org.apache.flink.table.catalog.ResolvedSchema;
import org.apache.flink.table.types.logical.RowType;

import org.apache.commons.lang3.tuple.Pair;

import java.math.BigInteger;
import java.util.function.Function;

public class ArgoDynamicTableSource extends JdbcDynamicTableSource {

    private final ArgoConfig argoConfig;

    public ArgoDynamicTableSource(
            ArgoConfig argoConfig,
            LookupConf lookupConf,
            ResolvedSchema physicalSchema,
            JdbcDialect jdbcDialect,
            JdbcInputFormatBuilder builder,
            Function<String, IncrementKeyUtil<?, BigInteger>> initIncrementUtilFunc) {
        super(argoConfig, lookupConf, physicalSchema, jdbcDialect, builder, initIncrementUtilFunc);
        this.argoConfig = argoConfig;
    }

    @Override
    public LookupRuntimeProvider getLookupRuntimeProvider(LookupContext context) {
        Pair<String[], int[]> keyNamesAndIndexes =
                LookupUtil.getKeyNamesAndIndexes(context.getKeys(), this.physicalRowDataType);
        // 通过该参数得到类型转换器，将数据库中的字段转成对应的类型
        final RowType rowType = (RowType) this.physicalRowDataType.getLogicalType();

        if (lookupConf.getCache().equalsIgnoreCase(CacheType.ALL.toString())) {
            return ParallelLookupFunctionProvider.of(
                    new ArgoAllLookupFunction(
                            argoConfig,
                            jdbcDialect,
                            lookupConf,
                            rowType.getFieldNames().toArray(new String[0]),
                            keyNamesAndIndexes.getKey(),
                            keyNamesAndIndexes.getValue(),
                            rowType),
                    lookupConf.getParallelism());
        }
        return ParallelAsyncLookupFunctionProvider.of(
                new ArgoLruLookupFunction(
                        argoConfig,
                        jdbcDialect,
                        lookupConf,
                        rowType.getFieldNames().toArray(new String[0]),
                        keyNamesAndIndexes.getKey(),
                        keyNamesAndIndexes.getValue(),
                        rowType),
                lookupConf.getParallelism());
    }
}
