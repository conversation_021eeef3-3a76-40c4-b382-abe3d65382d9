/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.argo.table;

import com.dtstack.chunjun.connector.argo.config.ArgoConfig;
import com.dtstack.chunjun.connector.argo.config.ArgoOptions;
import com.dtstack.chunjun.connector.argo.dialect.ArgoDefaultDialect;
import com.dtstack.chunjun.connector.argo.dialect.ArgoDialect;
import com.dtstack.chunjun.connector.argo.sink.ArgoDynamicTableSink;
import com.dtstack.chunjun.connector.argo.sink.ArgoOutputFormat;
import com.dtstack.chunjun.connector.argo.sink.ArgoOutputFormatBuilder;
import com.dtstack.chunjun.connector.argo.source.ArgoDynamicTableSource;
import com.dtstack.chunjun.connector.argo.source.ArgoInputFormat;
import com.dtstack.chunjun.connector.argo.source.ArgoInputFormatBuilder;
import com.dtstack.chunjun.connector.argo.util.ArgoDbUtil;
import com.dtstack.chunjun.connector.argo.util.increment.ArgoDateTypeUtil;
import com.dtstack.chunjun.connector.jdbc.conf.SinkConnectionConf;
import com.dtstack.chunjun.connector.jdbc.conf.SourceConnectionConf;
import com.dtstack.chunjun.connector.jdbc.dialect.JdbcDialect;
import com.dtstack.chunjun.connector.jdbc.options.JdbcLookupOptions;
import com.dtstack.chunjun.connector.jdbc.table.JdbcDynamicTableFactory;
import com.dtstack.chunjun.connector.jdbc.util.increment.IncrementKeyUtil;
import com.dtstack.chunjun.connector.jdbc.util.increment.NumericTypeUtil;
import com.dtstack.chunjun.connector.jdbc.util.increment.TimestampTypeUtil;
import com.dtstack.chunjun.enums.ColumnType;

import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.configuration.ReadableConfig;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.api.constraints.UniqueConstraint;
import org.apache.flink.table.catalog.ResolvedSchema;
import org.apache.flink.table.connector.sink.DynamicTableSink;
import org.apache.flink.table.connector.source.DynamicTableSource;
import org.apache.flink.table.factories.FactoryUtil;
import org.apache.flink.table.utils.TableSchemaUtils;

import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.dtstack.chunjun.connector.jdbc.options.JdbcCommonOptions.PASSWORD;
import static com.dtstack.chunjun.connector.jdbc.options.JdbcCommonOptions.SCHEMA_NAME;
import static com.dtstack.chunjun.connector.jdbc.options.JdbcCommonOptions.TABLE_NAME;
import static com.dtstack.chunjun.connector.jdbc.options.JdbcCommonOptions.URL;
import static com.dtstack.chunjun.connector.jdbc.options.JdbcCommonOptions.USERNAME;
import static com.dtstack.chunjun.connector.jdbc.options.JdbcLookupOptions.DRUID_PREFIX;
import static com.dtstack.chunjun.connector.jdbc.options.JdbcLookupOptions.VERTX_PREFIX;
import static com.dtstack.chunjun.connector.jdbc.options.JdbcSinkOptions.SINK_ALL_REPLACE;
import static com.dtstack.chunjun.connector.jdbc.options.JdbcSinkOptions.SINK_SEMANTIC;
import static com.dtstack.chunjun.source.options.SourceOptions.SCAN_DEFAULT_FETCH_SIZE;
import static com.dtstack.chunjun.source.options.SourceOptions.SCAN_FETCH_SIZE;
import static com.dtstack.chunjun.source.options.SourceOptions.SCAN_INCREMENT_COLUMN;
import static com.dtstack.chunjun.source.options.SourceOptions.SCAN_INCREMENT_COLUMN_TYPE;
import static com.dtstack.chunjun.source.options.SourceOptions.SCAN_PARALLELISM;
import static com.dtstack.chunjun.source.options.SourceOptions.SCAN_PARTITION_COLUMN;
import static com.dtstack.chunjun.source.options.SourceOptions.SCAN_PARTITION_STRATEGY;
import static com.dtstack.chunjun.source.options.SourceOptions.SCAN_POLLING_INTERVAL;
import static com.dtstack.chunjun.source.options.SourceOptions.SCAN_QUERY_TIMEOUT;
import static com.dtstack.chunjun.source.options.SourceOptions.SCAN_RESTORE_COLUMNNAME;
import static com.dtstack.chunjun.source.options.SourceOptions.SCAN_RESTORE_COLUMNTYPE;
import static com.dtstack.chunjun.source.options.SourceOptions.SCAN_START_LOCATION;
import static com.dtstack.chunjun.table.options.SinkOptions.SINK_BUFFER_FLUSH_INTERVAL;
import static com.dtstack.chunjun.table.options.SinkOptions.SINK_BUFFER_FLUSH_MAX_ROWS;
import static com.dtstack.chunjun.table.options.SinkOptions.SINK_PARALLELISM;

public class ArgoDynamicTableFactory extends JdbcDynamicTableFactory {

    private static final String KERBEROS_PREFIX = "security.kerberos.";

    private static final String IDENTIFIER = "argo-x";

    @Override
    public String factoryIdentifier() {
        return IDENTIFIER;
    }

    @Override
    protected JdbcDialect getDialect() {
        return new ArgoDefaultDialect();
    }

    @Override
    public Set<ConfigOption<?>> optionalOptions() {
        Set<ConfigOption<?>> options = super.optionalOptions();
        options.add(ArgoOptions.PARTITION_TYPE);
        options.add(ArgoOptions.PARTITION);
        options.add(ArgoOptions.DIALECT);
        return options;
    }

    @Override
    public DynamicTableSource createDynamicTableSource(Context context) {
        final FactoryUtil.TableFactoryHelper helper =
                FactoryUtil.createTableFactoryHelper(this, context);
        // 1.所有的requiredOptions和optionalOptions参数
        final ReadableConfig config = helper.getOptions();

        // 2.参数校验
        helper.validateExcept(VERTX_PREFIX, DRUID_PREFIX, KERBEROS_PREFIX);
        validateConfigOptions(config);

        // 3.封装参数
        ResolvedSchema resolvedSchema = context.getCatalogTable().getResolvedSchema();

        final Map<String, Object> druidConf =
                JdbcLookupOptions.getLibConfMap(
                        context.getCatalogTable().getOptions(), DRUID_PREFIX);

        ArgoConfig argoConfig = getSourceConnectionConf(helper.getOptions());

        addKerberosConfig(argoConfig, context.getCatalogTable().getOptions());

        ArgoDialect argoDialect = ArgoDbUtil.getDialect(argoConfig);

        return new ArgoDynamicTableSource(
                argoConfig,
                getJdbcLookupConf(
                        helper.getOptions(),
                        context.getObjectIdentifier().getObjectName(),
                        druidConf),
                resolvedSchema,
                argoDialect,
                new ArgoInputFormatBuilder(new ArgoInputFormat()),
                this::initIncrementUtil);
    }

    @Override
    protected ArgoConfig getSourceConnectionConf(ReadableConfig readableConfig) {
        ArgoConfig argoConfig = new ArgoConfig();
        SourceConnectionConf conf = new SourceConnectionConf();
        argoConfig.setConnection(Collections.singletonList(conf));

        conf.setJdbcUrl(Collections.singletonList(readableConfig.get(URL)));
        conf.setTable(Collections.singletonList(readableConfig.get(TABLE_NAME)));
        conf.setSchema(readableConfig.get(SCHEMA_NAME));

        argoConfig.setUsername(readableConfig.get(USERNAME));
        argoConfig.setPassword(readableConfig.get(PASSWORD));

        argoConfig.setDialect(readableConfig.get(ArgoOptions.DIALECT));

        argoConfig.setParallelism(readableConfig.get(SCAN_PARALLELISM));
        argoConfig.setFetchSize(
                readableConfig.get(SCAN_FETCH_SIZE) == 0
                        ? getDefaultFetchSize()
                        : readableConfig.get(SCAN_FETCH_SIZE));
        argoConfig.setQueryTimeOut(readableConfig.get(SCAN_QUERY_TIMEOUT));

        argoConfig.setSplitPk(readableConfig.get(SCAN_PARTITION_COLUMN));
        argoConfig.setSplitStrategy(readableConfig.get(SCAN_PARTITION_STRATEGY));

        String increColumn = readableConfig.get(SCAN_INCREMENT_COLUMN);
        if (StringUtils.isNotBlank(increColumn)) {
            argoConfig.setIncrement(true);
            argoConfig.setIncreColumn(increColumn);
            argoConfig.setIncreColumnType(readableConfig.get(SCAN_INCREMENT_COLUMN_TYPE));
        }

        argoConfig.setStartLocation(readableConfig.get(SCAN_START_LOCATION));

        argoConfig.setRestoreColumn(readableConfig.get(SCAN_RESTORE_COLUMNNAME));
        argoConfig.setRestoreColumnType(readableConfig.get(SCAN_RESTORE_COLUMNTYPE));

        Optional<Integer> pollingInterval = readableConfig.getOptional(SCAN_POLLING_INTERVAL);
        if (pollingInterval.isPresent() && pollingInterval.get() > 0) {
            argoConfig.setPolling(true);
            argoConfig.setPollingInterval(pollingInterval.get());
            argoConfig.setFetchSize(
                    readableConfig.get(SCAN_FETCH_SIZE) == 0
                            ? SCAN_DEFAULT_FETCH_SIZE.defaultValue()
                            : readableConfig.get(SCAN_FETCH_SIZE));
        }

        resetTableInfo(argoConfig);
        return argoConfig;
    }

    @Override
    public IncrementKeyUtil<?, BigInteger> initIncrementUtil(String incrementType) {
        switch (ColumnType.getType(incrementType)) {
            case TIMESTAMP:
                return new TimestampTypeUtil();
            case DATE:
                return new ArgoDateTypeUtil();
            default:
                if (ColumnType.isNumberType(incrementType)) {
                    return new NumericTypeUtil();
                } else {
                    throw new FlinkxRuntimeException(
                            String.format("Unsupported incrementColumnType [%s]", incrementType));
                }
        }
    }

    private void addKerberosConfig(ArgoConfig argoConfig, Map<String, String> options) {
        Map<String, Object> kerberosConfig = ArgoOptions.getKerberosConfig(options);
        kerberosConfig.put("useLocalFile", true);
        argoConfig.setHadoopConfig(kerberosConfig);
    }

    @Override
    public DynamicTableSink createDynamicTableSink(Context context) {
        final FactoryUtil.TableFactoryHelper helper =
                FactoryUtil.createTableFactoryHelper(this, context);
        // 1.所有的requiredOptions和optionalOptions参数
        final ReadableConfig config = helper.getOptions();

        // 2.参数校验
        helper.validateExcept("properties.", KERBEROS_PREFIX);

        // 3.封装参数
        TableSchema physicalSchema =
                TableSchemaUtils.getPhysicalSchema(context.getCatalogTable().getSchema());

        ArgoConfig argoConfig = getSinkConnectionConf(config, physicalSchema);

        addKerberosConfig(argoConfig, context.getCatalogTable().getOptions());

        ArgoDialect dialect = ArgoDbUtil.getDialect(argoConfig);

        return new ArgoDynamicTableSink(
                dialect,
                physicalSchema,
                new ArgoOutputFormatBuilder(new ArgoOutputFormat()),
                argoConfig);
    }

    @Override
    protected ArgoConfig getSinkConnectionConf(ReadableConfig readableConfig, TableSchema schema) {
        ArgoConfig argoConfig = new ArgoConfig();

        SinkConnectionConf sinkConnectionConf = new SinkConnectionConf();
        argoConfig.setConnection(Collections.singletonList(sinkConnectionConf));

        sinkConnectionConf.setJdbcUrl(readableConfig.get(URL));
        sinkConnectionConf.setTable(Collections.singletonList(readableConfig.get(TABLE_NAME)));
        sinkConnectionConf.setSchema(readableConfig.get(SCHEMA_NAME));
        sinkConnectionConf.setAllReplace(readableConfig.get(SINK_ALL_REPLACE));
        sinkConnectionConf.setUsername(readableConfig.get(USERNAME));
        sinkConnectionConf.setPassword(readableConfig.get(PASSWORD));

        argoConfig.setUsername(readableConfig.get(USERNAME));
        argoConfig.setPassword(readableConfig.get(PASSWORD));

        argoConfig.setDialect(readableConfig.get(ArgoOptions.DIALECT));
        argoConfig.setPartition(readableConfig.get(ArgoOptions.PARTITION));
        argoConfig.setPartitionType(readableConfig.get(ArgoOptions.PARTITION_TYPE));

        argoConfig.setAllReplace(sinkConnectionConf.getAllReplace());
        argoConfig.setBatchSize(readableConfig.get(SINK_BUFFER_FLUSH_MAX_ROWS));
        argoConfig.setFlushIntervalMills(readableConfig.get(SINK_BUFFER_FLUSH_INTERVAL));
        argoConfig.setParallelism(readableConfig.get(SINK_PARALLELISM));
        argoConfig.setSemantic(readableConfig.get(SINK_SEMANTIC));

        List<String> keyFields =
                schema.getPrimaryKey().map(UniqueConstraint::getColumns).orElse(null);
        argoConfig.setUniqueKey(keyFields);
        resetTableInfo(argoConfig);
        return argoConfig;
    }
}
