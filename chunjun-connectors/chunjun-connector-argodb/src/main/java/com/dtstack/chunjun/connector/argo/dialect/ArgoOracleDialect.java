/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.argo.dialect;

import com.dtstack.chunjun.connector.jdbc.conf.JdbcConf;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static java.lang.String.format;

public class ArgoOracleDialect extends ArgoDialect {

    private static final String DIALECT_NAME = "argo_oracle";

    @Override
    public String dialectName() {
        return DIALECT_NAME;
    }

    @Override
    public Optional<String> getReplaceStatement(
            String schema, String tableName, String[] fieldNames) {
        throw new RuntimeException("Oracle does not support replace sql");
    }

    @Override
    public boolean supportUpsert() {
        return true;
    }

    @Override
    public Optional<String> getUpsertStatement(
            String schema,
            String tableName,
            String[] fieldNames,
            String[] uniqueKeyFields,
            boolean allReplace) {
        if (uniqueKeyFields == null || uniqueKeyFields.length == 0) {
            throw new RuntimeException(
                    "updateKey is not allow empty when write mode is update, you can specify updateKey in the task json");
        }
        tableName = buildTableInfoWithSchema(schema, tableName);
        StringBuilder mergeIntoSql = new StringBuilder(64);
        mergeIntoSql
                .append("MERGE INTO ")
                .append(tableName)
                .append(" T1 USING (")
                .append(buildDualQueryStatement(fieldNames))
                .append(") T2 ON (")
                .append(buildEqualConditions(uniqueKeyFields))
                .append(") ");

        String updateSql = buildUpdateConnection(fieldNames, uniqueKeyFields, allReplace);

        if (StringUtils.isNotEmpty(updateSql)) {
            mergeIntoSql.append(" WHEN MATCHED THEN UPDATE SET ");
            mergeIntoSql.append(updateSql);
        }

        mergeIntoSql
                .append(" WHEN NOT MATCHED THEN ")
                .append("INSERT (")
                .append(
                        Arrays.stream(fieldNames)
                                .map(this::quoteIdentifier)
                                .collect(Collectors.joining(", ")))
                .append(") VALUES (")
                .append(
                        Arrays.stream(fieldNames)
                                .map(col -> "T2." + quoteIdentifier(col))
                                .collect(Collectors.joining(", ")))
                .append(")");

        return Optional.of(mergeIntoSql.toString());
    }

    @Override
    public String getDeleteStatement(
            String schema,
            String tableName,
            String[] conditionFields,
            String[] nullConditionFields,
            String[] columnType) {

        ArrayList<String> skipColumnOnDelete = new ArrayList<>();
        if (columnType != null) {
            for (int i = 0; i < columnType.length; i++) {
                if (columnType[i].equalsIgnoreCase("BLOB")
                        || columnType[i].equalsIgnoreCase("CLOB")
                        || columnType[i].equalsIgnoreCase("LONG")
                        || columnType[i].equalsIgnoreCase("LONG RAW")
                        || columnType[i].equalsIgnoreCase("NCLOB")
                        || columnType[i].equalsIgnoreCase("BFILE")) {
                    skipColumnOnDelete.add(conditionFields[i]);
                }
            }
        }

        ArrayList<String> nullFields = new ArrayList<>(Arrays.asList(nullConditionFields));

        List<String> conditions =
                Arrays.stream(conditionFields)
                        .filter(i -> !nullFields.contains(i) && !skipColumnOnDelete.contains(i))
                        .map(f -> format("%s = :%s", quoteIdentifier(f), f))
                        .collect(Collectors.toList());

        Arrays.stream(nullConditionFields)
                .filter(i -> !skipColumnOnDelete.contains(i))
                .map(f -> format("%s IS NULL", quoteIdentifier(f)))
                .forEach(conditions::add);

        String conditionClause = String.join(" AND ", conditions);
        return "DELETE FROM "
                + buildTableInfoWithSchema(schema, tableName)
                + " WHERE "
                + conditionClause;
    }

    /** build select sql , such as (SELECT ? "A",? "B" FROM DUAL) */
    public String buildDualQueryStatement(String[] column) {
        StringBuilder sb = new StringBuilder("SELECT ");
        String collect =
                Arrays.stream(column)
                        .map(col -> ":" + col + " " + quoteIdentifier(col))
                        .collect(Collectors.joining(", "));
        sb.append(collect).append(" FROM DUAL");
        return sb.toString();
    }

    /** build sql part e.g: T1.`A` = T2.`A`, T1.`B` = T2.`B` */
    private String buildEqualConditions(String[] uniqueKeyFields) {
        return Arrays.stream(uniqueKeyFields)
                .map(col -> "T1." + quoteIdentifier(col) + " = T2." + quoteIdentifier(col))
                .collect(Collectors.joining(" and "));
    }

    /** build T1."A"=T2."A" or T1."A"=nvl(T2."A",T1."A") */
    private String buildUpdateConnection(
            String[] fieldNames, String[] uniqueKeyFields, boolean allReplace) {
        List<String> uniqueKeyList = Arrays.asList(uniqueKeyFields);
        return Arrays.stream(fieldNames)
                .filter(col -> !uniqueKeyList.contains(col))
                .map(col -> buildConnectString(allReplace, col))
                .collect(Collectors.joining(","));
    }

    /**
     * Depending on parameter [allReplace] build different sql part. e.g T1."A"=T2."A" or
     * T1."A"=nvl(T2."A",T1."A")
     */
    private String buildConnectString(boolean allReplace, String col) {
        return allReplace
                ? "T1." + quoteIdentifier(col) + " = T2." + quoteIdentifier(col)
                : "T1."
                        + quoteIdentifier(col)
                        + " =NVL(T2."
                        + quoteIdentifier(col)
                        + ",T1."
                        + quoteIdentifier(col)
                        + ")";
    }

    @Override
    public String getRowNumColumn(String orderBy) {
        return "rownum as " + getRowNumColumnAlias();
    }

    @Override
    public String getRowIdRangeStatement(JdbcConf jdbcConf, int minNumSplits) {
        String oracleRowIdRangeStatement =
                "SELECT ORA_ROWID,LEAD(ORA_ROWID,1)OVER(ORDER BY RN ASC)"
                        + "FROM("
                        + "        WITH CNT AS (SELECT COUNT(*)"
                        + "                FROM %s"
                        + "        )"
                        + "        SELECT RN,ORA_ROWID"
                        + "        FROM (SELECT ROWNUM RN, ORA_ROWID"
                        + "              FROM (SELECT ROWID ORA_ROWID"
                        + "                    FROM %s"
                        + "                    ORDER BY ROWID"
                        + "                       ))"
                        + "        WHERE RN IN (SELECT (ROWNUM-1) * TRUNC((SELECT * FROM CNT)/%s)+1"
                        + "                FROM (%s) WHERE ROWNUM <= %s"
                        + "            ))";
        String table;
        if (StringUtils.isNotBlank(jdbcConf.getCustomSql())) {
            table = "(" + jdbcConf.getCustomSql() + ")";
        } else {
            table = jdbcConf.getSchema() + "." + jdbcConf.getTable();
        }
        return String.format(
                oracleRowIdRangeStatement,
                table,
                table,
                minNumSplits,
                getSequenceStatement(minNumSplits),
                minNumSplits);
    }

    private String getSequenceStatement(int numSplits) {
        List<String> list = new ArrayList<>(numSplits);
        for (int i = 0; i < numSplits; i++) {
            list.add(String.format("select %s from DUAL", i));
        }
        return String.join(" union ", list);
    }

    @Override
    public String getRowIdType() {
        return "varchar";
    }

    @Override
    public String getRowIdName() {
        return "ROWID";
    }

    @Override
    public boolean supportRowId() {
        return true;
    }

    @Override
    public String wrapLimitQuerySql(String querySql, int limitNum) {
        return "SELECT * FROM (" + querySql + ") WHERE ROWNUM <= " + limitNum;
    }

    @Override
    public String quoteIdentifier(String identifier) {
        return identifier;
    }
}
