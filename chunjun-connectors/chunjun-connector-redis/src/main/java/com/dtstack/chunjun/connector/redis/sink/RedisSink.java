/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.redis.sink;

import com.dtstack.chunjun.connector.redis.conf.RedisConf;
import com.dtstack.chunjun.converter.AbstractRowConverter;

import org.apache.flink.api.connector.sink2.Sink;
import org.apache.flink.api.connector.sink2.SinkWriter;
import org.apache.flink.api.connector.sink2.WriterInitContext;
import org.apache.flink.table.data.RowData;

import java.io.IOException;

/**
 * <AUTHOR>
 * @create 2021-06-16 15:12
 * @description
 */
public class RedisSink implements Sink<RowData> {
    private final AbstractRowConverter converter;
    private final RedisConf redisConf;

    public RedisSink(AbstractRowConverter converter, RedisConf redisConf) {
        this.converter = converter;
        this.redisConf = redisConf;
    }

    @Override
    public SinkWriter<RowData> createWriter(WriterInitContext context) throws IOException {
        return new RedisSinkWriter(converter, redisConf);
    }
}
