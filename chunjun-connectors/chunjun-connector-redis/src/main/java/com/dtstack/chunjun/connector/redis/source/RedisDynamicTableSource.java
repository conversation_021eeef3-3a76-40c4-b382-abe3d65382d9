/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.redis.source;

import com.dtstack.chunjun.connector.redis.conf.RedisConf;
import com.dtstack.chunjun.connector.redis.converter.RedisSqlConverter;
import com.dtstack.chunjun.connector.redis.lookup.RedisAllLookupFunction;
import com.dtstack.chunjun.connector.redis.lookup.RedisLookupHelper;
import com.dtstack.chunjun.connector.redis.lookup.RedisLruLookupFunctionChunjun;
import com.dtstack.chunjun.enums.CacheType;
import com.dtstack.chunjun.lookup.conf.LookupConf;
import com.dtstack.chunjun.lookup.util.LookupUtil;
import com.dtstack.chunjun.table.connector.source.ParallelAsyncLookupFunctionProvider;
import com.dtstack.chunjun.table.connector.source.ParallelLookupFunctionProvider;
import com.dtstack.chunjun.throwable.FlinkxRuntimeException;

import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.table.catalog.ResolvedSchema;
import org.apache.flink.table.catalog.UniqueConstraint;
import org.apache.flink.table.connector.source.DynamicTableSource;
import org.apache.flink.table.connector.source.LookupTableSource;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.util.Preconditions;

import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021-06-21 19:08
 * @description
 */
public class RedisDynamicTableSource implements LookupTableSource {

    protected ResolvedSchema physicalSchema;
    protected final RedisConf redisConf;
    protected final LookupConf lookupConf;

    protected DataType physicalRowDataType;

    public RedisDynamicTableSource(
            ResolvedSchema physicalSchema, RedisConf redisConf, LookupConf lookupConf) {
        this.physicalSchema = physicalSchema;
        this.redisConf = redisConf;
        this.lookupConf = lookupConf;
        this.physicalRowDataType = physicalSchema.toPhysicalRowDataType();
    }

    @Override
    public LookupRuntimeProvider getLookupRuntimeProvider(LookupContext context) {
        Pair<String[], int[]> keyNamesAndIndexes =
                LookupUtil.getKeyNamesAndIndexes(context.getKeys(), this.physicalRowDataType);
        final RowType rowType = (RowType) physicalRowDataType.getLogicalType();
        RedisLookupHelper redisLookupHelper = buildRedisLookupHelper(context);

        if (lookupConf.getCache().equalsIgnoreCase(CacheType.ALL.toString())) {
            return ParallelLookupFunctionProvider.of(
                    new RedisAllLookupFunction(
                            redisConf,
                            lookupConf,
                            rowType.getFieldNames().toArray(new String[0]),
                            redisLookupHelper,
                            keyNamesAndIndexes.getValue(),
                            new RedisSqlConverter(rowType)),
                    lookupConf.getParallelism());
        }
        return ParallelAsyncLookupFunctionProvider.of(
                new RedisLruLookupFunctionChunjun(
                        redisConf,
                        lookupConf,
                        redisLookupHelper,
                        new RedisSqlConverter(rowType),
                        keyNamesAndIndexes.getValue()),
                lookupConf.getParallelism());
    }

    public RedisLookupHelper buildRedisLookupHelper(LookupContext context) {
        List<String> primaryKeys =
                physicalSchema
                        .getPrimaryKey()
                        .map(UniqueConstraint::getColumns)
                        .orElseThrow(
                                () ->
                                        new FlinkxRuntimeException(
                                                "redis lookup table must define primary key"));
        Preconditions.checkArgument(
                context.getKeys().length >= primaryKeys.size(),
                "redis lookup table look up key must contains all primary key");

        List<Tuple2<Integer, Integer>> keyIndexes = new ArrayList<>(primaryKeys.size());
        String[] keyNames = new String[context.getKeys().length];
        List<Tuple2<Integer, String>> valueFilterKeys =
                new ArrayList<>(context.getKeys().length - primaryKeys.size());
        String[] columnNames = physicalSchema.getColumnNames().toArray(new String[0]);
        for (int i = 0; i < context.getKeys().length; i++) {
            int[] lookKeyArr = context.getKeys()[i];
            Preconditions.checkArgument(
                    lookKeyArr.length == 1, "redis only support non-nested look up keys");
            int lookKeyIndex = lookKeyArr[0];
            String lookKeyName = columnNames[lookKeyIndex];
            keyNames[i] = lookKeyName;
            if (primaryKeys.contains(lookKeyName)) {
                keyIndexes.add(Tuple2.of(i, lookKeyIndex));
            } else {
                valueFilterKeys.add(Tuple2.of(i, lookKeyName));
            }
        }
        Preconditions.checkArgument(
                keyIndexes.size() == primaryKeys.size(),
                "redis lookup table look up key must contains all primary key");
        int[] sortedKeyIndex =
                keyIndexes.stream()
                        .sorted(Comparator.comparingInt(tuple2 -> tuple2.f1))
                        .map(tuple2 -> tuple2.f0)
                        .mapToInt(Integer::intValue)
                        .toArray();
        return new RedisLookupHelper(
                redisConf.getTableName(), sortedKeyIndex, valueFilterKeys, keyNames);
    }

    @Override
    public DynamicTableSource copy() {
        return new RedisDynamicTableSource(physicalSchema, redisConf, lookupConf);
    }

    @Override
    public String asSummaryString() {
        return "redis";
    }
}
