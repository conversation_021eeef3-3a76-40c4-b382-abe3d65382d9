/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.oracle9.sink;

import com.dtstack.chunjun.conf.FieldConf;
import com.dtstack.chunjun.conf.TypeConfig;
import com.dtstack.chunjun.connector.jdbc.sink.JdbcOutputFormat;
import com.dtstack.chunjun.connector.jdbc.util.JdbcUtil;
import com.dtstack.chunjun.connector.oracle9.util.IOracle9Helper;
import com.dtstack.chunjun.connector.oracle9.util.Oracle9Util;
import com.dtstack.chunjun.enums.EWriteMode;
import com.dtstack.chunjun.enums.Semantic;
import com.dtstack.chunjun.common.util.ClassUtil;
import com.dtstack.chunjun.common.util.GsonUtil;
import com.dtstack.chunjun.common.util.JsonUtil;
import com.dtstack.chunjun.util.PluginUtil;
import com.dtstack.chunjun.common.util.RetryUtil;
import com.dtstack.chunjun.util.SysUtil;

import org.apache.flink.util.FlinkUserCodeClassLoaders;

import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

public class OracleOutputFormat extends JdbcOutputFormat {

    protected static final int SECOND_WAIT = 30;

    private URLClassLoader childFirstClassLoader;
    private Throwable classLoaderThrowable;
    private IOracle9Helper helper;

    // 压缩文件名称
    private final String ZIP_NAME = "flinkx-oracle9.zip";
    // taskmanager本地路径
    private String currentPath;
    // 解压后的jar包路径
    private String needLoadJarPath;
    // 解压jar包临时路径
    private String unzipTempPath;
    // 解压完成路径
    private String actionPath;

    @Override
    protected void openInternal(int taskNumber, int numTasks) {
        actionBeforeWriteData();
        try {
            dbConn = getConnection();
            // 默认关闭事务自动提交，手动控制事务
            if (Semantic.EXACTLY_ONCE == semantic) {
                dbConn.setAutoCommit(false);
            }
            initColumnList();
            if (!EWriteMode.INSERT.name().equalsIgnoreCase(jdbcConf.getMode())) {
                List<String> updateKey = jdbcConf.getUniqueKey();
                if (CollectionUtils.isEmpty(updateKey)) {
                    List<String> tableIndex =
                            JdbcUtil.getTableUniqueIndex(
                                    jdbcDialect.getTableIdentify(
                                            jdbcConf.getSchema(), jdbcConf.getTable()),
                                    dbConn);
                    jdbcConf.setUniqueKey(tableIndex);
                    LOG.info("updateKey = {}", JsonUtil.toJson(tableIndex));
                }
            }

            buildStatementExecutor();
            LOG.info("subTask[{}}] wait finished", taskNumber);
        } catch (SQLException sqe) {
            throw new IllegalArgumentException("open() failed.", sqe);
        } finally {
            JdbcUtil.commit(dbConn);
        }
    }

    /** init columnNameList、 columnTypeList and hasConstantField */
    protected void initColumnList() {
        Pair<List<String>, List<TypeConfig>> pair = getTableMetaData();

        List<FieldConf> fieldList = jdbcConf.getColumn();
        List<String> fullColumnList = pair.getLeft();
        List<TypeConfig> fullColumnTypeList = pair.getRight();
        handleColumnList(fieldList, fullColumnList, fullColumnTypeList);
    }

    /**
     * for override. because some databases have case-sensitive metadata。
     *
     * @return
     */
    protected Pair<List<String>, List<TypeConfig>> getTableMetaData() {
        return jdbcDialect.getTableMetaData(dbConn, jdbcConf);
    }

    /**
     * detailed logic for handling column
     *
     * @param fieldList
     * @param fullColumnList
     * @param fullColumnTypeList
     */
    protected void handleColumnList(
            List<FieldConf> fieldList,
            List<String> fullColumnList,
            List<TypeConfig> fullColumnTypeList) {
        if (fieldList.size() == 1 && Objects.equals(fieldList.get(0).getName(), "*")) {
            columnNameList = fullColumnList;
            columnTypeList = fullColumnTypeList;
            return;
        }

        columnNameList = new ArrayList<>(fieldList.size());
        columnTypeList = new ArrayList<>(fieldList.size());
        for (FieldConf fieldConf : fieldList) {
            columnNameList.add(fieldConf.getName());
            for (int i = 0; i < fullColumnList.size(); i++) {
                if (fieldConf.getName().equalsIgnoreCase(fullColumnList.get(i))) {
                    columnTypeList.add(fullColumnTypeList.get(i));
                    break;
                }
            }
        }
    }

    @Override
    protected Connection getConnection() throws SQLException {

        List<URL> needJar = Lists.newArrayList();
        Set<URL> collect = new HashSet<>();
        for (String s1 : PluginUtil.getAllJarNames(new File(needLoadJarPath))) {
            try {
                collect.add(new URL("file:" + needLoadJarPath + File.separator + s1));
            } catch (MalformedURLException e) {
                throw new RuntimeException(
                        "get  [" + "file:" + needLoadJarPath + File.separator + s1 + "] failed", e);
            }
        }
        needJar.addAll(collect);
        LOG.info("need jars {} ", GsonUtil.GSON.toJson(needJar));

        ClassLoader parentClassLoader = getClass().getClassLoader();
        List<String> list = new LinkedList<>();
        list.add("org.apache.flink");
        list.add("com.dtstack.chunjun");

        childFirstClassLoader =
                FlinkUserCodeClassLoaders.childFirst(
                        needJar.toArray(new URL[0]),
                        parentClassLoader,
                        list.toArray(new String[0]),
                        throwable -> classLoaderThrowable = throwable,
                        false);

        ClassLoader contextClassLoader = Thread.currentThread().getContextClassLoader();
        Thread.currentThread().setContextClassLoader(childFirstClassLoader);

        ClassUtil.forName(jdbcDialect.defaultDriverName().get(), childFirstClassLoader);

        try {
            helper = Oracle9Util.getOracleHelper(childFirstClassLoader);
            long startTime = System.nanoTime();
            Connection connection =
                    RetryUtil.executeWithRetry(
                            () ->
                                    helper.getConnection(
                                            jdbcConf.getJdbcUrl(),
                                            jdbcConf.getUsername(),
                                            jdbcConf.getPassword()),
                            3,
                            2000,
                            false);
            long convert =
                    TimeUnit.MILLISECONDS.convert(
                            System.nanoTime() - startTime, TimeUnit.NANOSECONDS);
            LOG.info(
                    "get connect elapsed {} {}",
                    convert,
                    TimeUnit.MILLISECONDS.toString().toLowerCase(Locale.ENGLISH));
            return connection;
        } catch (Exception e) {
            String message =
                    String.format(
                            "can not get oracle connection , dbUrl = %s, e = %s",
                            jdbcConf.getJdbcUrl(), ExceptionUtil.getErrorMessage(e));
            throw new RuntimeException(message, e);
        } finally {
            Thread.currentThread().setContextClassLoader(contextClassLoader);
        }
    }

    protected void actionBeforeWriteData() {
        String osName = System.getProperties().getProperty("os.name");
        if (osName.toLowerCase(Locale.ENGLISH).contains("windows")) {
            // window环境
            currentPath = Paths.get("").toAbsolutePath().toString();
        } else {
            // linux环境
            currentPath = SysUtil.getCurrentPath();
        }
        LOG.info("current path is {}", currentPath);

        File zipFile = new File(currentPath);
        zipFile = SysUtil.findFile(zipFile, ZIP_NAME);
        if (zipFile == null) {
            throw new RuntimeException(
                    "File "
                            + zipFile.getAbsolutePath()
                            + "  not exists,please sure upload this file");
        }

        needLoadJarPath =
                zipFile.getAbsolutePath()
                        .substring(0, zipFile.getAbsolutePath().lastIndexOf(".zip"));
        actionPath = needLoadJarPath + File.separator + "action";
        unzipTempPath = needLoadJarPath + File.separator + ".unzip";

        LOG.info("needLoadJarPath {}", needLoadJarPath);

        if (waitForActionFinishedBeforeWrite()) {
            // 获取zip进行解压缩
            try {
                File unzipDirectory = new File(unzipTempPath);
                if (!unzipDirectory.mkdir()) {
                    throw new RuntimeException("create directory [ " + unzipTempPath + "] failed");
                }

                List<String> jars = SysUtil.unZip(zipFile.getAbsolutePath(), unzipTempPath);

                for (String jarPath : jars) {
                    File file = new File(jarPath);
                    file.renameTo(new File(needLoadJarPath + File.separator + file.getName()));
                }

                unzipDirectory.delete();

                File actionFile = new File(actionPath);
                if (!actionFile.mkdir()) {
                    throw new RuntimeException(
                            "create file [ " + actionFile.getAbsolutePath() + "] failed");
                }
            } catch (IOException e) {
                new File(needLoadJarPath).deleteOnExit();
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 创建解压目录 如果创建成功 则当前subtask进行解压 其余的channel进行等待
     *
     * @return
     */
    protected boolean waitForActionFinishedBeforeWrite() {
        boolean result = false;
        File unzipFile = new File(needLoadJarPath);
        File actionFile = new File(actionPath);
        int n = 0;
        // 如果解压路径存在就不新建 否则代表其他的任务在新建中
        if (!unzipFile.exists()) {
            try {
                result = unzipFile.mkdir();
            } catch (Exception e) {
                if (!unzipFile.exists()) {
                    throw new RuntimeException("create directory" + needLoadJarPath + " failed", e);
                }
            }
        }
        // 如果创建成功 就返回true 否则就等待其他channel完成新建
        if (result) {
            return result;
        }

        while (!actionFile.exists()) {
            if (n > SECOND_WAIT) {
                throw new RuntimeException("Wait action finished before write timeout");
            }
            SysUtil.sleep(3000);
            n++;
        }
        // 如果等到其他任务创建成功 就返回false
        return result;
    }
}
