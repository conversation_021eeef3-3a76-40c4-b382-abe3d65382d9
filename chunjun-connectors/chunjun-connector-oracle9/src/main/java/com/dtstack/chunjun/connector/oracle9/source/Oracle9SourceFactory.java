/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.oracle9.source;

import com.dtstack.chunjun.conf.SyncConf;
import com.dtstack.chunjun.connector.jdbc.source.JdbcInputFormatBuilder;
import com.dtstack.chunjun.connector.jdbc.source.JdbcSourceFactory;
import com.dtstack.chunjun.connector.jdbc.util.increment.NumericTypeUtil;
import com.dtstack.chunjun.connector.oracle9.dialect.Oracle9Dialect;
import com.dtstack.chunjun.connector.oracle9.util.increment.Oracle9RowIdTypeUtil;
import com.dtstack.chunjun.connector.oracle9.util.increment.Oracle9TimestampTypeUtil;
import com.dtstack.chunjun.converter.AbstractRowConverter;
import com.dtstack.chunjun.enums.ColumnType;
import com.dtstack.chunjun.util.TableUtil;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.RowType;

/**
 * company www.dtstack.com
 *
 * <AUTHOR>
 */
public class Oracle9SourceFactory extends JdbcSourceFactory {

    public Oracle9SourceFactory(SyncConf syncConf, StreamExecutionEnvironment env) {
        super(syncConf, env, new Oracle9Dialect());
    }

    @Override
    protected JdbcInputFormatBuilder getBuilder() {
        return new JdbcInputFormatBuilder(new OracleInputFormat());
    }

    @Override
    protected void initIncrementUtil(String incrementName, String incrementType) {
        switch (ColumnType.getType(incrementType)) {
            case TIMESTAMP:
            case DATE:
                incrementKeyUtil = new Oracle9TimestampTypeUtil();
                break;
            default:
                if (ColumnType.isNumberType(incrementType)) {
                    incrementKeyUtil = new NumericTypeUtil();
                } else if (incrementName.equalsIgnoreCase(jdbcDialect.getRowIdName())) {
                    incrementKeyUtil = new Oracle9RowIdTypeUtil();
                } else {
                    throw new FlinkxRuntimeException(
                            String.format(
                                    "Unsupported incrementColumnType [%s], incrementColumnName [%s]",
                                    incrementType, incrementName));
                }
        }
    }

    @Override
    public DataStream<RowData> createSource() {
        JdbcInputFormatBuilder builder = getBuilder();
        initPollingConfig();
        initIncrementConfig();
        initRestore();
        int fetchSize = jdbcConf.getFetchSize();
        jdbcConf.setFetchSize(fetchSize == 0 ? getDefaultFetchSize() : fetchSize);

        int queryTimeOut = jdbcConf.getQueryTimeOut();
        jdbcConf.setQueryTimeOut(queryTimeOut == 0 ? DEFAULT_QUERY_TIMEOUT : queryTimeOut);

        builder.setJdbcConf(jdbcConf);
        builder.setJdbcDialect(jdbcDialect);
        builder.setIncrementKeyUtil(incrementKeyUtil);

        AbstractRowConverter converter = null;
        if (!isSyncJob) {
            checkConstant(jdbcConf);
            final RowType rowType =
                    TableUtil.createRowType(jdbcConf.getColumn(), getRawTypeMapper());
            converter = jdbcDialect.getSqlConverter(rowType, jdbcConf);
        }
        builder.setRowConverter(converter, isSyncJob);

        return createInput(builder.finish());
    }
}
