<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>chunjun-connectors</artifactId>
		<groupId>com.dtstack.chunjun</groupId>
		<version>2.0-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>chunjun-connector-oracle9</artifactId>
	<name>ChunJun : Connectors : Oracle9</name>


	<dependencies>
		<dependency>
			<groupId>com.dtstack.chunjun</groupId>
			<artifactId>chunjun-connector-jdbc-base</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc6</artifactId>
			<version>11.2.0.4</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.oracle.database.xml</groupId>
			<artifactId>xdb6</artifactId>
			<version>11.2.0.4</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.oracle.database.xml</groupId>
			<artifactId>xmlparserv2</artifactId>
			<version>11.2.0.4</version>
			<scope>provided</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
			</plugin>

			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-resources</id>
						<!-- here the phase you need -->
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<tasks>
								<copy todir="${basedir}/../../${dist.dir}/connector/oracle9/"
									  file="${basedir}/target/${project.artifactId}-${project.version}.jar"/>
								<move file="${basedir}/../../${dist.dir}/connector/oracle9/${project.artifactId}-${project.version}.jar"
									  tofile="${basedir}/../../${dist.dir}/connector/oracle9/${project.artifactId}-${git.branch}.jar"/>
								<delete>
									<fileset dir="${basedir}/../../${dist.dir}/connector/oracle9/"
											 includes="${project.artifactId}-*.jar"
											 excludes="${project.artifactId}-${git.branch}.jar"/>
								</delete>

								<copy todir="${basedir}/../../${dist.dir}/connector/oracle9/">
									<fileset dir="${basedir}/src/main/resources/">
										<include name="flinkx-oracle9.zip" />
									</fileset>
								</copy>
							</tasks>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>com.diffplug.spotless</groupId>
				<artifactId>spotless-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>
