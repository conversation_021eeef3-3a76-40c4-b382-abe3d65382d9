/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.hbase.util;

import com.dtstack.chunjun.common.security.CustomModule;
import com.dtstack.chunjun.common.security.KerberosUtil;
import com.dtstack.chunjun.common.security.SecurityUtil;
import com.dtstack.chunjun.common.util.JsonUtil;
import com.dtstack.security.SecurityContext;

import org.apache.flink.api.common.cache.DistributedCache;
import org.apache.flink.runtime.security.modules.SecurityModule;
import org.apache.flink.table.data.DecimalData;
import org.apache.flink.table.data.TimestampData;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.logical.LogicalTypeRoot;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.HConstants;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Admin;
import org.apache.hadoop.hbase.client.BufferedMutator;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.apache.hadoop.hbase.client.RegionLocator;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.hadoop.security.UserGroupInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.security.PrivilegedAction;
import java.util.Map;
import java.util.function.Function;

import static com.dtstack.chunjun.connector.hbase.util.HBaseConfigUtils.KEY_JAVA_SECURITY_KRB5_CONF;

/**
 * The utility class of HBase
 *
 * <p>Company: www.dtstack.com
 *
 * <AUTHOR>
 */
public class HBaseHelper {
    private static final Logger LOG = LoggerFactory.getLogger(HBaseHelper.class);

    private static final String KEY_HBASE_SECURITY_AUTHENTICATION = "hbase.security.authentication";
    private static final String KEY_HBASE_SECURITY_AUTHORIZATION = "hbase.security.authorization";
    private static final String KEY_HBASE_SECURITY_AUTH_ENABLE = "hbase.security.auth.enable";
    private static CustomModule customModule;

    public static Connection initConnection(
            SecurityContext securityContext, Map<String, Object> hbaseConfig) {
        try {
            try {
                if (com.dtstack.chunjun.common.security.KerberosUtil.addJaasModule(hbaseConfig)) {
                    securityContext.installModule(
                            SecurityUtil.buildJaasModuleProperties(hbaseConfig));
                }
            } catch (Exception e) {
                LOG.warn("security context installModule failed.", e);
            }
            return securityContext.execute(
                    null,
                    t -> {
                        String property = "zookeeper.sasl.clientconfig";
                        String clientName = System.getProperty(property);
                        try {
                            System.setProperty(property, KerberosUtil.getSectionName(hbaseConfig));
                            return HBaseHelper.getHbaseConnection(hbaseConfig);
                        } finally {
                            try {
                                securityContext.uninstallModule();
                            } catch (Exception e) {
                                LOG.warn("uninstallModule failed.", e);
                            }
                            if (StringUtils.isNotBlank(clientName)) {
                                System.setProperty(property, clientName);
                            } else {
                                System.clearProperty(property);
                            }
                        }
                    });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static Connection getHbaseConnection(Map<String, Object> hbaseConfigMap) {
        Validate.isTrue(MapUtils.isNotEmpty(hbaseConfigMap), "hbaseConfig不能为空Map结构!");

        try {
            Configuration hConfiguration = getConfig(hbaseConfigMap);
            return ConnectionFactory.createConnection(hConfiguration);
        } catch (IOException e) {
            LOG.error("Get connection fail with config:{}", hbaseConfigMap);
            throw new RuntimeException(e);
        }
    }

    private static synchronized Connection getConnectionWithKerberos(
            Map<String, Object> hbaseConfigMap,
            DistributedCache distributedCache,
            String jobId,
            String taskNumber) {
        String property = "zookeeper.sasl.clientconfig";
        try {
            setKerberosConf(hbaseConfigMap);
            UserGroupInformation ugi = getUgi(hbaseConfigMap, distributedCache, jobId, taskNumber);
            return ugi.doAs(
                    (PrivilegedAction<Connection>)
                            () -> {
                                try {
                                    if (!"Client"
                                            .equals(
                                                    System.setProperty(
                                                            property,
                                                            KerberosUtil.getSectionName(
                                                                    hbaseConfigMap)))) {
                                        System.setProperty(
                                                property,
                                                KerberosUtil.getSectionName(hbaseConfigMap));
                                    }
                                    Configuration hConfiguration = getConfig(hbaseConfigMap);
                                    return ConnectionFactory.createConnection(hConfiguration);
                                } catch (IOException e) {
                                    LOG.error("Get connection fail with config:{}", hbaseConfigMap);
                                    throw new RuntimeException(e);
                                }
                            });
        } catch (Exception e) {
            throw new RuntimeException("Login kerberos error", e);
        } finally {
            try {
                KerberosUtil.unInstallCustomModule(customModule);
            } catch (SecurityModule.SecurityInstallException e) {
                LOG.warn("Custom module uninstall failed. {}", e.getMessage());
            }
            // restore
            System.setProperty(property, "Client");
            customModule = null;
        }
    }

    public static UserGroupInformation getUgi(
            Map<String, Object> hbaseConfigMap,
            DistributedCache distributedCache,
            String jobId,
            String taskNumber)
            throws IOException {
        String keytabFileName = KerberosUtil.getPrincipalFileName(hbaseConfigMap);

        keytabFileName =
                KerberosUtil.loadFile(
                        hbaseConfigMap, keytabFileName, distributedCache, jobId, taskNumber);
        String principal = KerberosUtil.getPrincipal(hbaseConfigMap, keytabFileName);
        KerberosUtil.loadKrb5Conf(hbaseConfigMap, distributedCache, jobId, taskNumber);
        KerberosUtil.refreshConfig();

        try {
            customModule =
                    KerberosUtil.installCustomModule(
                            hbaseConfigMap, distributedCache, jobId, taskNumber);
        } catch (SecurityModule.SecurityInstallException e) {
            LOG.warn("Install custom security module failed.", e.getMessage());
        }

        return KerberosUtil.loginAndReturnUgi(
                principal, keytabFileName, System.getProperty(KEY_JAVA_SECURITY_KRB5_CONF));
    }

    public static Configuration getConfig(Map<String, Object> hbaseConfigMap) {
        Configuration hConfiguration = HBaseConfiguration.create();
        if (MapUtils.isEmpty(hbaseConfigMap)) {
            return hConfiguration;
        }

        for (Map.Entry<String, Object> entry : hbaseConfigMap.entrySet()) {
            if (entry.getValue() != null && !(entry.getValue() instanceof Map)) {
                hConfiguration.set(entry.getKey(), entry.getValue().toString());
            }
        }

        if (!hbaseConfigMap.containsKey(HBaseHelper.KEY_HBASE_SECURITY_AUTHENTICATION)) {
            hConfiguration.set(HBaseHelper.KEY_HBASE_SECURITY_AUTHENTICATION, "simple");
            if (LOG.isDebugEnabled()) {
                LOG.debug(
                        "The hbase.security.authentication is not explicitly declared so the default value simple is used");
            }
        } else if (LOG.isDebugEnabled()) {
            LOG.debug(
                    "The hbase.security.authentication is explicitly declared and the value is :"
                            + hConfiguration.get(HBaseHelper.KEY_HBASE_SECURITY_AUTHENTICATION));
        }

        if (LOG.isDebugEnabled()) {
            LOG.debug("The hbase config is:" + JsonUtil.toPrintJson(hbaseConfigMap));
            LOG.debug("The final hbase configuration after merge is:" + hConfiguration.toString());
        }
        return hConfiguration;
    }

    /** 设置hbase 开启kerberos 连接必要的固定参数 */
    public static void setKerberosConf(Map<String, Object> hbaseConfigMap) {
        hbaseConfigMap.put(KEY_HBASE_SECURITY_AUTHORIZATION, KerberosUtil.KRB_STR);
        hbaseConfigMap.put(KEY_HBASE_SECURITY_AUTHENTICATION, KerberosUtil.KRB_STR);
        hbaseConfigMap.put(KEY_HBASE_SECURITY_AUTH_ENABLE, true);
    }

    public static RegionLocator getRegionLocator(Connection hConnection, String userTable) {
        TableName hTableName = TableName.valueOf(userTable);
        Admin admin = null;
        RegionLocator regionLocator = null;
        try {
            admin = hConnection.getAdmin();
            HBaseHelper.checkHbaseTable(admin, hTableName);
            regionLocator = hConnection.getRegionLocator(hTableName);
        } catch (Exception e) {
            HBaseHelper.closeRegionLocator(regionLocator);
            HBaseHelper.closeAdmin(admin);
            HBaseHelper.closeConnection(hConnection);
            throw new RuntimeException(e);
        }
        return regionLocator;
    }

    public static byte[] convertRowKey(String rowKey, boolean isBinaryRowkey) {
        if (StringUtils.isBlank(rowKey)) {
            return HConstants.EMPTY_BYTE_ARRAY;
        } else {
            return HBaseHelper.stringToBytes(rowKey, isBinaryRowkey);
        }
    }

    private static byte[] stringToBytes(String rowKey, boolean isBinaryRowKey) {
        if (isBinaryRowKey) {
            return Bytes.toBytesBinary(rowKey);
        } else {
            return Bytes.toBytes(rowKey);
        }
    }

    public static void closeConnection(Connection hConnection) {
        try {
            if (null != hConnection) {
                hConnection.close();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void closeAdmin(Admin admin) {
        try {
            if (null != admin) {
                admin.close();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void closeRegionLocator(RegionLocator regionLocator) {
        try {
            if (null != regionLocator) {
                regionLocator.close();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void checkHbaseTable(Admin admin, TableName table) throws IOException {
        if (!admin.tableExists(table)) {
            throw new IllegalArgumentException(
                    String.format("hbase table %s does not exist.", table));
        }
        if (!admin.isTableAvailable(table)) {
            throw new RuntimeException(String.format("hbase table %s is not available.", table));
        }
        if (admin.isTableDisabled(table)) {
            throw new RuntimeException(String.format("hbase table %s is disabled", table));
        }
    }

    public static void closeBufferedMutator(BufferedMutator bufferedMutator) {
        try {
            if (null != bufferedMutator) {
                bufferedMutator.close();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static Function<Object, byte[]> createKeyEncoder(DataType fieldType) {
        LogicalTypeRoot typeRoot = fieldType.getLogicalType().getTypeRoot();
        // ordered by type root definition
        switch (typeRoot) {
            case CHAR:
            case VARCHAR:
                return data -> Bytes.toBytes(data.toString());
            case BOOLEAN:
                return data -> Bytes.toBytes((boolean) data);
            case BINARY:
            case VARBINARY:
                return data -> (byte[]) data;
            case DECIMAL:
                return data -> Bytes.toBytes(((DecimalData) data).toBigDecimal());
            case TINYINT:
                return data -> new byte[] {(byte) data};
            case SMALLINT:
                return data -> Bytes.toBytes((short) data);
            case INTEGER:
            case DATE:
            case INTERVAL_YEAR_MONTH:
            case TIME_WITHOUT_TIME_ZONE:
                return data -> Bytes.toBytes((int) data);
            case BIGINT:
            case INTERVAL_DAY_TIME:
                return data -> Bytes.toBytes((long) data);
            case FLOAT:
                return data -> Bytes.toBytes((float) data);
            case DOUBLE:
                return data -> Bytes.toBytes((double) data);
            case TIMESTAMP_WITHOUT_TIME_ZONE:
            case TIMESTAMP_WITH_LOCAL_TIME_ZONE:
                return data -> Bytes.toBytes(((TimestampData) data).getMillisecond());
            default:
                throw new UnsupportedOperationException("Unsupported type: " + typeRoot);
        }
    }
}
