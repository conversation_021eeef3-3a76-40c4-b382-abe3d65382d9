package com.dtstack.chunjun.connector.hbase.table.deserializer;

import com.dtstack.chunjun.connector.hbase.converter.HBaseSqlConverter;
import com.dtstack.chunjun.connector.hbase.source.deserializer.HBaseDeserializationSchema;

import org.apache.flink.annotation.Internal;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.table.data.RowData;

import org.apache.hadoop.hbase.client.Result;

@Internal
public class HBaseRowDataDeserializationSchema implements HBaseDeserializationSchema<RowData> {

    private final TypeInformation<RowData> typeInfo;
    private final HBaseSqlConverter hbaseSqlConverter;

    public HBaseRowDataDeserializationSchema(
            TypeInformation<RowData> typeInfo, HBaseSqlConverter hbaseSqlConverter) {
        this.typeInfo = typeInfo;
        this.hbaseSqlConverter = hbaseSqlConverter;
    }

    @Override
    public RowData deserialize(Result result) {
        return hbaseSqlConverter.toInternal(result);
    }

    @Override
    public TypeInformation<RowData> getProducedType() {
        return typeInfo;
    }
}
