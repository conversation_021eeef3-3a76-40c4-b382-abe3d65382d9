package com.dtstack.chunjun.connector.hbase.sink.writer;

import com.dtstack.chunjun.connector.hbase.table.serializer.HBaseRowDataSerializationSchema;
import com.dtstack.chunjun.connector.hbase.util.HBaseHelper;
import com.dtstack.security.SecurityContext;

import org.apache.flink.annotation.Internal;
import org.apache.flink.table.data.RowData;
import org.apache.flink.util.concurrent.ExecutorThreadFactory;

import org.apache.commons.lang.Validate;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.TableNotFoundException;
import org.apache.hadoop.hbase.client.BufferedMutator;
import org.apache.hadoop.hbase.client.BufferedMutatorParams;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.Mutation;
import org.apache.hadoop.hbase.client.RetriesExhaustedWithDetailsException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/** Hbase Client for sink data to hbase. */
@Internal
public class MutatorWriter implements BufferedMutator.ExceptionListener {

    private static final Logger LOG = LoggerFactory.getLogger(MutatorWriter.class);

    private final String hTableName;
    private final Map<String, Object> hbaseConfig;
    private final long bufferFlushMaxSizeInBytes;
    private final int bufferFlushMaxMutations;
    private final long bufferFlushIntervalMillis;
    private final HBaseRowDataSerializationSchema serializationSchema;

    private transient ScheduledExecutorService executor;
    private transient ScheduledFuture scheduledFuture;
    private transient AtomicLong numPendingRequests;

    private transient Connection connection;
    private transient DeduplicatedMutator mutator;
    private SecurityContext securityContext;

    private transient volatile boolean closed = false;

    private final AtomicReference<Throwable> failureThrowable = new AtomicReference<Throwable>();

    public MutatorWriter(
            String hTableName,
            Map<String, Object> hbaseConfig,
            long bufferFlushMaxSizeInBytes,
            int bufferFlushMaxMutations,
            long bufferFlushIntervalMillis,
            HBaseRowDataSerializationSchema serializationSchema,
            SecurityContext securityContext) {
        this.hTableName = hTableName;
        this.hbaseConfig = hbaseConfig;
        this.bufferFlushMaxSizeInBytes = bufferFlushMaxSizeInBytes;
        this.bufferFlushMaxMutations = bufferFlushMaxMutations;
        this.bufferFlushIntervalMillis = bufferFlushIntervalMillis;
        this.serializationSchema = serializationSchema;
        this.securityContext = securityContext;
    }

    protected void open() throws IOException {
        LOG.info("Opening Hbase mutator writer for {}", hTableName);
        Validate.isTrue(
                hbaseConfig != null && hbaseConfig.size() != 0, "hbaseConfig should not be null");

        numPendingRequests = new AtomicLong(0);

        try {
            this.connection = HBaseHelper.initConnection(securityContext, hbaseConfig);

            TableName tableName = TableName.valueOf(hTableName);
            if (!this.connection.getAdmin().tableExists(tableName)) {
                throw new TableNotFoundException(hTableName);
            }

            BufferedMutatorParams params = new BufferedMutatorParams(tableName).listener(this);
            if (bufferFlushMaxSizeInBytes > 0) {
                params.writeBufferSize(bufferFlushMaxSizeInBytes);
            }

            mutator =
                    new DeduplicatedMutator(
                            bufferFlushMaxMutations, connection.getBufferedMutator(params));

            if (bufferFlushIntervalMillis > 0 && bufferFlushMaxMutations != 1) {
                this.executor =
                        Executors.newScheduledThreadPool(
                                1, new ExecutorThreadFactory("hbase-upsert-sink-flusher"));
                this.scheduledFuture =
                        this.executor.scheduleWithFixedDelay(
                                () -> {
                                    if (closed) {
                                        return;
                                    }
                                    try {
                                        LOG.info("Scheduling mutator writer for {}", hTableName);
                                        flush();
                                    } catch (IOException e) {
                                        throw new RuntimeException(e);
                                    }
                                },
                                bufferFlushIntervalMillis,
                                bufferFlushIntervalMillis,
                                TimeUnit.SECONDS);
            }
        } catch (TableNotFoundException tableNotFoundException) {
            LOG.error("The table " + hTableName + " not found ", tableNotFoundException);
            throw new RuntimeException(
                    "HBase table '" + hTableName + "' not found.", tableNotFoundException);
        } catch (IOException ioe) {
            LOG.error("Exception while creating connection to HBase.", ioe);
            throw new RuntimeException("Cannot create connection to HBase.", ioe);
        }
        LOG.info("Opened Hbase mutator writer for {}", hTableName);
    }

    protected void writeRecord(RowData record) throws Exception {
        checkErrorAndRethrow();

        mutator.mutate(serializationSchema.serialize(record));
        if (bufferFlushMaxSizeInBytes > 0
                && numPendingRequests.incrementAndGet() >= bufferFlushMaxMutations) {
            flush();
        } else if (bufferFlushMaxMutations == 0 && bufferFlushIntervalMillis == 0) {
            flush();
        }
    }

    protected void flush() throws IOException {
        mutator.flush();
        numPendingRequests.set(0);
        checkErrorAndRethrow();
    }

    protected void checkErrorAndRethrow() {
        Throwable cause = failureThrowable.get();
        if (cause != null) {
            throw new RuntimeException("An error occurred in HbaseWriter.", cause);
        }
    }

    protected void close() {
        LOG.info("Closing Hbase mutator writer for {}", hTableName);
        closed = true;
        if (mutator != null) {
            try {
                mutator.close();
            } catch (IOException e) {
                LOG.warn("Exception occurs while closing HBase BufferedMutator.", e);
            }
            this.mutator = null;
        }
        if (connection != null) {
            try {
                HBaseHelper.closeConnection(connection);
            } catch (Exception e) {
                LOG.warn("Exception occurs while closing HBase connection.", e);
            }
            this.connection = null;
        }

        if (scheduledFuture != null) {
            scheduledFuture.cancel(false);
            if (executor != null) {
                executor.shutdownNow();
            }
        }
    }

    @Override
    public void onException(RetriesExhaustedWithDetailsException exception, BufferedMutator mutator)
            throws RetriesExhaustedWithDetailsException {
        // fail the sink and skip the rest of the items
        // if the failure handler decides to throw an exception
        failureThrowable.compareAndSet(null, exception);
    }

    /**
     * Thread-safe class, grouped mutations by rows and keep the latest mutation. For more info, see
     * <a href="https://issues.apache.org/jira/browse/HBASE-8626">HBASE-8626</a>.
     */
    private static class DeduplicatedMutator {

        private final BufferedMutator mutator;
        private final Map<ByteBuffer, Mutation> mutations;

        DeduplicatedMutator(int size, BufferedMutator mutator) {
            this.mutator = mutator;
            this.mutations = new HashMap<>(size);
        }

        synchronized void mutate(Mutation current) {
            ByteBuffer key = ByteBuffer.wrap(current.getRow());
            Mutation old = mutations.get(key);
            if (old == null || current.getTimeStamp() >= old.getTimeStamp()) {
                mutations.put(key, current);
            } else {
                LOG.info("Deprecated mutator writer for {}", current.getRow());
            }
        }

        synchronized void flush() throws IOException {
            if (mutations.isEmpty()) {
                return;
            }
            mutator.mutate(new ArrayList<>(mutations.values()));
            mutator.flush();
            LOG.info("Mutated {} rows", mutations.size());
            mutations.clear();
        }

        synchronized void close() throws IOException {
            mutator.mutate(new ArrayList<>(mutations.values()));
            mutator.close();
            mutations.clear();
        }
    }
}
