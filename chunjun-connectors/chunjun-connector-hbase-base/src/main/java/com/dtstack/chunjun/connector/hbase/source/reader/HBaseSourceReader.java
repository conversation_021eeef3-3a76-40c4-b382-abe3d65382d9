package com.dtstack.chunjun.connector.hbase.source.reader;

import com.dtstack.chunjun.connector.hbase.source.split.HBaseSourceSplit;
import com.dtstack.chunjun.connector.hbase.source.split.HBaseSourceSplitState;

import org.apache.flink.annotation.Internal;
import org.apache.flink.api.connector.source.SourceReaderContext;
import org.apache.flink.connector.base.source.reader.RecordEmitter;
import org.apache.flink.connector.base.source.reader.SingleThreadMultiplexSourceReaderBase;
import org.apache.flink.connector.base.source.reader.splitreader.SplitReader;

import org.apache.hadoop.hbase.client.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.function.Supplier;

@Internal
public class HBaseSourceReader<OUT>
        extends SingleThreadMultiplexSourceReaderBase<
                Result, OUT, HBaseSourceSplit, HBaseSourceSplitState> {

    private static final Logger LOG = LoggerFactory.getLogger(HBaseSourceReader.class);

    public HBaseSourceReader(
            Supplier<SplitReader<Result, HBaseSourceSplit>> splitReaderSupplier,
            RecordEmitter<Result, OUT, HBaseSourceSplitState> recordEmitter,
            SourceReaderContext context) {
        super(splitReaderSupplier, recordEmitter, context.getConfiguration(), context);
    }

    @Override
    public void start() {
        if (getNumberOfCurrentlyAssignedSplits() == 0) {
            LOG.info("Currently assigned splits size is 0, Send split request at start.");
            context.sendSplitRequest();
        }
    }

    @Override
    protected void onSplitFinished(Map<String, HBaseSourceSplitState> finishedSplitIds) {
        for (HBaseSourceSplitState splitState : finishedSplitIds.values()) {
            HBaseSourceSplit sourceSplit = splitState.toHbaseSourceSplit();
            LOG.info("Split {} is finished.", sourceSplit);
        }
        context.sendSplitRequest();
    }

    @Override
    protected HBaseSourceSplitState initializedState(HBaseSourceSplit split) {
        return new HBaseSourceSplitState(split);
    }

    @Override
    protected HBaseSourceSplit toSplitType(String splitId, HBaseSourceSplitState splitState) {
        return splitState.toHbaseSourceSplit();
    }
}
