package com.dtstack.chunjun.connector.inceptorhyperbase.sink.executor;

import com.dtstack.chunjun.connector.hbase.util.HBaseHelper;
import com.dtstack.chunjun.converter.AbstractRowConverter;

import org.apache.flink.table.data.RowData;

import org.apache.hadoop.hbase.client.BufferedMutator;
import org.apache.hadoop.hbase.client.Put;

import java.sql.ResultSet;
import java.util.function.Function;

public class BatchPutExecutor implements BatchMutationExecutor<RowData> {

    private final AbstractRowConverter rowConverter;
    private final BufferedMutator bufferedMutator;
    private final Function<RowData, byte[]> keySerializer;

    public BatchPutExecutor(
            AbstractRowConverter rowConverter,
            BufferedMutator bufferedMutator,
            Function<RowData, byte[]> keySerializer) {
        this.rowConverter = rowConverter;
        this.bufferedMutator = bufferedMutator;
        this.keySerializer = keySerializer;
    }

    @Override
    public void addToBatch(RowData record) throws Exception {
        Put put = new Put(keySerializer.apply(record));
        rowConverter.toExternal(record, put);
        bufferedMutator.mutate(put);
    }

    @Override
    public void executeBatch() throws Exception {
        bufferedMutator.flush();
    }

    @Override
    public void writeSingleRecord(RowData record) throws Exception {
        Put put = new Put(keySerializer.apply(record));
        rowConverter.toExternal(record, put);
        bufferedMutator.mutate(put);
        bufferedMutator.flush();
    }

    @Override
    public ResultSet executeQuery(RowData record) {
        return null;
    }

    @Override
    public void close() throws Exception {
        HBaseHelper.closeBufferedMutator(bufferedMutator);
    }

    @Override
    public void clearBatch() throws Exception {}
}
