package com.dtstack.chunjun.connector.inceptorhyperbase.conf;

import com.dtstack.chunjun.connector.hbase.conf.HBaseConf;
import com.dtstack.chunjun.connector.jdbc.conf.SinkConnectionConf;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HyperbaseConf extends HBaseConf {
    Map<String, String> rowKeyExpress = new HashMap<>();
    List<SinkConnectionConf> connection;

    public Map<String, String> getRowKeyExpress() {
        return rowKeyExpress;
    }

    public void setRowKeyExpress(Map<String, String> rowKeyExpress) {
        this.rowKeyExpress = rowKeyExpress;
    }

    public List<SinkConnectionConf> getConnection() {
        return connection;
    }

    public void setConnection(List<SinkConnectionConf> connection) {
        this.connection = connection;
    }

    @Override
    public String toString() {
        return "HyperbaseConf{"
                + "rowKeyExpress="
                + rowKeyExpress
                + ", connection="
                + connection
                + '}';
    }
}
