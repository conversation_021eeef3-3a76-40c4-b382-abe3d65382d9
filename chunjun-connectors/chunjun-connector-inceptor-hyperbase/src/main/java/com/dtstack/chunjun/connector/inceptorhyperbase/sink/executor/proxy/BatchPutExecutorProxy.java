package com.dtstack.chunjun.connector.inceptorhyperbase.sink.executor.proxy;

import com.dtstack.chunjun.connector.inceptorhyperbase.conf.HyperbaseConf;
import com.dtstack.chunjun.connector.inceptorhyperbase.conf.HyperbaseTableMeta;
import com.dtstack.chunjun.connector.inceptorhyperbase.sink.executor.MutationExecutorUtil;
import com.dtstack.chunjun.element.ColumnRowData;

import org.apache.flink.table.data.RowData;

import org.apache.hadoop.hbase.client.BufferedMutator;

import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.ExecutionException;

public class BatchPutExecutorProxy extends MutationCachedExecutorProxy<RowData> {

    private final HyperbaseTableMeta hyperbaseTableMeta;

    private final BufferedMutator bufferedMutator;

    public BatchPutExecutorProxy(
            HyperbaseConf hyperbaseConf,
            HyperbaseTableMeta hyperbaseTableMeta,
            BufferedMutator bufferedMutator,
            int cacheSize,
            long cacheDurationMin,
            boolean cacheIsExpire) {
        super(hyperbaseConf, cacheSize, cacheDurationMin, cacheIsExpire);
        this.hyperbaseTableMeta = hyperbaseTableMeta;
        this.bufferedMutator = bufferedMutator;
    }

    @Override
    protected RowData switchExecutorFromCache(RowData record) throws ExecutionException {
        String[] headers = ((ColumnRowData) record).getHeaders();

        String cacheKey = String.join("_", Objects.requireNonNull(headers));
        currentExecutor =
                cache.get(
                        cacheKey,
                        () ->
                                MutationExecutorUtil.buildPutExecutor(
                                        hyperbaseConf,
                                        bufferedMutator,
                                        Arrays.asList(headers),
                                        hyperbaseTableMeta));

        return record;
    }
}
