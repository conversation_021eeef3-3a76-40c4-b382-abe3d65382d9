package com.dtstack.chunjun.connector.inceptorhyperbase.sink.executor;

import com.dtstack.chunjun.conf.FieldConf;
import com.dtstack.chunjun.conf.TypeConfig;
import com.dtstack.chunjun.connector.hbase.FunctionParser;
import com.dtstack.chunjun.connector.hbase.FunctionTree;
import com.dtstack.chunjun.connector.inceptor.hyperbase.type.HyperbaseStringType;
import com.dtstack.chunjun.connector.inceptor.hyperbase.type.InceptorHyperbaseRawTypeMapper;
import com.dtstack.chunjun.connector.inceptorhyperbase.conf.HyperbaseConf;
import com.dtstack.chunjun.connector.inceptorhyperbase.conf.HyperbaseTableMeta;
import com.dtstack.chunjun.connector.inceptorhyperbase.converter.DirectHyperbaseSqlConvert;
import com.dtstack.chunjun.connector.inceptorhyperbase.converter.DirectHyperbaseSyncConvert;
import com.dtstack.chunjun.connector.inceptorhyperbase.sink.executor.buffer.PutDeleteBufferReduceExecutor;
import com.dtstack.chunjun.connector.inceptorhyperbase.sink.executor.buffer.PutDeleteMergedBufferReduceExecutor;
import com.dtstack.chunjun.connector.inceptorhyperbase.sink.executor.proxy.BatchPutExecutorProxy;
import com.dtstack.chunjun.connector.inceptorhyperbase.sink.executor.proxy.ExpressExecutorProxy;
import com.dtstack.chunjun.connector.inceptorhyperbase.util.HyperbaseUtil;
import com.dtstack.chunjun.connector.inceptorhyperbase.util.InceptorConnectionProvider;
import com.dtstack.chunjun.element.ColumnRowData;
import com.dtstack.chunjun.enums.ColumnType;
import com.dtstack.chunjun.util.TableUtil;

import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.table.types.logical.TimestampType;
import org.apache.flink.table.types.logical.VarCharType;
import org.apache.flink.types.RowKind;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.client.BufferedMutator;
import org.apache.hadoop.hyperbase.datatype.BigintDataType;
import org.apache.hadoop.hyperbase.datatype.DateDataType;
import org.apache.hadoop.hyperbase.datatype.DoubleDataType;
import org.apache.hadoop.hyperbase.datatype.FloatDataType;
import org.apache.hadoop.hyperbase.datatype.IntegerDataType;
import org.apache.hadoop.hyperbase.datatype.SmallintDataType;
import org.apache.hadoop.hyperbase.datatype.StringHDataType;
import org.apache.hadoop.hyperbase.datatype.TimestampDataType;
import org.apache.hadoop.hyperbase.datatype.TinyintDataType;
import org.apache.hadoop.hyperbase.datatype.VarcharHDataType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class MutationExecutorUtil {
    private static final Logger LOG = LoggerFactory.getLogger(MutationExecutorUtil.class);

    public static BatchMutationExecutor<RowData> buildExecutor(
            InceptorConnectionProvider provider,
            org.apache.hadoop.hbase.client.Connection hConnection,
            Configuration hConfiguration,
            HyperbaseConf hyperbaseConf,
            String inceptorTableName,
            RowType schemaRowType,
            long writeBufferSize,
            boolean useAbstractColumn) {
        try {
            List<String> selectedColumnList =
                    hyperbaseConf.getColumn().stream()
                            .map(FieldConf::getName)
                            .collect(Collectors.toList());
            HyperbaseTableMeta tableMetaData =
                    getTableMetaData(
                            provider.getConnection(),
                            inceptorTableName,
                            hyperbaseConf.getEncoding());
            RowType rowType = getRowType(selectedColumnList, tableMetaData);
            String rowKeyExpress = hyperbaseConf.getRowKeyExpress().get(inceptorTableName);
            checkRowKeyTypeValid(tableMetaData.getRowKeyType(), rowKeyExpress, inceptorTableName);
            int keyIndex =
                    getKeyIndex(rowKeyExpress, selectedColumnList, tableMetaData.getRowKeyName());
            String hyperbaseTableName = inceptorTableName.replace(".", ":");

            BufferedMutator bufferedMutator =
                    HyperbaseUtil.createBufferedMutator(
                            hConnection, hConfiguration, hyperbaseTableName, writeBufferSize);

            BatchMutationExecutor<RowData> putExecutor =
                    buildPutExecutor(
                            hyperbaseConf,
                            bufferedMutator,
                            selectedColumnList,
                            tableMetaData,
                            rowType,
                            keyIndex,
                            useAbstractColumn);

            Function<RowData, String> keyExtractor =
                    getKeyExtractor(
                            inceptorTableName,
                            tableMetaData.getRowKeyName(),
                            tableMetaData.getRowKeyType(),
                            rowKeyExpress,
                            selectedColumnList,
                            rowType,
                            false);
            if (useAbstractColumn) {
                if (StringUtils.isNotBlank(rowKeyExpress)) {
                    return new ExpressExecutorProxy(putExecutor, keyExtractor, rowType, true);
                } else {
                    return putExecutor;
                }
            } else {
                checkRowTypeValid(schemaRowType, rowType);
                BatchDeleteExecutor deleteExecutor =
                        new BatchDeleteExecutor(
                                bufferedMutator,
                                getKeySerializer(
                                        tableMetaData.getRowKeyType(),
                                        tableMetaData.getTableName(),
                                        keyIndex,
                                        true));
                if (StringUtils.isNotBlank(rowKeyExpress)) {
                    PutDeleteBufferReduceExecutor putDeleteBufferReduceExecutor =
                            new PutDeleteBufferReduceExecutor(
                                    true, bufferedMutator, putExecutor, deleteExecutor, null);
                    return new ExpressExecutorProxy(
                            putDeleteBufferReduceExecutor, keyExtractor, rowType, false);
                } else {
                    return new PutDeleteBufferReduceExecutor(
                            true, bufferedMutator, putExecutor, deleteExecutor, keyExtractor);
                }
            }
        } catch (Exception e) {
            throw new FlinkxRuntimeException("build mutation executor error", e);
        }
    }

    private static void checkRowTypeValid(RowType schemaRowType, RowType rowType) {
        int count = rowType.getFieldCount();
        for (int i = 0; i < count; i++) {
            if (schemaRowType.getTypeAt(i).getTypeRoot() != rowType.getTypeAt(i).getTypeRoot()) {
                throw new IllegalArgumentException(
                        String.format(
                                "The schemaRowType[%s] does not match the metadataType[%s]. Check the ddl statement",
                                schemaRowType, rowType));
            }
        }
    }

    // Used to build bufferKey, SyncJob-with-rowExpression treats the result as the rowKey
    public static Function<RowData, String> getKeyExtractor(
            String hyperbaseTableName,
            String rowKeyName,
            TypeConfig rowKeyTypeConfig,
            String rowKeyExpress,
            List<String> selectedColumnNameList,
            RowType rowType,
            boolean useMergedExecutor) {
        if (StringUtils.isBlank(rowKeyExpress)) {
            return getNoExpressKeyExtractor(
                    hyperbaseTableName,
                    selectedColumnNameList,
                    rowKeyName,
                    rowKeyTypeConfig,
                    useMergedExecutor);
        }
        FunctionTree functionTree = FunctionParser.parse(rowKeyExpress);
        List<String> rowKeyColumnList = FunctionParser.parseRowKeyCol(rowKeyExpress);
        List<Integer> rowKeyColumnIndexList = new ArrayList<>(rowKeyColumnList.size());
        for (String rowKeyColumn : rowKeyColumnList) {
            int index = selectedColumnNameList.indexOf(rowKeyColumn);
            if (index == -1) {
                throw new RuntimeException(
                        "Can not get row key column from columns:" + rowKeyColumn);
            }
            rowKeyColumnIndexList.add(index);
        }

        if (useMergedExecutor) {
            return getMergedExpressKeyExtractor(
                    functionTree,
                    rowKeyColumnList,
                    rowKeyColumnIndexList,
                    selectedColumnNameList,
                    hyperbaseTableName,
                    rowKeyExpress);
        } else {
            return getExpressKeyExtractor(
                    functionTree, rowKeyColumnList, rowKeyColumnIndexList, rowType);
        }
    }

    private static Function<RowData, String> getExpressKeyExtractor(
            FunctionTree functionTree,
            List<String> rowKeyColumnList,
            List<Integer> rowKeyColumnIndexList,
            RowType rowType) {
        List<RowData.FieldGetter> fieldGetterList = new ArrayList<>(rowKeyColumnList.size());
        for (int i = 0; i < rowKeyColumnIndexList.size(); i++) {
            Integer index = rowKeyColumnIndexList.get(i);
            fieldGetterList.add(RowData.createFieldGetter(rowType.getTypeAt(index), index));
        }
        return rowData -> {
            Map<String, Object> nameValueMap = new HashMap<>(rowKeyColumnList.size());
            for (int i = 0; i < fieldGetterList.size(); i++) {
                Object fieldOrNull = fieldGetterList.get(i).getFieldOrNull(rowData);
                nameValueMap.put(rowKeyColumnList.get(i), String.valueOf(fieldOrNull));
            }
            try {
                return functionTree.evaluate(nameValueMap);
            } catch (Exception e) {
                throw new FlinkxRuntimeException(e);
            }
        };
    }

    public static Function<RowData, String> getMergedExpressKeyExtractor(
            FunctionTree functionTree,
            List<String> rowKeyColumnList,
            List<Integer> rowKeyColumnIndexList,
            List<String> selectedColumnNameList,
            String hyperbaseTableName,
            String rowKeyExpress) {
        return rowData -> {
            Map<String, Object> nameValueMap = new HashMap<>(rowKeyColumnList.size());
            if (rowData.getRowKind() == RowKind.INSERT) {
                for (Integer index : rowKeyColumnIndexList) {
                    nameValueMap.put(
                            selectedColumnNameList.get(index),
                            ((ColumnRowData) rowData).getField(index).getData());
                }
            } else {
                Map<String, Integer> headerInfo = ((ColumnRowData) rowData).getHeaderInfo();
                for (String rowKeyColumn : rowKeyColumnList) {
                    Integer index = headerInfo.get(rowKeyColumn);
                    if (index == null) {
                        throw new RuntimeException(
                                String.format(
                                        "Can not get row key column from data[%s] ,table[%s] ,express[%s]",
                                        rowData, hyperbaseTableName, rowKeyExpress));
                    }
                    nameValueMap.put(
                            rowKeyColumn, ((ColumnRowData) rowData).getField(index).getData());
                }
            }
            try {
                return functionTree.evaluate(nameValueMap);
            } catch (Exception e) {
                throw new FlinkxRuntimeException(e);
            }
        };
    }

    public static Function<RowData, String> getNoExpressKeyExtractor(
            String hyperbaseTableName,
            List<String> selectedColumnNameList,
            String rowKeyName,
            TypeConfig rowKeyTypeConfig,
            boolean useMergedExecutor) {
        LogicalType keyType =
                InceptorHyperbaseRawTypeMapper.apply(rowKeyTypeConfig).getLogicalType();
        if (useMergedExecutor) {
            return row -> {
                int keyIndex = ((ColumnRowData) row).getHeaderInfo().getOrDefault(rowKeyName, -1);
                if (keyIndex == -1) {
                    throw new FlinkxRuntimeException(
                            String.format(
                                    "rowKey is not found in the data and rowKeyExpress is not set, please check the configuration.\n"
                                            + "table[%s],data[%s]",
                                    hyperbaseTableName, row));
                }
                Object fieldOrNull =
                        buildRowKeyFiledGetter(keyType, keyIndex, hyperbaseTableName)
                                .getFieldOrNull(row);
                return String.valueOf(fieldOrNull);
            };
        } else {
            int keyIndex = selectedColumnNameList.indexOf(rowKeyName);
            if (keyIndex == -1) {
                throw new FlinkxRuntimeException(
                        String.format(
                                "rowKeyColumn[%s] is not found in the table[%s] meta columnList:%s",
                                rowKeyName, hyperbaseTableName, selectedColumnNameList));
            }
            RowData.FieldGetter fieldGetter =
                    buildRowKeyFiledGetter(keyType, keyIndex, hyperbaseTableName);
            return row -> {
                Object fieldOrNull = fieldGetter.getFieldOrNull(row);
                return String.valueOf(fieldOrNull);
            };
        }
    }

    public static boolean changeFlag(RowKind rowKind) {
        switch (rowKind) {
            case INSERT:
            case UPDATE_AFTER:
                return true;
            case DELETE:
            case UPDATE_BEFORE:
                return false;
            default:
                throw new UnsupportedOperationException(
                        String.format(
                                "Unknown row kind, the supported row kinds is: INSERT, UPDATE_BEFORE, UPDATE_AFTER,"
                                        + " DELETE, but get: %s.",
                                rowKind));
        }
    }

    public static BatchMutationExecutor<RowData> buildMergedPutDeleteExecutor(
            Connection inceptorConnection,
            org.apache.hadoop.hbase.client.Connection hConnection,
            Configuration hConfiguration,
            HyperbaseConf hyperbaseConf,
            String inceptorTableName,
            long writeBufferSize,
            int cacheSize,
            long cacheDurationMin,
            boolean cacheIsExpire) {
        try {
            String hyperbaseTableName = inceptorTableName.replace(".", ":");
            BufferedMutator bufferedMutator =
                    HyperbaseUtil.createBufferedMutator(
                            hConnection, hConfiguration, hyperbaseTableName, writeBufferSize);

            HyperbaseTableMeta tableMetaData =
                    getTableMetaData(
                            inceptorConnection, inceptorTableName, hyperbaseConf.getEncoding());

            BatchDeleteExecutor deleteExecutor =
                    new BatchDeleteExecutor(
                            bufferedMutator,
                            getKeySerializer(
                                    tableMetaData.getRowKeyType(),
                                    tableMetaData.getTableName(),
                                    -1,
                                    true));

            BatchPutExecutorProxy putExecutorProxy =
                    new BatchPutExecutorProxy(
                            hyperbaseConf,
                            tableMetaData,
                            bufferedMutator,
                            cacheSize,
                            cacheDurationMin,
                            cacheIsExpire);

            Function<RowData, String> keyExtractor =
                    getKeyExtractor(
                            inceptorTableName,
                            tableMetaData.getRowKeyName(),
                            tableMetaData.getRowKeyType(),
                            hyperbaseConf.getRowKeyExpress().get(inceptorTableName),
                            // This parameter is consistent with the order of ColumnRowData$header
                            tableMetaData.getFullColumnName().stream()
                                    .sorted()
                                    .collect(Collectors.toList()),
                            null,
                            true);
            return new PutDeleteMergedBufferReduceExecutor(
                    true, bufferedMutator, putExecutorProxy, deleteExecutor, keyExtractor);
        } catch (Exception e) {
            throw new FlinkxRuntimeException(e);
        }
    }

    public static BatchMutationExecutor<RowData> buildPutExecutor(
            HyperbaseConf hyperbaseConf,
            BufferedMutator bufferedMutator,
            List<String> selectedColumnList,
            HyperbaseTableMeta tableMetaData) {
        return buildPutExecutor(
                hyperbaseConf,
                bufferedMutator,
                selectedColumnList,
                tableMetaData,
                getRowType(selectedColumnList, tableMetaData),
                selectedColumnList.size(),
                true);
    }

    public static BatchMutationExecutor<RowData> buildPutExecutor(
            HyperbaseConf hyperbaseConf,
            BufferedMutator bufferedMutator,
            List<String> selectedColumnList,
            HyperbaseTableMeta tableMetaData,
            RowType rowType,
            int keyIndex,
            boolean useAbstractBaseColumn) {
        byte[][][] familyAndQualifier = new byte[selectedColumnList.size()][][];
        Map<String, byte[][]> familyQualifierMap = tableMetaData.getFamilyQualifierMap();
        for (int i = 0; i < selectedColumnList.size(); i++) {
            familyAndQualifier[i] = familyQualifierMap.get(selectedColumnList.get(i));
        }

        Function<RowData, byte[]> keySerializer =
                getKeySerializer(
                        tableMetaData.getRowKeyType(),
                        tableMetaData.getTableName(),
                        keyIndex,
                        false);

        DirectHyperbaseSyncConvert directHyperbaseSyncConvert;
        if (useAbstractBaseColumn) {
            directHyperbaseSyncConvert =
                    new DirectHyperbaseSyncConvert(
                            tableMetaData.getRowKeyName(),
                            rowType,
                            hyperbaseConf,
                            familyAndQualifier,
                            tableMetaData.getVirtualFamilyQualifier());
        } else {
            directHyperbaseSyncConvert =
                    new DirectHyperbaseSqlConvert(
                            tableMetaData.getRowKeyName(),
                            rowType,
                            hyperbaseConf,
                            familyAndQualifier,
                            tableMetaData.getVirtualFamilyQualifier());
        }

        return new BatchPutExecutor(directHyperbaseSyncConvert, bufferedMutator, keySerializer);
    }

    public static HyperbaseTableMeta getTableMetaData(
            Connection inceptorConnection, String hyperbaseTableName, String encoding)
            throws SQLException {
        int i = 1;
        while (true) {
            try {
                return getTableMetaDataInternal(inceptorConnection, hyperbaseTableName, encoding);
            } catch (Exception e) {
                if (i++ >= 3 || inceptorConnection.isValid(10)) {
                    throw new FlinkxRuntimeException(e);
                } else {
                    LOG.warn("getTableMetaData failed, retrying times{}...", i, e);
                }
            }
        }
    }

    private static HyperbaseTableMeta getTableMetaDataInternal(
            Connection inceptorConnection, String hyperbaseTableName, String encoding) {
        try {
            String sql = String.format("select * from %s where 1=0", hyperbaseTableName);
            ResultSet selectResultSet = inceptorConnection.createStatement().executeQuery(sql);
            ResultSetMetaData metaData = selectResultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            List<String> columnNameList = new ArrayList<>(columnCount);
            List<TypeConfig> columnTypeList = new ArrayList<>(columnCount);
            for (int i = 1; i <= columnCount; i++) {
                columnNameList.add(metaData.getColumnName(i));
                TypeConfig typeConfig = TypeConfig.fromString(metaData.getColumnTypeName(i));
                typeConfig.setPrecision(metaData.getPrecision(i));
                typeConfig.setScale(metaData.getScale(i));
                columnTypeList.add(
                        new TypeConfig(
                                metaData.getColumnTypeName(i),
                                metaData.getPrecision(i),
                                metaData.getScale(i)));
            }
            String descSql = String.format("describe formatted %s", hyperbaseTableName);
            ResultSet resultSet = inceptorConnection.createStatement().executeQuery(descSql);
            Map<String, String> tableProperties = new HashMap<>();
            while (resultSet.next()) {
                String key = resultSet.getString(1);
                String value = resultSet.getString(2);
                if (key == null || value == null) {
                    continue;
                }
                tableProperties.put(key.trim(), value.trim());
            }
            return new HyperbaseTableMeta(
                    hyperbaseTableName, columnNameList, columnTypeList, tableProperties, encoding);

        } catch (SQLException e) {
            throw new FlinkxRuntimeException(e);
        }
    }

    private static RowType getRowType(
            List<String> selectedColumnList, HyperbaseTableMeta tableMetaData) {
        List<TypeConfig> selectedTypeList = new ArrayList<>(selectedColumnList.size());
        for (String columnName : selectedColumnList) {
            int columnIndex = tableMetaData.getFullColumnName().indexOf(columnName);
            if (columnIndex == -1) {
                throw new FlinkxRuntimeException(
                        String.format(
                                "Can not find column[%s] in table[%s]",
                                columnName, tableMetaData.getTableName()));
            }
            selectedTypeList.add(tableMetaData.getFullColumnType().get(columnIndex));
        }
        return TableUtil.createRowType(
                selectedColumnList, selectedTypeList, InceptorHyperbaseRawTypeMapper::apply);
    }

    private static int getKeyIndex(
            String rowKeyExpress, List<String> selectedColumnList, String rowKeyName) {
        if (StringUtils.isNotBlank(rowKeyExpress)) {
            return selectedColumnList.size();
        }
        return selectedColumnList.indexOf(rowKeyName);
    }

    private static void checkRowKeyTypeValid(
            TypeConfig rowKeyType, String rowKeyExpress, String tableName) {
        if (StringUtils.isNotBlank(rowKeyExpress)
                && !ColumnType.isStringType(rowKeyType.getType())) {
            throw new IllegalArgumentException(
                    String.format(
                            "When rowKeyExpress[%s] is not blank, the rowKey type of table [%s] must be stringType but [%s]",
                            rowKeyExpress, tableName, rowKeyType.getType()));
        }
    }

    private static Function<RowData, byte[]> getKeySerializer(
            TypeConfig rowKeyTypeConfig, String tableName, int keyIndex, boolean isDelete) {
        LogicalType keyType =
                InceptorHyperbaseRawTypeMapper.apply(rowKeyTypeConfig).getLogicalType();

        Function<RowData, byte[]> keySerializerInternal =
                getInternalKeySerializer(keyType, tableName, keyIndex, isDelete);
        // Just to print the error log
        return row -> {
            try {
                return keySerializerInternal.apply(row);
            } catch (Exception e) {
                throw new FlinkxRuntimeException(
                        String.format(
                                "Failed to serialize key[%s] of table[%s],rowKey type is [%s]",
                                row, tableName, keyType),
                        e);
            }
        };
    }

    private static Function<RowData, byte[]> getInternalKeySerializer(
            LogicalType keyType, String tableName, int keyIndex, boolean isDelete) {
        if (isDelete && keyIndex == -1) {
            return buildSyncDeleteKeySerializer(keyType, tableName);
        } else {
            return buildKeySerializer(keyType, tableName, keyIndex);
        }
    }

    private static Function<RowData, byte[]> buildKeySerializer(
            LogicalType type, String tableName, int keyIndex) {
        switch (type.getTypeRoot()) {
            case CHAR:
            case VARCHAR:
                if (type instanceof HyperbaseStringType) {
                    return row -> StringHDataType.encodeString(row.getString(keyIndex).toString());
                } else {
                    int length = ((VarCharType) type).getLength();
                    return row ->
                            VarcharHDataType.encodeVarchar(
                                    row.getString(keyIndex).toString(), length);
                }
            case BIGINT:
                return row -> BigintDataType.encodeLong(row.getLong(keyIndex));
            case INTEGER:
                return row -> IntegerDataType.encodeInteger(row.getInt(keyIndex));
            case SMALLINT:
                return row -> SmallintDataType.encodeShort(row.getShort(keyIndex));
            case TINYINT:
                return row -> TinyintDataType.encodeByte(row.getByte(keyIndex));
            case DOUBLE:
                return row -> DoubleDataType.encodeDouble(row.getDouble(keyIndex));
            case FLOAT:
                return row -> FloatDataType.encodeFloat(row.getFloat(keyIndex));
            case TIMESTAMP_WITHOUT_TIME_ZONE:
                int precision = ((TimestampType) type).getPrecision();
                if (precision == 0) {
                    return row ->
                            DateDataType.encodeDate(
                                    row.getTimestamp(keyIndex, precision).toTimestamp());
                } else {
                    return row ->
                            TimestampDataType.encodeTimestamp(
                                    row.getTimestamp(keyIndex, precision).toTimestamp());
                }
            default:
                throw new FlinkxRuntimeException(
                        String.format(
                                "Unsupported rowKey type[%s],tableName[%s]", type, tableName));
        }
    }

    private static Function<RowData, byte[]> buildSyncDeleteKeySerializer(
            LogicalType type, String tableName) {
        switch (type.getTypeRoot()) {
            case CHAR:
            case VARCHAR:
                if (type instanceof HyperbaseStringType) {
                    return row ->
                            StringHDataType.encodeString(
                                    row.getString(row.getArity() - 1).toString());
                } else {
                    int length = ((VarCharType) type).getLength();
                    return row ->
                            VarcharHDataType.encodeVarchar(
                                    row.getString(row.getArity() - 1).toString(), length);
                }
            case BIGINT:
                return row -> {
                    long l = Long.parseLong(row.getString(row.getArity() - 1).toString());
                    return BigintDataType.encodeLong(l);
                };
            case INTEGER:
                return row -> {
                    int v = Integer.parseInt(row.getString(row.getArity() - 1).toString());
                    return IntegerDataType.encodeInteger(v);
                };
            case SMALLINT:
                return row -> {
                    short v = Short.parseShort(row.getString(row.getArity() - 1).toString());
                    return SmallintDataType.encodeShort(v);
                };
            case TINYINT:
                return row -> {
                    byte v = Byte.parseByte(row.getString(row.getArity() - 1).toString());
                    return TinyintDataType.encodeByte(v);
                };
            case DOUBLE:
                return row -> {
                    double v = Double.parseDouble(row.getString(row.getArity() - 1).toString());
                    return DoubleDataType.encodeDouble(v);
                };
            case FLOAT:
                return row -> {
                    float v = Float.parseFloat(row.getString(row.getArity() - 1).toString());
                    return FloatDataType.encodeFloat(v);
                };
            case DATE:
                return row -> {
                    String val = row.getString(row.getArity() - 1).toString();
                    if (StringUtils.isNumeric(val)) {
                        int v = Integer.parseInt(val);
                        return DateDataType.encodeDate(new Date(v));
                    } else {
                        return DateDataType.encodeDate(new Date(val));
                    }
                };
            case TIMESTAMP_WITHOUT_TIME_ZONE:
                return row -> {
                    String val = row.getString(row.getArity() - 1).toString();
                    if (StringUtils.isNumeric(val)) {
                        long v = Long.parseLong(val);
                        return TimestampDataType.encodeTimestamp(new Timestamp(v));
                    } else {
                        return TimestampDataType.encodeTimestamp(Timestamp.valueOf(val));
                    }
                };
            default:
                throw new FlinkxRuntimeException(
                        String.format(
                                "Unsupported rowKey type[%s],tableName[%s]", type, tableName));
        }
    }

    private static RowData.FieldGetter buildRowKeyFiledGetter(
            LogicalType type, int keyIndex, String tableName) {
        switch (type.getTypeRoot()) {
            case CHAR:
            case VARCHAR:
                return row -> row.getString(keyIndex);
            case BIGINT:
                return row -> row.getLong(keyIndex);
            case INTEGER:
                return row -> row.getInt(keyIndex);
            case SMALLINT:
                return row -> row.getShort(keyIndex);
            case TINYINT:
                return row -> row.getByte(keyIndex);
            case DOUBLE:
                return row -> row.getDouble(keyIndex);
            case FLOAT:
                return row -> row.getFloat(keyIndex);
            case TIMESTAMP_WITHOUT_TIME_ZONE:
                int precision = ((TimestampType) type).getPrecision();
                return row -> row.getTimestamp(keyIndex, precision).toTimestamp();
            default:
                throw new FlinkxRuntimeException(
                        String.format(
                                "Unsupported rowKey type[%s],tableName[%s]", type, tableName));
        }
    }
}
