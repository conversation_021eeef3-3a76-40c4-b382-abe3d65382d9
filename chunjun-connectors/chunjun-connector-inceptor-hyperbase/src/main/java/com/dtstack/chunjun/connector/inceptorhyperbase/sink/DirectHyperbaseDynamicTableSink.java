package com.dtstack.chunjun.connector.inceptorhyperbase.sink;

import com.dtstack.chunjun.conf.FieldConf;
import com.dtstack.chunjun.connector.inceptorhyperbase.conf.HyperbaseConf;
import com.dtstack.chunjun.sink.DtOutputFormatSinkFunction;

import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.connector.ChangelogMode;
import org.apache.flink.table.connector.sink.DynamicTableSink;
import org.apache.flink.table.connector.sink.SinkFunctionProvider;
import org.apache.flink.table.types.logical.RowType;
import org.apache.flink.types.RowKind;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class DirectHyperbaseDynamicTableSink implements DynamicTableSink {

    private final HyperbaseConf hyperbaseConf;
    private final TableSchema tableSchema;

    public DirectHyperbaseDynamicTableSink(HyperbaseConf hyperbaseConf, TableSchema tableSchema) {
        this.hyperbaseConf = hyperbaseConf;
        this.tableSchema = tableSchema;
    }

    @Override
    public ChangelogMode getChangelogMode(ChangelogMode requestedMode) {
        return ChangelogMode.newBuilder()
                .addContainedKind(RowKind.INSERT)
                .addContainedKind(RowKind.DELETE)
                .addContainedKind(RowKind.UPDATE_AFTER)
                .build();
    }

    @Override
    public SinkRuntimeProvider getSinkRuntimeProvider(Context context) {
        // 通过该参数得到类型转换器，将数据库中的字段转成对应的类型
        final RowType rowType = (RowType) tableSchema.toRowDataType().getLogicalType();

        String[] fieldNames = tableSchema.getFieldNames();
        List<FieldConf> columnList = new ArrayList<>(fieldNames.length);
        for (int i = 0; i < fieldNames.length; i++) {
            String name = fieldNames[i];
            String type = rowType.getTypeAt(i).asSummaryString();
            FieldConf field = new FieldConf();
            field.setName(name);
            field.setType(type);
            field.setIndex(i);
            columnList.add(field);
        }
        hyperbaseConf.setColumn(columnList);

        DirectHyperbaseOutputFormatBuilder builder = new DirectHyperbaseOutputFormatBuilder();
        builder.setConfig(hyperbaseConf);
        builder.setJdbcOptions(hyperbaseConf.getConnection().get(0));
        builder.setUseAbstractBaseColumn(false);
        builder.setHbaseConfig(hyperbaseConf.getHbaseConfig());
        builder.setWriteBufferSize(hyperbaseConf.getWriteBufferSize());
        builder.setRowType(rowType);
        return SinkFunctionProvider.of(
                new DtOutputFormatSinkFunction(builder.finish()), hyperbaseConf.getParallelism());
    }

    @Override
    public DynamicTableSink copy() {
        return new DirectHyperbaseDynamicTableSink(hyperbaseConf, tableSchema);
    }

    @Override
    public String asSummaryString() {
        return "Inceptor:directHyperbase";
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof DirectHyperbaseDynamicTableSink)) {
            return false;
        }
        DirectHyperbaseDynamicTableSink that = (DirectHyperbaseDynamicTableSink) o;
        return Objects.equals(hyperbaseConf, that.hyperbaseConf)
                && Objects.equals(tableSchema, that.tableSchema);
    }

    @Override
    public int hashCode() {
        return Objects.hash(hyperbaseConf, tableSchema);
    }
}
