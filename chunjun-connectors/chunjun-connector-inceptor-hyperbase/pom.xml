<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.dtstack.chunjun</groupId>
		<artifactId>chunjun-connectors</artifactId>
		<version>2.0-SNAPSHOT</version>
	</parent>

	<artifactId>chunjun-connector-inceptor-hyperbase</artifactId>
	<name>ChunJun : Connectors : Inceptor Hyperbase</name>

	<properties>
		<maven.compiler.source>${target.java.version}</maven.compiler.source>
		<maven.compiler.target>${target.java.version}</maven.compiler.target>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<hbase.version>1.3.1-transwarp-6.2.2</hbase.version>
	</properties>
	<dependencies>
		<dependency>
			<groupId>com.dtstack.chunjun</groupId>
			<artifactId>chunjun-connector-hbase-base</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.hbase</groupId>
			<artifactId>hbase-common</artifactId>
			<version>${hbase.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.hbase</groupId>
			<artifactId>hbase-client</artifactId>
			<version>${hbase.version}</version>
		</dependency>
		<dependency>
			<groupId>com.dtstack.chunjun</groupId>
			<artifactId>chunjun-connector-inceptor-base</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>io.transwarp</groupId>
					<artifactId>inceptor-driver</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.hive</groupId>
					<artifactId>inceptor-serde</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.parquet</groupId>
					<artifactId>parquet-hadoop</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.hive</groupId>
					<artifactId>inceptor-streaming</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.htrace</groupId>
			<artifactId>htrace-core</artifactId>
			<version>3.1.0-incubating</version>
		</dependency>
		<dependency>
			<groupId>org.apache.hbase</groupId>
			<artifactId>hbase-protocol</artifactId>
			<version>${hbase.version}</version>
		</dependency>
		<dependency>
			<groupId>com.yammer.metrics</groupId>
			<artifactId>metrics-core</artifactId>
			<version>2.2.0</version>
		</dependency>
		<dependency>
			<groupId>io.transwarp</groupId>
			<artifactId>federation-utils-guardian</artifactId>
			<version>3.1.3</version>
		</dependency>

		<dependency>
			<groupId>com.github.stephenc.findbugs</groupId>
			<artifactId>findbugs-annotations</artifactId>
			<version>1.3.9-1</version>
		</dependency>

		<dependency>
			<groupId>io.transwarp</groupId>
			<artifactId>guardian-client-guardian</artifactId>
			<version>3.1.3</version>
		</dependency>

		<dependency>
			<groupId>io.transwarp</groupId>
			<artifactId>guardian-common-guardian</artifactId>
			<version>3.1.3</version>
		</dependency>

		<dependency>
			<groupId>io.transwarp</groupId>
			<artifactId>guardian-sasl</artifactId>
			<version>transwarp-6.2.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-common</artifactId>
			<version>2.7.2-transwarp-6.2.2</version>
		</dependency>
		<dependency>
			<groupId>io.transwarp</groupId>
			<artifactId>inceptor-driver</artifactId>
			<version>8.0.2</version>
		</dependency>

	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<createDependencyReducedPom>false</createDependencyReducedPom>
							<artifactSet>
								<excludes>
									<exclude>com.data-artisans:*</exclude>
									<exclude>org.scala-lang:*</exclude>
									<exclude>io.netty:*</exclude>
								</excludes>
							</artifactSet>
							<filters>
								<filter>
									<artifact>*:*</artifact>
									<excludes>
										<exclude>META-INF/*.SF</exclude>
										<exclude>META-INF/*.DSA</exclude>
										<exclude>META-INF/*.RSA</exclude>
										<!--<exclude>org/apache/flink/**</exclude>-->
									</excludes>
								</filter>
							</filters>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-resources</id>
						<!-- here the phase you need -->
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<tasks>
								<copy todir="${basedir}/../../${dist.dir}/connector/inceptorhyperbase"
									  file="${basedir}/target/${project.artifactId}-${project.version}.jar"/>
								<!--suppress UnresolvedMavenProperty -->
								<move
									file="${basedir}/../../${dist.dir}/connector/inceptorhyperbase/${project.artifactId}-${project.version}.jar"
									tofile="${basedir}/../../${dist.dir}/connector/inceptorhyperbase/${project.artifactId}-${git.branch}.jar"/>
								<delete>
									<!--suppress UnresolvedMavenProperty -->
									<fileset dir="${basedir}/../../${dist.dir}/connector/inceptorhyperbase/"
											 includes="${project.artifactId}-*.jar"
											 excludes="${project.artifactId}-${git.branch}.jar"/>
								</delete>
							</tasks>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
