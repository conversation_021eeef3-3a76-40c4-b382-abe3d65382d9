<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>chunjun-connectors</artifactId>
		<groupId>com.dtstack.chunjun</groupId>
		<version>2.0-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>chunjun-connector-hudi</artifactId>
	<name>ChunJun : Connectors : Hudi</name>

	<properties>
		<maven.compiler.source>${target.java.version}</maven.compiler.source>
		<maven.compiler.target>${target.java.version}</maven.compiler.target>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<hudi.version>0.13.0</hudi.version>
		<hive.version>2.1.0</hive.version>
		<!--<hadoop.version>3.2.1</hadoop.version>-->
		<!--<guava.version>27.0-jre</guava.version>-->
		<flink.avro.version>1.11.1</flink.avro.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>${guava.version}</version>
		</dependency>
		<!-- Avro -->
		<dependency>
			<groupId>org.apache.avro</groupId>
			<artifactId>avro</artifactId>
			<version>${flink.avro.version}</version>
			<scope>compile</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-common</artifactId>
			<version>${hadoop.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>gson</artifactId>
					<groupId>com.google.code.gson</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-cli</artifactId>
					<groupId>commons-cli</groupId>
				</exclusion>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-hdfs</artifactId>
			<version>${hadoop.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>commons-cli</artifactId>
					<groupId>commons-cli</groupId>
				</exclusion>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-mapreduce-client-core</artifactId>
			<version>${hadoop.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.hive</groupId>
			<artifactId>hive-exec</artifactId>
			<version>${hive.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>commons-logging</artifactId>
					<groupId>commons-logging</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-cli</artifactId>
					<groupId>commons-cli</groupId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.derby</groupId>
					<artifactId>derby</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>groovy-all</artifactId>
					<groupId>org.codehaus.groovy</groupId>
				</exclusion>
				<exclusion>
					<artifactId>gson</artifactId>
					<groupId>com.google.code.gson</groupId>
				</exclusion>
				<exclusion>
					<artifactId>parquet-hadoop-bundle</artifactId>
					<groupId>org.apache.parquet</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hudi</groupId>
			<artifactId>hudi-flink1.16-bundle</artifactId>
			<version>0.14.0-dt-4</version>
		</dependency>

		<dependency>
			<groupId>com.github.luben</groupId>
			<artifactId>zstd-jni</artifactId>
			<version>1.4.9-1</version>
		</dependency>

	</dependencies>


	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<artifactSet>
								<excludes>
									<exclude>org.slf4j:slf4j-api</exclude>
									<exclude>log4j:log4j</exclude>
									<exclude>ch.qos.logback:*</exclude>
								</excludes>
							</artifactSet>
							<filters>
								<filter>
									<artifact>*:*</artifact>
									<excludes>
										<exclude>META-INF/*.SF</exclude>
										<exclude>META-INF/*.DSA</exclude>
										<exclude>META-INF/*.RSA</exclude>
									</excludes>
								</filter>
								<filter>
									<artifact>org.apache.hive:hive-exec</artifact>
									<excludes>
										<exclude>org/apache/parquet/**</exclude>
									</excludes>
								</filter>
								<filter>
									<artifact>org.apache.hudi:hudi-flink1.16-bundle</artifact>
									<excludes>
										<exclude>META-INF/services/org.apache.flink.table.factories.Factory</exclude>
									</excludes>
								</filter>
							</filters>
							<relocations>
								<relocation>
									<pattern>com.google.common</pattern>
									<shadedPattern>${connector.shading.prefix}.hudi.com.google.common</shadedPattern>
								</relocation>
								<relocation>
									<pattern>org.apache.flink.formats.parquet.vector</pattern>
									<shadedPattern>${connector.shading.prefix}.hudi.org.apache.flink.formats.parquet.vector</shadedPattern>
								</relocation>
								<relocation>
									<pattern>org.apache.flink.formats.parquet.vector</pattern>
									<shadedPattern>${connector.shading.prefix}.hudi.org.apache.flink.formats.parquet.vector</shadedPattern>
								</relocation>
								<relocation>
									<pattern>org.apache.flink.formats.parquet.utils.SerializableConfiguration</pattern>
									<shadedPattern>${connector.shading.prefix}.hudi.org.apache.flink.formats.parquet.utils.SerializableConfiguration</shadedPattern>
								</relocation>
								<!-- 防止FileInputFormat被AppClassLoader加载，因为lib下没有com.github.luben:zstd-jni的依赖 -->
								<relocation>
									<pattern>org.apache.flink.api.common.io.FileInputFormat</pattern>
									<shadedPattern>${connector.shading.prefix}.hudi.org.apache.flink.api.common.io.FileInputFormat</shadedPattern>
								</relocation>
								<relocation>
									<pattern>org.apache.flink.api.common.io.compression.ZStandardInputStreamFactory</pattern>
									<shadedPattern>${connector.shading.prefix}.hudi.org.apache.flink.api.common.io.compression.ZStandardInputStreamFactory</shadedPattern>
								</relocation>
							</relocations>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-resources</id>
						<!-- here the phase you need -->
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<tasks>
								<copy todir="${basedir}/../../${dist.dir}/connector/hudi"
									  file="${basedir}/target/${project.artifactId}-${project.version}.jar"/>
								<move file="${basedir}/../../${dist.dir}/connector/hudi/${project.artifactId}-${project.version}.jar"
									  tofile="${basedir}/../../${dist.dir}/connector/hudi/${project.artifactId}-${git.branch}.jar"/>
								<delete>
									<fileset dir="${basedir}/../../${dist.dir}/connector/hudi/"
											 includes="${project.artifactId}-*.jar"
											 excludes="${project.artifactId}-${git.branch}.jar"/>
								</delete>
							</tasks>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
