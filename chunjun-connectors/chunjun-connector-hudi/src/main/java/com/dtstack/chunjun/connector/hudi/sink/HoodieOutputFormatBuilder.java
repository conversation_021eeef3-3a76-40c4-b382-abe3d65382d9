package com.dtstack.chunjun.connector.hudi.sink;

import com.dtstack.chunjun.connector.hudi.converter.HoodieSyncConverter;
import com.dtstack.chunjun.converter.AbstractRowConverter;
import com.dtstack.chunjun.sink.format.BaseRichOutputFormatBuilder;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSink;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;

import org.apache.hudi.common.model.HoodieRecord;
import org.apache.hudi.common.model.WriteOperationType;
import org.apache.hudi.configuration.ChunjunHoodieConfig;
import org.apache.hudi.configuration.FlinkOptions;
import org.apache.hudi.configuration.OptionsInference;
import org.apache.hudi.configuration.OptionsResolver;
import org.apache.hudi.sink.utils.Pipelines;

public class HoodieOutputFormatBuilder extends BaseRichOutputFormatBuilder {

    private final Configuration conf;
    private final RowType rowType;
    private final ChunjunHoodieConfig chunjunHoodieConfig;
    private AbstractRowConverter<RowData, RowData, RowData, LogicalType> converter;

    public HoodieOutputFormatBuilder(
            ChunjunHoodieConfig chunjunHoodieConfig, Configuration conf, RowType rowType) {
        this.conf = conf;
        this.rowType = rowType;
        this.chunjunHoodieConfig = chunjunHoodieConfig;
        converter = new HoodieSyncConverter(rowType, chunjunHoodieConfig);
    }

    public DataStreamSink<?> probeSink(DataStream<RowData> dataStream) {
        // 类型转换
        // dataStream = dataStream.map(new HudiTypeConvertMapFunction(converter));
        // setup configuration
        long ckpTimeout =
                dataStream.getExecutionEnvironment().getCheckpointConfig().getCheckpointTimeout();
        conf.setLong(FlinkOptions.WRITE_COMMIT_ACK_TIMEOUT, ckpTimeout);
        // set up default parallelism
        OptionsInference.setupSinkTasks(conf, dataStream.getExecutionConfig().getParallelism());

        // bulk_insert mode
        final String writeOperation = this.conf.get(FlinkOptions.OPERATION);
        if (WriteOperationType.fromValue(writeOperation) == WriteOperationType.BULK_INSERT) {
            return Pipelines.bulkInsert(conf, rowType, dataStream);
        }

        // insert mode
        //  1. table.type = COPY_ON_WRITE
        //  2. write.operation = insert
        //  3. write.insert.cluster = false
        //  4. clustering.schedule.enabled = true
        if (OptionsResolver.isAppendMode(conf)) {
            DataStream<Object> pipeline = Pipelines.append(conf, rowType, dataStream, true);
            // 1. write.operation = insert
            // 2. clustering.async.enabled = true
            if (OptionsResolver.needsAsyncClustering(conf)) {
                return Pipelines.cluster(conf, rowType, pipeline);
            } else {
                return Pipelines.dummySink(pipeline);
            }
        }

        DataStream<Object> pipeline;
        // bootstrap
        final DataStream<HoodieRecord> hoodieRecordDataStream =
                Pipelines.bootstrap(conf, rowType, dataStream, true, false);
        // write pipeline
        pipeline = Pipelines.hoodieStreamWrite(conf, hoodieRecordDataStream);
        // compaction
        if (OptionsResolver.needsAsyncCompaction(conf)) {
            conf.setBoolean(FlinkOptions.COMPACTION_ASYNC_ENABLED, false);
            return Pipelines.compact(conf, pipeline);
        } else {
            return Pipelines.clean(conf, pipeline);
        }
    }

    @Override
    protected void checkFormat() {}
}
