package com.dtstack.chunjun.connector.hudi.source;

import com.dtstack.chunjun.conf.FieldConf;
import com.dtstack.chunjun.conf.SyncConf;
import com.dtstack.chunjun.conf.TypeConfig;
import com.dtstack.chunjun.connector.hudi.converter.HoodieRawTypeMapper;
import com.dtstack.chunjun.converter.RawTypeMapper;
import com.dtstack.chunjun.source.SourceFactory;
import com.dtstack.chunjun.common.util.GsonUtil;
import com.dtstack.chunjun.util.TableUtil;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.ValidationException;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.hudi.common.table.HoodieTableConfig;
import org.apache.hudi.configuration.ChunjunHoodieConfig;
import org.apache.hudi.configuration.FlinkOptions;
import org.apache.hudi.configuration.OptionsResolver;
import org.apache.hudi.exception.HoodieValidationException;
import org.apache.hudi.keygen.ComplexAvroKeyGenerator;
import org.apache.hudi.keygen.NonpartitionedAvroKeyGenerator;
import org.apache.hudi.keygen.TimestampBasedAvroKeyGenerator;
import org.apache.hudi.keygen.constant.KeyGeneratorOptions;
import org.apache.hudi.util.AvroSchemaConverter;
import org.apache.hudi.util.DataTypeUtils;
import org.apache.hudi.util.StreamerUtil;

import java.io.IOException;
import java.security.PrivilegedAction;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.apache.hudi.util.ChunjunHoodieUtil.getDefaultHdpConfig;
import static org.apache.hudi.util.ChunjunHoodieUtil.getUgi;

public class HoodieSourceFactory extends SourceFactory {

    private final ChunjunHoodieConfig chunjunHoodieConfig;
    private final Configuration conf;
    List<String> columnNameList = new ArrayList<>();
    List<TypeConfig> columnTypeList = new ArrayList<>();
    List<DataType> columnDataTypeList = new ArrayList<>();
    UserGroupInformation ugi;

    public HoodieSourceFactory(SyncConf syncConf, StreamExecutionEnvironment env)
            throws IOException {
        super(syncConf, env);
        Map<String, Object> parameter = syncConf.getReader().getParameter();
        chunjunHoodieConfig =
                GsonUtil.GSON.fromJson(GsonUtil.GSON.toJson(parameter), ChunjunHoodieConfig.class);
        if (chunjunHoodieConfig.getHadoopConfig() == null) {
            chunjunHoodieConfig.setHadoopConfig(getDefaultHdpConfig());
        }

        List<FieldConf> fieldList = syncConf.getReader().getFieldList();
        chunjunHoodieConfig.setColumn(fieldList);
        for (FieldConf fieldConf : fieldList) {
            if (StringUtils.isBlank(fieldConf.getValue())) {
                columnNameList.add(fieldConf.getName());
                columnTypeList.add(fieldConf.getScriptType());
                columnDataTypeList.add(getRawTypeMapper().apply(fieldConf.getScriptType()));
            }
        }
        conf = ChunjunHoodieConfig.toConfiguration(syncConf.getReader().getParameter());
        super.initFlinkxCommonConf(chunjunHoodieConfig);
        ugi = getUgi(chunjunHoodieConfig.getHadoopConfig());
    }

    @Override
    public RawTypeMapper getRawTypeMapper() {
        return HoodieRawTypeMapper::apply;
    }

    @Override
    public DataStream<RowData> createSource() {
        RowType rowType =
                TableUtil.createRowType(columnNameList, columnTypeList, getRawTypeMapper());
        String path =
                conf.getOptional(FlinkOptions.PATH)
                        .orElseThrow(
                                () ->
                                        new ValidationException(
                                                "Option [path] should not be empty."));
        String defaultPartitionName = conf.getString(FlinkOptions.PARTITION_DEFAULT_NAME);
        List<String> partitionKeyList =
                Arrays.stream(conf.getString(FlinkOptions.PARTITION_PATH_FIELD).split(","))
                        .collect(Collectors.toList());

        return ugi.doAs(
                (PrivilegedAction<DataStream<RowData>>)
                        () -> {
                            setupTableOptions();
                            setupConfOptions(rowType);

                            HoodieInputFormatBuilder builder =
                                    new HoodieInputFormatBuilder(
                                            chunjunHoodieConfig,
                                            isSyncJob,
                                            columnNameList,
                                            columnDataTypeList,
                                            rowType,
                                            new Path(path),
                                            partitionKeyList,
                                            defaultPartitionName,
                                            conf);
                            return builder.probeInputFormat(env, getTypeInformation());
                        });
    }

    private void setupTableOptions() {
        org.apache.hadoop.conf.Configuration hadoopConfiguration =
                new org.apache.hadoop.conf.Configuration();
        chunjunHoodieConfig.getHadoopConfig().entrySet().stream()
                .filter(entry -> entry.getValue() instanceof String && !entry.getKey().equals(""))
                .forEach(
                        entry ->
                                hadoopConfiguration.set(
                                        entry.getKey(), String.valueOf(entry.getValue())));
        String basePath = conf.getString(FlinkOptions.PATH);
        StreamerUtil.getTableConfig(basePath, hadoopConfiguration)
                .ifPresent(
                        tableConfig -> {
                            if (tableConfig.contains(HoodieTableConfig.RECORDKEY_FIELDS)
                                    && !conf.contains(FlinkOptions.RECORD_KEY_FIELD)) {
                                conf.setString(
                                        FlinkOptions.RECORD_KEY_FIELD,
                                        tableConfig.getString(HoodieTableConfig.RECORDKEY_FIELDS));
                            }
                            if (tableConfig.contains(HoodieTableConfig.PRECOMBINE_FIELD)
                                    && !conf.contains(FlinkOptions.PRECOMBINE_FIELD)) {
                                conf.setString(
                                        FlinkOptions.PRECOMBINE_FIELD,
                                        tableConfig.getString(HoodieTableConfig.PRECOMBINE_FIELD));
                            }
                            if (tableConfig.contains(
                                            HoodieTableConfig.HIVE_STYLE_PARTITIONING_ENABLE)
                                    && !conf.contains(FlinkOptions.HIVE_STYLE_PARTITIONING)) {
                                conf.setBoolean(
                                        FlinkOptions.HIVE_STYLE_PARTITIONING,
                                        tableConfig.getBoolean(
                                                HoodieTableConfig.HIVE_STYLE_PARTITIONING_ENABLE));
                            }
                            if (tableConfig.contains(HoodieTableConfig.TYPE)
                                    && !conf.contains(FlinkOptions.TABLE_TYPE)) {
                                conf.setString(
                                        FlinkOptions.TABLE_TYPE,
                                        tableConfig.getString(HoodieTableConfig.TYPE));
                            }
                            if (tableConfig.contains(HoodieTableConfig.PAYLOAD_CLASS_NAME)
                                    && !conf.contains(FlinkOptions.PAYLOAD_CLASS_NAME)) {
                                conf.setString(
                                        FlinkOptions.PAYLOAD_CLASS_NAME,
                                        tableConfig.getString(
                                                HoodieTableConfig.PAYLOAD_CLASS_NAME));
                            }
                        });
    }

    private void setupConfOptions(RowType rowType) {
        // hoodie key about options
        setupHoodieKeyOptions(conf);
        // compaction options
        setupCompactionOptions(conf);
        // read options
        setupReadOptions(conf);
        // write options
        setupWriteOptions(conf);
        // infer avro schema from physical DDL schema
        inferAvroSchema(conf, rowType);
    }

    /**
     * Sets up the hoodie key options (e.g. record key and partition key) from the table definition.
     */
    private void setupHoodieKeyOptions(Configuration conf) {

        // tweak the key gen class if possible
        final String[] partitions = conf.getString(FlinkOptions.PARTITION_PATH_FIELD).split(",");
        final String[] pks = conf.getString(FlinkOptions.RECORD_KEY_FIELD).split(",");
        if (partitions.length == 1) {
            final String partitionField = partitions[0];
            if (partitionField.isEmpty()) {
                conf.setString(
                        FlinkOptions.KEYGEN_CLASS_NAME,
                        NonpartitionedAvroKeyGenerator.class.getName());
                LOG.info(
                        "Table option [{}] is reset to {} because this is a non-partitioned table",
                        FlinkOptions.KEYGEN_CLASS_NAME.key(),
                        NonpartitionedAvroKeyGenerator.class.getName());
                return;
            }
            int i = 0;
            for (; i < columnNameList.size(); i++) {
                if (columnNameList.get(i).equals(partitionField)) {
                    break;
                }
            }
            if (i >= columnNameList.size()) {
                throw new HoodieValidationException("Field " + partitionField + " does not exist");
            }
            DataType partitionFieldType = getRawTypeMapper().apply(columnTypeList.get(i));
            if (pks.length <= 1 && DataTypeUtils.isDatetimeType(partitionFieldType)) {
                // timestamp based key gen only supports simple primary key
                setupTimestampKeygenOptions(conf, partitionFieldType);
                return;
            }
        }
        boolean complexHoodieKey = pks.length > 1 || partitions.length > 1;
        if (complexHoodieKey
                && FlinkOptions.isDefaultValueDefined(conf, FlinkOptions.KEYGEN_CLASS_NAME)) {
            conf.setString(FlinkOptions.KEYGEN_CLASS_NAME, ComplexAvroKeyGenerator.class.getName());
            LOG.info(
                    "Table option [{}] is reset to {} because record key or partition path has two or more fields",
                    FlinkOptions.KEYGEN_CLASS_NAME.key(),
                    ComplexAvroKeyGenerator.class.getName());
        }
    }

    /**
     * Sets up the keygen options when the partition path is datetime type.
     *
     * <p>The UTC timezone is used as default.
     */
    private static void setupTimestampKeygenOptions(Configuration conf, DataType fieldType) {
        if (conf.contains(FlinkOptions.KEYGEN_CLASS_NAME)) {
            // the keygen clazz has been set up explicitly, skipping
            return;
        }

        conf.setString(
                FlinkOptions.KEYGEN_CLASS_NAME, TimestampBasedAvroKeyGenerator.class.getName());
        LOG.info(
                "Table option [{}] is reset to {} because datetime partitioning turns on",
                FlinkOptions.KEYGEN_CLASS_NAME.key(),
                TimestampBasedAvroKeyGenerator.class.getName());
        if (DataTypeUtils.isTimestampType(fieldType)) {
            int precision = DataTypeUtils.precision(fieldType.getLogicalType());
            if (precision == 0) {
                // seconds
                conf.setString(
                        KeyGeneratorOptions.Config.TIMESTAMP_TYPE_FIELD_PROP,
                        TimestampBasedAvroKeyGenerator.TimestampType.UNIX_TIMESTAMP.name());
            } else if (precision == 3) {
                // milliseconds
                conf.setString(
                        KeyGeneratorOptions.Config.TIMESTAMP_TYPE_FIELD_PROP,
                        TimestampBasedAvroKeyGenerator.TimestampType.EPOCHMILLISECONDS.name());
            }
            String outputPartitionFormat =
                    conf.getOptional(FlinkOptions.PARTITION_FORMAT)
                            .orElse(FlinkOptions.PARTITION_FORMAT_HOUR);
            conf.setString(
                    KeyGeneratorOptions.Config.TIMESTAMP_OUTPUT_DATE_FORMAT_PROP,
                    outputPartitionFormat);
        } else {
            conf.setString(
                    KeyGeneratorOptions.Config.TIMESTAMP_TYPE_FIELD_PROP,
                    TimestampBasedAvroKeyGenerator.TimestampType.SCALAR.name());
            conf.setString(KeyGeneratorOptions.Config.INPUT_TIME_UNIT, TimeUnit.DAYS.toString());

            String outputPartitionFormat =
                    conf.getOptional(FlinkOptions.PARTITION_FORMAT)
                            .orElse(FlinkOptions.PARTITION_FORMAT_DAY);
            conf.setString(
                    KeyGeneratorOptions.Config.TIMESTAMP_OUTPUT_DATE_FORMAT_PROP,
                    outputPartitionFormat);
            // the option is actually useless, it only works for validation
            conf.setString(
                    KeyGeneratorOptions.Config.TIMESTAMP_INPUT_DATE_FORMAT_PROP,
                    FlinkOptions.PARTITION_FORMAT_DAY);
        }
        conf.setString(KeyGeneratorOptions.Config.TIMESTAMP_OUTPUT_TIMEZONE_FORMAT_PROP, "UTC");
    }

    /** Sets up the compaction options from the table definition. */
    private void setupCompactionOptions(Configuration conf) {
        int commitsToRetain = conf.getInteger(FlinkOptions.CLEAN_RETAIN_COMMITS);
        int minCommitsToKeep = conf.getInteger(FlinkOptions.ARCHIVE_MIN_COMMITS);
        if (commitsToRetain >= minCommitsToKeep) {
            LOG.info(
                    "Table option [{}] is reset to {} to be greater than {}={},\n"
                            + "to avoid risk of missing data from few instants in incremental pull",
                    FlinkOptions.ARCHIVE_MIN_COMMITS.key(),
                    commitsToRetain + 10,
                    FlinkOptions.CLEAN_RETAIN_COMMITS.key(),
                    commitsToRetain);
            conf.setInteger(FlinkOptions.ARCHIVE_MIN_COMMITS, commitsToRetain + 10);
            conf.setInteger(FlinkOptions.ARCHIVE_MAX_COMMITS, commitsToRetain + 20);
        }
    }

    /** Sets up the read options from the table definition. */
    private void setupReadOptions(Configuration conf) {
        if (OptionsResolver.isIncrementalQuery(conf)) {
            conf.setString(FlinkOptions.QUERY_TYPE, FlinkOptions.QUERY_TYPE_INCREMENTAL);
        }
    }

    /** Sets up the write options from the table definition. */
    private static void setupWriteOptions(Configuration conf) {
        if (FlinkOptions.isDefaultValueDefined(conf, FlinkOptions.OPERATION)
                && OptionsResolver.isCowTable(conf)) {
            conf.setBoolean(FlinkOptions.PRE_COMBINE, true);
        }
    }

    /**
     * Inferences the deserialization Avro schema from the table schema (e.g. the DDL) if both
     * options {@link FlinkOptions#SOURCE_AVRO_SCHEMA_PATH} and {@link
     * FlinkOptions#SOURCE_AVRO_SCHEMA} are not specified.
     *
     * @param conf The configuration
     * @param rowType The specified table row type
     */
    private void inferAvroSchema(Configuration conf, LogicalType rowType) {
        if (!conf.getOptional(FlinkOptions.SOURCE_AVRO_SCHEMA_PATH).isPresent()
                && !conf.getOptional(FlinkOptions.SOURCE_AVRO_SCHEMA).isPresent()) {
            String inferredSchema = AvroSchemaConverter.convertToSchema(rowType).toString();
            conf.setString(FlinkOptions.SOURCE_AVRO_SCHEMA, inferredSchema);
        }
    }
}
