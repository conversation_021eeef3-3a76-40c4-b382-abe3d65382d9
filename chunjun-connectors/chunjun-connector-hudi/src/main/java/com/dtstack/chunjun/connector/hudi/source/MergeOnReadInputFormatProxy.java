package com.dtstack.chunjun.connector.hudi.source;

import com.dtstack.chunjun.common.util.FileSystemUtil;
import com.dtstack.chunjun.constants.Metrics;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.DataType;

import org.apache.commons.lang3.tuple.Pair;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.hudi.table.format.InternalSchemaManager;
import org.apache.hudi.table.format.mor.MergeOnReadInputFormat;
import org.apache.hudi.table.format.mor.MergeOnReadInputSplit;
import org.apache.hudi.table.format.mor.MergeOnReadTableState;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

public class MergeOnReadInputFormatProxy extends MergeOnReadInputFormat {

    private final Map<String, Object> kerberosConfig;

    private UserGroupInformation ugi;
    private ExecutorService ugiRefreshService;

    private String jobId;
    private int indexOfSubTask;

    public MergeOnReadInputFormatProxy(
            Configuration conf,
            MergeOnReadTableState tableState,
            List<DataType> fieldTypes,
            String defaultPartName,
            long limit,
            boolean emitDelete,
            InternalSchemaManager internalSchemaManager,
            Map<String, Object> kerberosConfig) {
        super(
                conf,
                tableState,
                fieldTypes,
                defaultPartName,
                limit,
                emitDelete,
                internalSchemaManager);
        this.kerberosConfig = kerberosConfig;
    }

    protected void initSecurity(String jobId, int indexOfSubTask) {
        try {
            Pair<UserGroupInformation, ExecutorService> pair =
                    FileSystemUtil.getRefreshUGI(
                            kerberosConfig, null, null, jobId, String.valueOf(indexOfSubTask));
            this.ugi = pair.getLeft();
            this.ugiRefreshService = pair.getRight();
        } catch (Exception e) {
            throw new FlinkxRuntimeException("failed to init security", e);
        }
    }

    @Override
    public MergeOnReadInputSplit[] createInputSplits(int minNumSplits) {
        try {
            UserGroupInformation ugi =
                    FileSystemUtil.getUGI(
                            kerberosConfig, null, null, jobId, String.valueOf(indexOfSubTask));
            return FileSystemUtil.runSecured(ugi, () -> super.createInputSplits(minNumSplits));
        } catch (Exception e) {
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public void open(MergeOnReadInputSplit split) throws IOException {
        try {
            Map<String, String> vars = getRuntimeContext().getMetricGroup().getAllVariables();
            if (vars != null) {
                jobId = vars.get(Metrics.JOB_NAME);
                indexOfSubTask = Integer.parseInt(vars.get(Metrics.SUBTASK_INDEX));
            }
            initSecurity(jobId, indexOfSubTask);
            FileSystemUtil.runSecured(
                    ugi,
                    () -> {
                        super.open(split);
                        return null;
                    });
        } catch (Exception e) {
            throw new IOException(e);
        }
    }

    @Override
    public boolean reachedEnd() throws IOException {
        try {
            return FileSystemUtil.runSecured(ugi, super::reachedEnd);
        } catch (Exception e) {
            throw new IOException(e);
        }
    }

    @Override
    public RowData nextRecord(RowData o) {
        try {
            return FileSystemUtil.runSecured(ugi, () -> super.nextRecord(o));
        } catch (Exception e) {
            throw new FlinkxRuntimeException(e);
        }
    }

    @Override
    public void close() throws IOException {
        try {
            FileSystemUtil.runSecured(
                    ugi,
                    () -> {
                        super.close();
                        if (ugiRefreshService != null) {
                            ugiRefreshService.shutdown();
                        }
                        return null;
                    });
        } catch (Exception e) {
            throw new IOException(e);
        }
    }
}
