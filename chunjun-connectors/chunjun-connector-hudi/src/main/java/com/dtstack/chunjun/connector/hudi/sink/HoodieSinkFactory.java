package com.dtstack.chunjun.connector.hudi.sink;

import com.dtstack.chunjun.conf.FieldConf;
import com.dtstack.chunjun.conf.SyncConf;
import com.dtstack.chunjun.conf.TypeConfig;
import com.dtstack.chunjun.connector.hudi.converter.HoodieRawTypeMapper;
import com.dtstack.chunjun.converter.RawTypeMapper;
import com.dtstack.chunjun.sink.SinkFactory;
import com.dtstack.chunjun.common.util.GsonUtil;
import com.dtstack.chunjun.util.TableUtil;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSink;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.logical.RowType;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.hudi.configuration.ChunjunHoodieConfig;
import org.apache.hudi.configuration.FlinkOptions;

import java.io.IOException;
import java.security.PrivilegedAction;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.apache.hudi.common.util.ValidationUtils.checkArgument;
import static org.apache.hudi.util.ChunjunHoodieUtil.getUgi;
import static org.apache.hudi.util.ChunjunHoodieUtil.sanityCheck;
import static org.apache.hudi.util.ChunjunHoodieUtil.setupConfOptions;
import static org.apache.hudi.util.ChunjunHoodieUtil.setupTableOptions;

public class HoodieSinkFactory extends SinkFactory {
    private final ChunjunHoodieConfig chunjunHoodieConfig;
    private final Configuration conf;
    List<String> columnNameList = new ArrayList<>();
    List<TypeConfig> columnTypeList = new ArrayList<>();
    List<DataType> columnDataTypeList = new ArrayList<>();
    UserGroupInformation ugi;

    public HoodieSinkFactory(SyncConf syncConf) throws IOException {
        super(syncConf);
        Map<String, Object> parameter = syncConf.getWriter().getParameter();
        chunjunHoodieConfig =
                GsonUtil.GSON.fromJson(GsonUtil.GSON.toJson(parameter), ChunjunHoodieConfig.class);

        List<FieldConf> fieldList = syncConf.getWriter().getFieldList();
        chunjunHoodieConfig.setColumn(fieldList);
        for (FieldConf fieldConf : fieldList) {
            if (StringUtils.isBlank(fieldConf.getValue())) {
                columnNameList.add(fieldConf.getName());
                columnTypeList.add(fieldConf.getScriptType());
                columnDataTypeList.add(getRawTypeMapper().apply(fieldConf.getScriptType()));
            }
        }
        conf = ChunjunHoodieConfig.toConfiguration(syncConf.getWriter().getParameter());
        super.initFlinkxCommonConf(chunjunHoodieConfig);

        ugi = getUgi(chunjunHoodieConfig.getHadoopConfig());
    }

    @Override
    public RawTypeMapper getRawTypeMapper() {
        return HoodieRawTypeMapper::apply;
    }

    @Override
    public DataStreamSink<?> createSink(DataStream<RowData> dataSet) {
        RowType rowType =
                TableUtil.createRowType(columnNameList, columnTypeList, getRawTypeMapper());
        checkArgument(
                !org.apache.hudi.common.util.StringUtils.isNullOrEmpty(
                        conf.getString(FlinkOptions.PATH)),
                "Option [path] should not be empty.");

        return ugi.doAs(
                (PrivilegedAction<? extends DataStreamSink<?>>)
                        () -> {
                            {
                                setupTableOptions(conf, chunjunHoodieConfig);
                                sanityCheck(conf, columnNameList);
                                setupConfOptions(conf, rowType, columnNameList);

                                HoodieOutputFormatBuilder builder =
                                        new HoodieOutputFormatBuilder(
                                                chunjunHoodieConfig, conf, rowType);
                                return builder.probeSink(dataSet);
                            }
                        });
    }
}
