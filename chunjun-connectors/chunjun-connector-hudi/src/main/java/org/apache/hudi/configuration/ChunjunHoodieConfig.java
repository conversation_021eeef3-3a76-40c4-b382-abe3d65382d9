package org.apache.hudi.configuration;

import com.dtstack.chunjun.common.util.JsonUtil;
import com.dtstack.chunjun.conf.FlinkxCommonConf;

import org.apache.flink.configuration.ConfigOption;
import org.apache.flink.configuration.Configuration;

import org.apache.hudi.client.clustering.plan.strategy.FlinkSizeBasedClusteringPlanStrategy;
import org.apache.hudi.common.model.EventTimeAvroPayload;
import org.apache.hudi.common.model.HoodieAvroRecordMerger;
import org.apache.hudi.common.model.HoodieRecordMerger;
import org.apache.hudi.common.util.PartitionPathEncodeUtils;
import org.apache.hudi.hive.MultiPartKeysValueExtractor;

import java.util.HashMap;
import java.util.Map;

public class ChunjunHoodieConfig extends FlinkxCommonConf {

    private static final Map<String, ConfigOption<?>> configOptionMap;

    // Chunjun Adapt
    Map<String, Object> hadoopConfig;

    // Base
    private String path;

    // Common Options
    private String hoodieDatabaseName;
    private String hoodieTableName;
    private String tableType = "COPY_ON_WRITE";
    private String preCombineFiled = "ts";
    private String payloadClass = EventTimeAvroPayload.class.getName();
    private String recordMergerImpls = HoodieAvroRecordMerger.class.getName();
    private String recordMergerStrategy = HoodieRecordMerger.DEFAULT_MERGER_STRATEGY_UUID;
    private String partitionDefaultName = PartitionPathEncodeUtils.DEFAULT_PARTITION_PATH;

    // Changelog Capture Options
    private boolean changelogEnable = false;
    private boolean cdcEnable = false;
    private String cdcSupplementalLoggingMode = "data_before_after";

    //  Metadata Table Options
    private boolean metadataEnable = true;
    private int metadataCompactionDeltaCommits = 10;

    // Index Options
    private String indexType = "FLINK_STATE";
    private boolean indexBootstrapEnabled = false;
    private double indexStateTtl = 0d;
    private boolean indexGlobalEnabled = true;
    private String indexPartitionRegex = ".*";

    // Read Options
    private int readTasks;
    private String sourceAvroSchemaPath;
    private String sourceAvroSchema;
    private String hoodieDatasourceQueryType = "snapshot";
    private String hoodieDatasourceMergeType = "payload_combine";
    private boolean readUtcTimezone = true;
    private boolean readStreamingEnable = false;
    private int readStreamingCheckInterval = 60; // default 1 min
    private boolean readStreamingSkipCompaction = false;
    private boolean readStreamingSkipClustering = false;
    private String readStartCommit;
    private String readEndCommit;
    private boolean readDataSkippingEnabled = false;

    // Write Options
    private boolean writeInsertCluster = false;
    private String writeOperation = "UPSERT";
    private boolean writePreCombine = false;
    private int writeRetryTimes = 3;
    private Long writeRetryIntervalMs = 2000L;
    private boolean writeIgnoreFailed = false;
    private String recordKeyField = "uuid";
    private String indexKeyField = "";
    private int bucketIndexNumBuckets = 4;
    private String partitionPathFiled = "";
    private boolean urlEncodePartitioning = false;
    private boolean hiveStylePartitioning = false;
    private String keygenClassName;
    private String keygenType = "simple";
    private String writePartitionFormat;
    private int writeIndexBootstrapTasks;
    private int writeBucketAssignTasks;
    private int writeTasks;
    private double writeTaskMaxSize = 1024d; // 1G
    private long writeRateLimit;
    private double writeBatchSize = 256d; // 256M
    private int writeLogBlockSize = 128; // 128M
    private long writeLogMaxSize = 1024; // 1G
    private int writeParquetMaxFileSize = 120;
    private int writeParquetPageSize = 1;
    private int writeMergeMaxMemory = 100; // 100M
    private long writeCommitAckTimeout = -1; // default at least once
    private boolean writeBulkInsertShuffleInput = true;
    private boolean writeBulkInsertSortInput = true;
    private boolean writeBulkInsertSortInputByRecordKey = false;
    private int writeSortMemory = 128; // 128M
    private int writeClientId = 128; // 128M

    // Compaction Options
    private boolean compactionScheduleEnabled = true;
    private boolean compactionAsyncEnabled = true;
    private int compactionTasks;
    private String compactionTriggerStrategy = "num_commits";
    private int compactionDeltaCommits = 5;
    private int compactionDeltaSeconds = 3600; // default 1 hour
    private int compactionTimeoutSeconds = 1200;
    private int compactionMaxMemory = 100; // 100M
    private long compactionTargetIo = 500 * 1024L; // 500G
    private boolean cleanAsyncEnabled = true;
    private String cleanPolicy = "KEEP_LATEST_COMMITS";
    private int cleanRetainCommits = 30;
    private int cleanRetainHours = 24;
    private int cleanRetainFileVersions = 5;
    private int archiveMaxCommits = 50;
    private int archiveMinCommits = 40;

    // Clustering Options
    private boolean clusteringScheduleEnabled = false;
    private boolean clusteringAsyncEnabled = false;
    private int clusteringDeltaCommits = 4;
    private int clusteringTasks;
    private int clusteringPlanStrategyDayBasedLookBackPartitions = 2;
    private int clusteringPlanStrategyDaybasedSkipfromlatestPartitions = 0;
    private String clusteringPlanStrategyClusterBeginPartition = "";
    private String clusteringPlanStrategyClusterEndPartition = "";
    private String clusteringPlanStrategyPartitionRegexPattern = "";
    private String clusteringPlanStrategyPartitionSelected = "";

    private String clusteringPlanStrategyClass =
            FlinkSizeBasedClusteringPlanStrategy.class.getName();
    private String clusteringPlanPartitionFilterMode = "NONE";
    private long clusteringPlanStrategyTargetFileMaxBytes = 1024 * 1024 * 1024L; // 1G
    private long clusteringPlanStrategySmallFileLimit = 600L;
    private int clusteringPlanStrategyDayBasedSkipFromLatestPartitions = 0;
    private String clusteringPlanStrategySortColumns = "";
    private int clusteringPlanStrategyMaxNumGroups = 30;

    // Hive Sync Options
    private boolean hiveSyncEnabled = false;
    private String hiveSyncDb = "default";
    private String hiveSyncTable = "unkonw";
    private String hiveSyncFileFormat = "PARQUET";
    private String hiveSyncMode = "HMS";
    private String hiveSyncUsername = "hive";
    private String hiveSyncPassword = "hive";
    private String hiveSyncJdbcUrl = "***************************";
    private String hiveSyncMetastoreUrl = "";
    private String hiveSyncPartitionFields = "";
    private String hiveSyncPartitionExtractorClass = MultiPartKeysValueExtractor.class.getName();
    private boolean hiveSyncAssumeDatePartitioning = false;
    private boolean hiveSyncUseJdbc = true;
    private boolean hiveSyncAutoCreateDb = true;
    private boolean hiveSyncIgnoreExceptions = false;
    private boolean hiveSyncSkipRoSuffix = false;
    private boolean hiveSyncSupportTimestamp = true;
    private String hiveSyncTableProperties;
    private String hiveSyncSerdeProperties;
    private String hiveSyncConfDir;
    private String hiveSyncTableStrategy = "ALL";

    public Map<String, Object> getHadoopConfig() {
        return hadoopConfig;
    }

    public void setHadoopConfig(Map<String, Object> hadoopConfig) {
        this.hadoopConfig = hadoopConfig;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getHoodieDatabaseName() {
        return hoodieDatabaseName;
    }

    public void setHoodieDatabaseName(String hoodieDatabaseName) {
        this.hoodieDatabaseName = hoodieDatabaseName;
    }

    public String getHoodieTableName() {
        return hoodieTableName;
    }

    public void setHoodieTableName(String hoodieTableName) {
        this.hoodieTableName = hoodieTableName;
    }

    public String getTableType() {
        return tableType;
    }

    public void setTableType(String tableType) {
        this.tableType = tableType;
    }

    public String getPreCombineFiled() {
        return preCombineFiled;
    }

    public void setPreCombineFiled(String preCombineFiled) {
        this.preCombineFiled = preCombineFiled;
    }

    public String getPayloadClass() {
        return payloadClass;
    }

    public void setPayloadClass(String payloadClass) {
        this.payloadClass = payloadClass;
    }

    public String getRecordMergerImpls() {
        return recordMergerImpls;
    }

    public void setRecordMergerImpls(String recordMergerImpls) {
        this.recordMergerImpls = recordMergerImpls;
    }

    public String getRecordMergerStrategy() {
        return recordMergerStrategy;
    }

    public void setRecordMergerStrategy(String recordMergerStrategy) {
        this.recordMergerStrategy = recordMergerStrategy;
    }

    public String getPartitionDefaultName() {
        return partitionDefaultName;
    }

    public void setPartitionDefaultName(String partitionDefaultName) {
        this.partitionDefaultName = partitionDefaultName;
    }

    public boolean isChangelogEnable() {
        return changelogEnable;
    }

    public void setChangelogEnable(boolean changelogEnable) {
        this.changelogEnable = changelogEnable;
    }

    public boolean isCdcEnable() {
        return cdcEnable;
    }

    public void setCdcEnable(boolean cdcEnable) {
        this.cdcEnable = cdcEnable;
    }

    public String getCdcSupplementalLoggingMode() {
        return cdcSupplementalLoggingMode;
    }

    public void setCdcSupplementalLoggingMode(String cdcSupplementalLoggingMode) {
        this.cdcSupplementalLoggingMode = cdcSupplementalLoggingMode;
    }

    public boolean isMetadataEnable() {
        return metadataEnable;
    }

    public void setMetadataEnable(boolean metadataEnable) {
        this.metadataEnable = metadataEnable;
    }

    public int getMetadataCompactionDeltaCommits() {
        return metadataCompactionDeltaCommits;
    }

    public void setMetadataCompactionDeltaCommits(int metadataCompactionDeltaCommits) {
        this.metadataCompactionDeltaCommits = metadataCompactionDeltaCommits;
    }

    public String getIndexType() {
        return indexType;
    }

    public void setIndexType(String indexType) {
        this.indexType = indexType;
    }

    public boolean isIndexBootstrapEnabled() {
        return indexBootstrapEnabled;
    }

    public void setIndexBootstrapEnabled(boolean indexBootstrapEnabled) {
        this.indexBootstrapEnabled = indexBootstrapEnabled;
    }

    public double getIndexStateTtl() {
        return indexStateTtl;
    }

    public void setIndexStateTtl(double indexStateTtl) {
        this.indexStateTtl = indexStateTtl;
    }

    public boolean isIndexGlobalEnabled() {
        return indexGlobalEnabled;
    }

    public void setIndexGlobalEnabled(boolean indexGlobalEnabled) {
        this.indexGlobalEnabled = indexGlobalEnabled;
    }

    public String getIndexPartitionRegex() {
        return indexPartitionRegex;
    }

    public void setIndexPartitionRegex(String indexPartitionRegex) {
        this.indexPartitionRegex = indexPartitionRegex;
    }

    public int getReadTasks() {
        return readTasks;
    }

    public void setReadTasks(int readTasks) {
        this.readTasks = readTasks;
    }

    public String getSourceAvroSchemaPath() {
        return sourceAvroSchemaPath;
    }

    public void setSourceAvroSchemaPath(String sourceAvroSchemaPath) {
        this.sourceAvroSchemaPath = sourceAvroSchemaPath;
    }

    public String getSourceAvroSchema() {
        return sourceAvroSchema;
    }

    public void setSourceAvroSchema(String sourceAvroSchema) {
        this.sourceAvroSchema = sourceAvroSchema;
    }

    public String getHoodieDatasourceQueryType() {
        return hoodieDatasourceQueryType;
    }

    public void setHoodieDatasourceQueryType(String hoodieDatasourceQueryType) {
        this.hoodieDatasourceQueryType = hoodieDatasourceQueryType;
    }

    public String getHoodieDatasourceMergeType() {
        return hoodieDatasourceMergeType;
    }

    public void setHoodieDatasourceMergeType(String hoodieDatasourceMergeType) {
        this.hoodieDatasourceMergeType = hoodieDatasourceMergeType;
    }

    public boolean isReadUtcTimezone() {
        return readUtcTimezone;
    }

    public void setReadUtcTimezone(boolean readUtcTimezone) {
        this.readUtcTimezone = readUtcTimezone;
    }

    public boolean isReadStreamingEnable() {
        return readStreamingEnable;
    }

    public void setReadStreamingEnable(boolean readStreamingEnable) {
        this.readStreamingEnable = readStreamingEnable;
    }

    public int getReadStreamingCheckInterval() {
        return readStreamingCheckInterval;
    }

    public void setReadStreamingCheckInterval(int readStreamingCheckInterval) {
        this.readStreamingCheckInterval = readStreamingCheckInterval;
    }

    public boolean isReadStreamingSkipCompaction() {
        return readStreamingSkipCompaction;
    }

    public void setReadStreamingSkipCompaction(boolean readStreamingSkipCompaction) {
        this.readStreamingSkipCompaction = readStreamingSkipCompaction;
    }

    public boolean isReadStreamingSkipClustering() {
        return readStreamingSkipClustering;
    }

    public void setReadStreamingSkipClustering(boolean readStreamingSkipClustering) {
        this.readStreamingSkipClustering = readStreamingSkipClustering;
    }

    public String getReadStartCommit() {
        return readStartCommit;
    }

    public void setReadStartCommit(String readStartCommit) {
        this.readStartCommit = readStartCommit;
    }

    public String getReadEndCommit() {
        return readEndCommit;
    }

    public void setReadEndCommit(String readEndCommit) {
        this.readEndCommit = readEndCommit;
    }

    public boolean isReadDataSkippingEnabled() {
        return readDataSkippingEnabled;
    }

    public void setReadDataSkippingEnabled(boolean readDataSkippingEnabled) {
        this.readDataSkippingEnabled = readDataSkippingEnabled;
    }

    public boolean isWriteInsertCluster() {
        return writeInsertCluster;
    }

    public void setWriteInsertCluster(boolean writeInsertCluster) {
        this.writeInsertCluster = writeInsertCluster;
    }

    public String getWriteOperation() {
        return writeOperation;
    }

    public void setWriteOperation(String writeOperation) {
        this.writeOperation = writeOperation;
    }

    public boolean isWritePreCombine() {
        return writePreCombine;
    }

    public void setWritePreCombine(boolean writePreCombine) {
        this.writePreCombine = writePreCombine;
    }

    public int getWriteRetryTimes() {
        return writeRetryTimes;
    }

    public void setWriteRetryTimes(int writeRetryTimes) {
        this.writeRetryTimes = writeRetryTimes;
    }

    public Long getWriteRetryIntervalMs() {
        return writeRetryIntervalMs;
    }

    public void setWriteRetryIntervalMs(Long writeRetryIntervalMs) {
        this.writeRetryIntervalMs = writeRetryIntervalMs;
    }

    public boolean isWriteIgnoreFailed() {
        return writeIgnoreFailed;
    }

    public void setWriteIgnoreFailed(boolean writeIgnoreFailed) {
        this.writeIgnoreFailed = writeIgnoreFailed;
    }

    public String getRecordKeyField() {
        return recordKeyField;
    }

    public void setRecordKeyField(String recordKeyField) {
        this.recordKeyField = recordKeyField;
    }

    public String getIndexKeyField() {
        return indexKeyField;
    }

    public void setIndexKeyField(String indexKeyField) {
        this.indexKeyField = indexKeyField;
    }

    public int getBucketIndexNumBuckets() {
        return bucketIndexNumBuckets;
    }

    public void setBucketIndexNumBuckets(int bucketIndexNumBuckets) {
        this.bucketIndexNumBuckets = bucketIndexNumBuckets;
    }

    public String getPartitionPathFiled() {
        return partitionPathFiled;
    }

    public void setPartitionPathFiled(String partitionPathFiled) {
        this.partitionPathFiled = partitionPathFiled;
    }

    public boolean isUrlEncodePartitioning() {
        return urlEncodePartitioning;
    }

    public void setUrlEncodePartitioning(boolean urlEncodePartitioning) {
        this.urlEncodePartitioning = urlEncodePartitioning;
    }

    public boolean isHiveStylePartitioning() {
        return hiveStylePartitioning;
    }

    public void setHiveStylePartitioning(boolean hiveStylePartitioning) {
        this.hiveStylePartitioning = hiveStylePartitioning;
    }

    public String getKeygenClassName() {
        return keygenClassName;
    }

    public void setKeygenClassName(String keygenClassName) {
        this.keygenClassName = keygenClassName;
    }

    public String getKeygenType() {
        return keygenType;
    }

    public void setKeygenType(String keygenType) {
        this.keygenType = keygenType;
    }

    public String getWritePartitionFormat() {
        return writePartitionFormat;
    }

    public void setWritePartitionFormat(String writePartitionFormat) {
        this.writePartitionFormat = writePartitionFormat;
    }

    public int getWriteIndexBootstrapTasks() {
        return writeIndexBootstrapTasks;
    }

    public void setWriteIndexBootstrapTasks(int writeIndexBootstrapTasks) {
        this.writeIndexBootstrapTasks = writeIndexBootstrapTasks;
    }

    public int getWriteBucketAssignTasks() {
        return writeBucketAssignTasks;
    }

    public void setWriteBucketAssignTasks(int writeBucketAssignTasks) {
        this.writeBucketAssignTasks = writeBucketAssignTasks;
    }

    public int getWriteTasks() {
        return writeTasks;
    }

    public void setWriteTasks(int writeTasks) {
        this.writeTasks = writeTasks;
    }

    public double getWriteTaskMaxSize() {
        return writeTaskMaxSize;
    }

    public void setWriteTaskMaxSize(double writeTaskMaxSize) {
        this.writeTaskMaxSize = writeTaskMaxSize;
    }

    public long getWriteRateLimit() {
        return writeRateLimit;
    }

    public void setWriteRateLimit(long writeRateLimit) {
        this.writeRateLimit = writeRateLimit;
    }

    public double getWriteBatchSize() {
        return writeBatchSize;
    }

    public void setWriteBatchSize(double writeBatchSize) {
        this.writeBatchSize = writeBatchSize;
    }

    public int getWriteLogBlockSize() {
        return writeLogBlockSize;
    }

    public void setWriteLogBlockSize(int writeLogBlockSize) {
        this.writeLogBlockSize = writeLogBlockSize;
    }

    public long getWriteLogMaxSize() {
        return writeLogMaxSize;
    }

    public void setWriteLogMaxSize(long writeLogMaxSize) {
        this.writeLogMaxSize = writeLogMaxSize;
    }

    public int getWriteParquetMaxFileSize() {
        return writeParquetMaxFileSize;
    }

    public void setWriteParquetMaxFileSize(int writeParquetMaxFileSize) {
        this.writeParquetMaxFileSize = writeParquetMaxFileSize;
    }

    public int getWriteParquetPageSize() {
        return writeParquetPageSize;
    }

    public void setWriteParquetPageSize(int writeParquetPageSize) {
        this.writeParquetPageSize = writeParquetPageSize;
    }

    public int getWriteMergeMaxMemory() {
        return writeMergeMaxMemory;
    }

    public void setWriteMergeMaxMemory(int writeMergeMaxMemory) {
        this.writeMergeMaxMemory = writeMergeMaxMemory;
    }

    public long getWriteCommitAckTimeout() {
        return writeCommitAckTimeout;
    }

    public void setWriteCommitAckTimeout(long writeCommitAckTimeout) {
        this.writeCommitAckTimeout = writeCommitAckTimeout;
    }

    public boolean isWriteBulkInsertShuffleInput() {
        return writeBulkInsertShuffleInput;
    }

    public void setWriteBulkInsertShuffleInput(boolean writeBulkInsertShuffleInput) {
        this.writeBulkInsertShuffleInput = writeBulkInsertShuffleInput;
    }

    public boolean isWriteBulkInsertSortInput() {
        return writeBulkInsertSortInput;
    }

    public void setWriteBulkInsertSortInput(boolean writeBulkInsertSortInput) {
        this.writeBulkInsertSortInput = writeBulkInsertSortInput;
    }

    public boolean isWriteBulkInsertSortInputByRecordKey() {
        return writeBulkInsertSortInputByRecordKey;
    }

    public void setWriteBulkInsertSortInputByRecordKey(
            boolean writeBulkInsertSortInputByRecordKey) {
        this.writeBulkInsertSortInputByRecordKey = writeBulkInsertSortInputByRecordKey;
    }

    public int getWriteSortMemory() {
        return writeSortMemory;
    }

    public void setWriteSortMemory(int writeSortMemory) {
        this.writeSortMemory = writeSortMemory;
    }

    public int getWriteClientId() {
        return writeClientId;
    }

    public void setWriteClientId(int writeClientId) {
        this.writeClientId = writeClientId;
    }

    public boolean isCompactionScheduleEnabled() {
        return compactionScheduleEnabled;
    }

    public void setCompactionScheduleEnabled(boolean compactionScheduleEnabled) {
        this.compactionScheduleEnabled = compactionScheduleEnabled;
    }

    public boolean isCompactionAsyncEnabled() {
        return compactionAsyncEnabled;
    }

    public void setCompactionAsyncEnabled(boolean compactionAsyncEnabled) {
        this.compactionAsyncEnabled = compactionAsyncEnabled;
    }

    public int getCompactionTasks() {
        return compactionTasks;
    }

    public void setCompactionTasks(int compactionTasks) {
        this.compactionTasks = compactionTasks;
    }

    public String getCompactionTriggerStrategy() {
        return compactionTriggerStrategy;
    }

    public void setCompactionTriggerStrategy(String compactionTriggerStrategy) {
        this.compactionTriggerStrategy = compactionTriggerStrategy;
    }

    public int getCompactionDeltaCommits() {
        return compactionDeltaCommits;
    }

    public void setCompactionDeltaCommits(int compactionDeltaCommits) {
        this.compactionDeltaCommits = compactionDeltaCommits;
    }

    public int getCompactionDeltaSeconds() {
        return compactionDeltaSeconds;
    }

    public void setCompactionDeltaSeconds(int compactionDeltaSeconds) {
        this.compactionDeltaSeconds = compactionDeltaSeconds;
    }

    public int getCompactionTimeoutSeconds() {
        return compactionTimeoutSeconds;
    }

    public void setCompactionTimeoutSeconds(int compactionTimeoutSeconds) {
        this.compactionTimeoutSeconds = compactionTimeoutSeconds;
    }

    public int getCompactionMaxMemory() {
        return compactionMaxMemory;
    }

    public void setCompactionMaxMemory(int compactionMaxMemory) {
        this.compactionMaxMemory = compactionMaxMemory;
    }

    public long getCompactionTargetIo() {
        return compactionTargetIo;
    }

    public void setCompactionTargetIo(long compactionTargetIo) {
        this.compactionTargetIo = compactionTargetIo;
    }

    public boolean isCleanAsyncEnabled() {
        return cleanAsyncEnabled;
    }

    public void setCleanAsyncEnabled(boolean cleanAsyncEnabled) {
        this.cleanAsyncEnabled = cleanAsyncEnabled;
    }

    public String getCleanPolicy() {
        return cleanPolicy;
    }

    public void setCleanPolicy(String cleanPolicy) {
        this.cleanPolicy = cleanPolicy;
    }

    public int getCleanRetainCommits() {
        return cleanRetainCommits;
    }

    public void setCleanRetainCommits(int cleanRetainCommits) {
        this.cleanRetainCommits = cleanRetainCommits;
    }

    public int getCleanRetainHours() {
        return cleanRetainHours;
    }

    public void setCleanRetainHours(int cleanRetainHours) {
        this.cleanRetainHours = cleanRetainHours;
    }

    public int getCleanRetainFileVersions() {
        return cleanRetainFileVersions;
    }

    public void setCleanRetainFileVersions(int cleanRetainFileVersions) {
        this.cleanRetainFileVersions = cleanRetainFileVersions;
    }

    public int getArchiveMaxCommits() {
        return archiveMaxCommits;
    }

    public void setArchiveMaxCommits(int archiveMaxCommits) {
        this.archiveMaxCommits = archiveMaxCommits;
    }

    public int getArchiveMinCommits() {
        return archiveMinCommits;
    }

    public void setArchiveMinCommits(int archiveMinCommits) {
        this.archiveMinCommits = archiveMinCommits;
    }

    public boolean isClusteringScheduleEnabled() {
        return clusteringScheduleEnabled;
    }

    public void setClusteringScheduleEnabled(boolean clusteringScheduleEnabled) {
        this.clusteringScheduleEnabled = clusteringScheduleEnabled;
    }

    public boolean isClusteringAsyncEnabled() {
        return clusteringAsyncEnabled;
    }

    public void setClusteringAsyncEnabled(boolean clusteringAsyncEnabled) {
        this.clusteringAsyncEnabled = clusteringAsyncEnabled;
    }

    public int getClusteringDeltaCommits() {
        return clusteringDeltaCommits;
    }

    public void setClusteringDeltaCommits(int clusteringDeltaCommits) {
        this.clusteringDeltaCommits = clusteringDeltaCommits;
    }

    public int getClusteringTasks() {
        return clusteringTasks;
    }

    public void setClusteringTasks(int clusteringTasks) {
        this.clusteringTasks = clusteringTasks;
    }

    public int getClusteringPlanStrategyDayBasedLookBackPartitions() {
        return clusteringPlanStrategyDayBasedLookBackPartitions;
    }

    public void setClusteringPlanStrategyDayBasedLookBackPartitions(
            int clusteringPlanStrategyDayBasedLookBackPartitions) {
        this.clusteringPlanStrategyDayBasedLookBackPartitions =
                clusteringPlanStrategyDayBasedLookBackPartitions;
    }

    public int getClusteringPlanStrategyDaybasedSkipfromlatestPartitions() {
        return clusteringPlanStrategyDaybasedSkipfromlatestPartitions;
    }

    public void setClusteringPlanStrategyDaybasedSkipfromlatestPartitions(
            int clusteringPlanStrategyDaybasedSkipfromlatestPartitions) {
        this.clusteringPlanStrategyDaybasedSkipfromlatestPartitions =
                clusteringPlanStrategyDaybasedSkipfromlatestPartitions;
    }

    public String getClusteringPlanStrategyClusterBeginPartition() {
        return clusteringPlanStrategyClusterBeginPartition;
    }

    public void setClusteringPlanStrategyClusterBeginPartition(
            String clusteringPlanStrategyClusterBeginPartition) {
        this.clusteringPlanStrategyClusterBeginPartition =
                clusteringPlanStrategyClusterBeginPartition;
    }

    public String getClusteringPlanStrategyClusterEndPartition() {
        return clusteringPlanStrategyClusterEndPartition;
    }

    public void setClusteringPlanStrategyClusterEndPartition(
            String clusteringPlanStrategyClusterEndPartition) {
        this.clusteringPlanStrategyClusterEndPartition = clusteringPlanStrategyClusterEndPartition;
    }

    public String getClusteringPlanStrategyPartitionRegexPattern() {
        return clusteringPlanStrategyPartitionRegexPattern;
    }

    public void setClusteringPlanStrategyPartitionRegexPattern(
            String clusteringPlanStrategyPartitionRegexPattern) {
        this.clusteringPlanStrategyPartitionRegexPattern =
                clusteringPlanStrategyPartitionRegexPattern;
    }

    public String getClusteringPlanStrategyPartitionSelected() {
        return clusteringPlanStrategyPartitionSelected;
    }

    public void setClusteringPlanStrategyPartitionSelected(
            String clusteringPlanStrategyPartitionSelected) {
        this.clusteringPlanStrategyPartitionSelected = clusteringPlanStrategyPartitionSelected;
    }

    public String getClusteringPlanStrategyClass() {
        return clusteringPlanStrategyClass;
    }

    public void setClusteringPlanStrategyClass(String clusteringPlanStrategyClass) {
        this.clusteringPlanStrategyClass = clusteringPlanStrategyClass;
    }

    public String getClusteringPlanPartitionFilterMode() {
        return clusteringPlanPartitionFilterMode;
    }

    public void setClusteringPlanPartitionFilterMode(String clusteringPlanPartitionFilterMode) {
        this.clusteringPlanPartitionFilterMode = clusteringPlanPartitionFilterMode;
    }

    public long getClusteringPlanStrategyTargetFileMaxBytes() {
        return clusteringPlanStrategyTargetFileMaxBytes;
    }

    public void setClusteringPlanStrategyTargetFileMaxBytes(
            long clusteringPlanStrategyTargetFileMaxBytes) {
        this.clusteringPlanStrategyTargetFileMaxBytes = clusteringPlanStrategyTargetFileMaxBytes;
    }

    public long getClusteringPlanStrategySmallFileLimit() {
        return clusteringPlanStrategySmallFileLimit;
    }

    public void setClusteringPlanStrategySmallFileLimit(long clusteringPlanStrategySmallFileLimit) {
        this.clusteringPlanStrategySmallFileLimit = clusteringPlanStrategySmallFileLimit;
    }

    public int getClusteringPlanStrategyDayBasedSkipFromLatestPartitions() {
        return clusteringPlanStrategyDayBasedSkipFromLatestPartitions;
    }

    public void setClusteringPlanStrategyDayBasedSkipFromLatestPartitions(
            int clusteringPlanStrategyDayBasedSkipFromLatestPartitions) {
        this.clusteringPlanStrategyDayBasedSkipFromLatestPartitions =
                clusteringPlanStrategyDayBasedSkipFromLatestPartitions;
    }

    public String getClusteringPlanStrategySortColumns() {
        return clusteringPlanStrategySortColumns;
    }

    public void setClusteringPlanStrategySortColumns(String clusteringPlanStrategySortColumns) {
        this.clusteringPlanStrategySortColumns = clusteringPlanStrategySortColumns;
    }

    public int getClusteringPlanStrategyMaxNumGroups() {
        return clusteringPlanStrategyMaxNumGroups;
    }

    public void setClusteringPlanStrategyMaxNumGroups(int clusteringPlanStrategyMaxNumGroups) {
        this.clusteringPlanStrategyMaxNumGroups = clusteringPlanStrategyMaxNumGroups;
    }

    public boolean isHiveSyncEnabled() {
        return hiveSyncEnabled;
    }

    public void setHiveSyncEnabled(boolean hiveSyncEnabled) {
        this.hiveSyncEnabled = hiveSyncEnabled;
    }

    public String getHiveSyncDb() {
        return hiveSyncDb;
    }

    public void setHiveSyncDb(String hiveSyncDb) {
        this.hiveSyncDb = hiveSyncDb;
    }

    public String getHiveSyncTable() {
        return hiveSyncTable;
    }

    public void setHiveSyncTable(String hiveSyncTable) {
        this.hiveSyncTable = hiveSyncTable;
    }

    public String getHiveSyncFileFormat() {
        return hiveSyncFileFormat;
    }

    public void setHiveSyncFileFormat(String hiveSyncFileFormat) {
        this.hiveSyncFileFormat = hiveSyncFileFormat;
    }

    public String getHiveSyncMode() {
        return hiveSyncMode;
    }

    public void setHiveSyncMode(String hiveSyncMode) {
        this.hiveSyncMode = hiveSyncMode;
    }

    public String getHiveSyncUsername() {
        return hiveSyncUsername;
    }

    public void setHiveSyncUsername(String hiveSyncUsername) {
        this.hiveSyncUsername = hiveSyncUsername;
    }

    public String getHiveSyncPassword() {
        return hiveSyncPassword;
    }

    public void setHiveSyncPassword(String hiveSyncPassword) {
        this.hiveSyncPassword = hiveSyncPassword;
    }

    public String getHiveSyncJdbcUrl() {
        return hiveSyncJdbcUrl;
    }

    public void setHiveSyncJdbcUrl(String hiveSyncJdbcUrl) {
        this.hiveSyncJdbcUrl = hiveSyncJdbcUrl;
    }

    public String getHiveSyncMetastoreUrl() {
        return hiveSyncMetastoreUrl;
    }

    public void setHiveSyncMetastoreUrl(String hiveSyncMetastoreUrl) {
        this.hiveSyncMetastoreUrl = hiveSyncMetastoreUrl;
    }

    public String getHiveSyncPartitionFields() {
        return hiveSyncPartitionFields;
    }

    public void setHiveSyncPartitionFields(String hiveSyncPartitionFields) {
        this.hiveSyncPartitionFields = hiveSyncPartitionFields;
    }

    public String getHiveSyncPartitionExtractorClass() {
        return hiveSyncPartitionExtractorClass;
    }

    public void setHiveSyncPartitionExtractorClass(String hiveSyncPartitionExtractorClass) {
        this.hiveSyncPartitionExtractorClass = hiveSyncPartitionExtractorClass;
    }

    public boolean isHiveSyncAssumeDatePartitioning() {
        return hiveSyncAssumeDatePartitioning;
    }

    public void setHiveSyncAssumeDatePartitioning(boolean hiveSyncAssumeDatePartitioning) {
        this.hiveSyncAssumeDatePartitioning = hiveSyncAssumeDatePartitioning;
    }

    public boolean isHiveSyncUseJdbc() {
        return hiveSyncUseJdbc;
    }

    public void setHiveSyncUseJdbc(boolean hiveSyncUseJdbc) {
        this.hiveSyncUseJdbc = hiveSyncUseJdbc;
    }

    public boolean isHiveSyncAutoCreateDb() {
        return hiveSyncAutoCreateDb;
    }

    public void setHiveSyncAutoCreateDb(boolean hiveSyncAutoCreateDb) {
        this.hiveSyncAutoCreateDb = hiveSyncAutoCreateDb;
    }

    public boolean isHiveSyncIgnoreExceptions() {
        return hiveSyncIgnoreExceptions;
    }

    public void setHiveSyncIgnoreExceptions(boolean hiveSyncIgnoreExceptions) {
        this.hiveSyncIgnoreExceptions = hiveSyncIgnoreExceptions;
    }

    public boolean isHiveSyncSkipRoSuffix() {
        return hiveSyncSkipRoSuffix;
    }

    public void setHiveSyncSkipRoSuffix(boolean hiveSyncSkipRoSuffix) {
        this.hiveSyncSkipRoSuffix = hiveSyncSkipRoSuffix;
    }

    public boolean isHiveSyncSupportTimestamp() {
        return hiveSyncSupportTimestamp;
    }

    public void setHiveSyncSupportTimestamp(boolean hiveSyncSupportTimestamp) {
        this.hiveSyncSupportTimestamp = hiveSyncSupportTimestamp;
    }

    public String getHiveSyncTableProperties() {
        return hiveSyncTableProperties;
    }

    public void setHiveSyncTableProperties(String hiveSyncTableProperties) {
        this.hiveSyncTableProperties = hiveSyncTableProperties;
    }

    public String getHiveSyncSerdeProperties() {
        return hiveSyncSerdeProperties;
    }

    public void setHiveSyncSerdeProperties(String hiveSyncSerdeProperties) {
        this.hiveSyncSerdeProperties = hiveSyncSerdeProperties;
    }

    public String getHiveSyncConfDir() {
        return hiveSyncConfDir;
    }

    public void setHiveSyncConfDir(String hiveSyncConfDir) {
        this.hiveSyncConfDir = hiveSyncConfDir;
    }

    public String getHiveSyncTableStrategy() {
        return hiveSyncTableStrategy;
    }

    public void setHiveSyncTableStrategy(String hiveSyncTableStrategy) {
        this.hiveSyncTableStrategy = hiveSyncTableStrategy;
    }

    static {
        configOptionMap = new HashMap<>();
        // Base
        configOptionMap.put("path", FlinkOptions.PATH);

        // Common Options
        configOptionMap.put("hoodieDatabaseName", FlinkOptions.DATABASE_NAME);
        configOptionMap.put("hoodieTableName", FlinkOptions.TABLE_NAME);
        configOptionMap.put("tableType", FlinkOptions.TABLE_TYPE);
        configOptionMap.put("preCombineFiled", FlinkOptions.PRECOMBINE_FIELD);
        configOptionMap.put("payloadClass", FlinkOptions.PAYLOAD_CLASS_NAME);
        configOptionMap.put("recordMergerImpls", FlinkOptions.RECORD_MERGER_IMPLS);
        configOptionMap.put("recordMergerStrategy", FlinkOptions.RECORD_MERGER_STRATEGY);
        configOptionMap.put("partitionDefaultName", FlinkOptions.PARTITION_DEFAULT_NAME);

        // Changelog Capture Options
        configOptionMap.put("changelogEnable", FlinkOptions.CHANGELOG_ENABLED);
        configOptionMap.put("cdcEnable", FlinkOptions.CDC_ENABLED);
        configOptionMap.put("cdcSupplementalLoggingMode", FlinkOptions.SUPPLEMENTAL_LOGGING_MODE);

        // Metadata Table Options
        configOptionMap.put("metadataEnable", FlinkOptions.METADATA_ENABLED);
        configOptionMap.put(
                "metadataCompactionDeltaCommits", FlinkOptions.METADATA_COMPACTION_DELTA_COMMITS);

        // Index Options
        configOptionMap.put("indexType", FlinkOptions.INDEX_TYPE);
        configOptionMap.put("indexBootstrapEnabled", FlinkOptions.INDEX_BOOTSTRAP_ENABLED);
        configOptionMap.put("indexStateTtl", FlinkOptions.INDEX_STATE_TTL);
        configOptionMap.put("indexGlobalEnabled", FlinkOptions.INDEX_GLOBAL_ENABLED);
        configOptionMap.put("indexPartitionRegex", FlinkOptions.INDEX_PARTITION_REGEX);

        // Read Options
        configOptionMap.put("readTasks", FlinkOptions.READ_TASKS);
        configOptionMap.put("sourceAvroSchemaPath", FlinkOptions.SOURCE_AVRO_SCHEMA);
        configOptionMap.put("sourceAvroSchema", FlinkOptions.SOURCE_AVRO_SCHEMA_PATH);
        configOptionMap.put("hoodieDatasourceQueryType", FlinkOptions.QUERY_TYPE);
        configOptionMap.put("hoodieDatasourceMergeType", FlinkOptions.MERGE_TYPE);
        configOptionMap.put("readUtcTimezone", FlinkOptions.UTC_TIMEZONE);
        configOptionMap.put("readStreamingEnable", FlinkOptions.READ_AS_STREAMING);
        configOptionMap.put(
                "readStreamingCheckInterval", FlinkOptions.READ_STREAMING_CHECK_INTERVAL);
        configOptionMap.put(
                "readStreamingSkipCompaction", FlinkOptions.READ_STREAMING_SKIP_COMPACT);
        configOptionMap.put(
                "readStreamingSkipClustering", FlinkOptions.READ_STREAMING_SKIP_CLUSTERING);
        configOptionMap.put("readStartCommit", FlinkOptions.READ_START_COMMIT);
        configOptionMap.put("readEndCommit", FlinkOptions.READ_END_COMMIT);
        configOptionMap.put("readDataSkippingEnabled", FlinkOptions.READ_DATA_SKIPPING_ENABLED);

        // Write Options
        configOptionMap.put("writeInsertCluster", FlinkOptions.INSERT_CLUSTER);
        configOptionMap.put("writeOperation", FlinkOptions.OPERATION);
        configOptionMap.put("writePreCombine", FlinkOptions.PRE_COMBINE);
        configOptionMap.put("writeRetryTimes", FlinkOptions.RETRY_TIMES);
        configOptionMap.put("writeRetryIntervalMs", FlinkOptions.RETRY_INTERVAL_MS);
        configOptionMap.put("writeIgnoreFailed", FlinkOptions.IGNORE_FAILED);
        configOptionMap.put("recordKeyField", FlinkOptions.RECORD_KEY_FIELD);
        configOptionMap.put("indexKeyField", FlinkOptions.INDEX_KEY_FIELD);
        configOptionMap.put("bucketIndexNumBuckets", FlinkOptions.BUCKET_INDEX_NUM_BUCKETS);
        configOptionMap.put("partitionPathFiled", FlinkOptions.PARTITION_PATH_FIELD);
        configOptionMap.put("urlEncodePartitioning", FlinkOptions.URL_ENCODE_PARTITIONING);
        configOptionMap.put("hiveStylePartitioning", FlinkOptions.HIVE_STYLE_PARTITIONING);
        configOptionMap.put("keygenClassName", FlinkOptions.KEYGEN_CLASS_NAME);
        configOptionMap.put("keygenType", FlinkOptions.KEYGEN_TYPE);
        configOptionMap.put("writePartitionFormat", FlinkOptions.PARTITION_FORMAT);
        configOptionMap.put("writeIndexBootstrapTasks", FlinkOptions.INDEX_BOOTSTRAP_TASKS);
        configOptionMap.put("writeBucketAssignTasks", FlinkOptions.BUCKET_ASSIGN_TASKS);
        configOptionMap.put("writeTasks", FlinkOptions.WRITE_TASKS);
        configOptionMap.put("writeTaskMaxSize", FlinkOptions.WRITE_TASK_MAX_SIZE);
        configOptionMap.put("writeRateLimit", FlinkOptions.WRITE_RATE_LIMIT);
        configOptionMap.put("writeBatchSize", FlinkOptions.WRITE_BATCH_SIZE);
        configOptionMap.put("writeLogBlockSize", FlinkOptions.WRITE_LOG_BLOCK_SIZE);
        configOptionMap.put("writeLogMaxSize", FlinkOptions.WRITE_LOG_MAX_SIZE);
        configOptionMap.put("writeParquetMaxFileSize", FlinkOptions.WRITE_PARQUET_MAX_FILE_SIZE);
        configOptionMap.put("writeParquetPageSize", FlinkOptions.WRITE_PARQUET_PAGE_SIZE);
        configOptionMap.put("writeMergeMaxMemory", FlinkOptions.WRITE_MERGE_MAX_MEMORY);
        configOptionMap.put("writeCommitAckTimeout", FlinkOptions.WRITE_COMMIT_ACK_TIMEOUT);
        configOptionMap.put(
                "writeBulkInsertShuffleInput", FlinkOptions.WRITE_BULK_INSERT_SHUFFLE_INPUT);
        configOptionMap.put("writeBulkInsertSortInput", FlinkOptions.WRITE_BULK_INSERT_SORT_INPUT);
        configOptionMap.put(
                "writeBulkInsertSortInputByRecordKey",
                FlinkOptions.WRITE_BULK_INSERT_SORT_INPUT_BY_RECORD_KEY);
        configOptionMap.put("writeClientId", FlinkOptions.WRITE_CLIENT_ID);
        configOptionMap.put("writeSortMemory", FlinkOptions.WRITE_SORT_MEMORY);

        // Compaction Options
        configOptionMap.put("compactionScheduleEnabled", FlinkOptions.COMPACTION_SCHEDULE_ENABLED);
        configOptionMap.put("compactionAsyncEnabled", FlinkOptions.COMPACTION_ASYNC_ENABLED);
        configOptionMap.put("compactionTasks", FlinkOptions.COMPACTION_TASKS);
        configOptionMap.put("compactionTriggerStrategy", FlinkOptions.COMPACTION_TRIGGER_STRATEGY);
        configOptionMap.put("compactionDeltaCommits", FlinkOptions.COMPACTION_DELTA_COMMITS);
        configOptionMap.put("compactionDeltaSeconds", FlinkOptions.COMPACTION_DELTA_SECONDS);
        configOptionMap.put("compactionTimeoutSeconds", FlinkOptions.COMPACTION_TIMEOUT_SECONDS);
        configOptionMap.put("compactionMaxMemory", FlinkOptions.COMPACTION_MAX_MEMORY);
        configOptionMap.put("compactionTargetIo", FlinkOptions.COMPACTION_TARGET_IO);
        configOptionMap.put("cleanAsyncEnabled", FlinkOptions.CLEAN_ASYNC_ENABLED);
        configOptionMap.put("cleanPolicy", FlinkOptions.CLEAN_POLICY);
        configOptionMap.put("cleanRetainCommits", FlinkOptions.CLEAN_RETAIN_COMMITS);
        configOptionMap.put("cleanRetainHours", FlinkOptions.CLEAN_RETAIN_HOURS);
        configOptionMap.put("cleanRetainFileVersions", FlinkOptions.CLEAN_RETAIN_FILE_VERSIONS);
        configOptionMap.put("archiveMaxCommits", FlinkOptions.ARCHIVE_MAX_COMMITS);
        configOptionMap.put("archiveMinCommits", FlinkOptions.ARCHIVE_MIN_COMMITS);

        // Clustering Options
        configOptionMap.put("clusteringScheduleEnabled", FlinkOptions.CLUSTERING_SCHEDULE_ENABLED);
        configOptionMap.put("clusteringAsyncEnabled", FlinkOptions.CLUSTERING_ASYNC_ENABLED);
        configOptionMap.put("clusteringDeltaCommits", FlinkOptions.CLUSTERING_DELTA_COMMITS);
        configOptionMap.put("clusteringTasks", FlinkOptions.CLUSTERING_TASKS);
        configOptionMap.put(
                "clusteringPlanStrategyDayBasedLookBackPartitions",
                FlinkOptions.CLUSTERING_TARGET_PARTITIONS);
        configOptionMap.put(
                "clusteringPlanStrategyDaybasedSkipfromlatestPartitions",
                FlinkOptions.CLUSTERING_PLAN_STRATEGY_SKIP_PARTITIONS_FROM_LATEST);
        configOptionMap.put(
                "clusteringPlanStrategyClusterBeginPartition",
                FlinkOptions.CLUSTERING_PLAN_STRATEGY_CLUSTER_BEGIN_PARTITION);
        configOptionMap.put(
                "clusteringPlanStrategyClusterEndPartition",
                FlinkOptions.CLUSTERING_PLAN_STRATEGY_CLUSTER_END_PARTITION);
        configOptionMap.put(
                "clusteringPlanStrategyPartitionRegexPattern",
                FlinkOptions.CLUSTERING_PLAN_STRATEGY_PARTITION_REGEX_PATTERN);
        configOptionMap.put(
                "clusteringPlanStrategyPartitionSelected",
                FlinkOptions.CLUSTERING_PLAN_STRATEGY_PARTITION_SELECTED);

        // private int clusteringPlanStrategyDaybasedSkipfromlatestPartitions = 0;
        //    private String clusteringPlanStrategyClusterBeginPartition = "";
        //    private String clusteringPlanStrategyClusterEndPartition = "";
        //    private String clusteringPlanStrategyPartitionRegexPattern = "";
        //    private String clusteringPlanStrategyPartitionSelected = "";

        configOptionMap.put(
                "clusteringPlanStrategyClass", FlinkOptions.CLUSTERING_PLAN_STRATEGY_CLASS);
        configOptionMap.put(
                "clusteringPlanPartitionFilterMode",
                FlinkOptions.CLUSTERING_PLAN_PARTITION_FILTER_MODE_NAME);
        configOptionMap.put(
                "clusteringPlanStrategyTargetFileMaxBytes",
                FlinkOptions.CLUSTERING_PLAN_STRATEGY_TARGET_FILE_MAX_BYTES);
        configOptionMap.put(
                "clusteringPlanStrategySmallFileLimit",
                FlinkOptions.CLUSTERING_PLAN_STRATEGY_SMALL_FILE_LIMIT);
        configOptionMap.put(
                "clusteringPlanStrategyDayBasedSkipFromLatestPartitions",
                FlinkOptions.CLUSTERING_PLAN_STRATEGY_SKIP_PARTITIONS_FROM_LATEST);
        configOptionMap.put(
                "clusteringPlanStrategySortColumns", FlinkOptions.CLUSTERING_SORT_COLUMNS);
        configOptionMap.put(
                "clusteringPlanStrategyMaxNumGroups", FlinkOptions.CLUSTERING_MAX_NUM_GROUPS);

        // Hive Sync Options
        configOptionMap.put("hiveSyncEnabled", FlinkOptions.HIVE_SYNC_ENABLED);
        configOptionMap.put("hiveSyncDb", FlinkOptions.HIVE_SYNC_DB);
        configOptionMap.put("hiveSyncTable", FlinkOptions.HIVE_SYNC_TABLE);
        configOptionMap.put("hiveSyncFileFormat", FlinkOptions.HIVE_SYNC_FILE_FORMAT);
        configOptionMap.put("hiveSyncMode", FlinkOptions.HIVE_SYNC_MODE);
        configOptionMap.put("hiveSyncUsername", FlinkOptions.HIVE_SYNC_USERNAME);
        configOptionMap.put("hiveSyncPassword", FlinkOptions.HIVE_SYNC_PASSWORD);
        configOptionMap.put("hiveSyncJdbcUrl", FlinkOptions.HIVE_SYNC_JDBC_URL);
        configOptionMap.put("hiveSyncMetastoreUrl", FlinkOptions.HIVE_SYNC_METASTORE_URIS);
        configOptionMap.put("hiveSyncPartitionFields", FlinkOptions.HIVE_SYNC_PARTITION_FIELDS);
        configOptionMap.put(
                "hiveSyncPartitionExtractorClass",
                FlinkOptions.HIVE_SYNC_PARTITION_EXTRACTOR_CLASS_NAME);
        configOptionMap.put(
                "hiveSyncAssumeDatePartitioning", FlinkOptions.HIVE_SYNC_ASSUME_DATE_PARTITION);
        configOptionMap.put("hiveSyncUseJdbc", FlinkOptions.HIVE_SYNC_USE_JDBC);
        configOptionMap.put("hiveSyncAutoCreateDb", FlinkOptions.HIVE_SYNC_AUTO_CREATE_DB);
        configOptionMap.put("hiveSyncIgnoreExceptions", FlinkOptions.HIVE_SYNC_IGNORE_EXCEPTIONS);
        configOptionMap.put("hiveSyncSkipRoSuffix", FlinkOptions.HIVE_SYNC_SKIP_RO_SUFFIX);
        configOptionMap.put("hiveSyncSupportTimestamp", FlinkOptions.HIVE_SYNC_SUPPORT_TIMESTAMP);
        configOptionMap.put("hiveSyncTableProperties", FlinkOptions.HIVE_SYNC_TABLE_PROPERTIES);
        configOptionMap.put(
                "hiveSyncSerdeProperties", FlinkOptions.HIVE_SYNC_TABLE_SERDE_PROPERTIES);
        configOptionMap.put("hiveSyncConfDir", FlinkOptions.HIVE_SYNC_CONF_DIR);
        configOptionMap.put("hiveSyncTableStrategy", FlinkOptions.HIVE_SYNC_TABLE_STRATEGY);
    }

    public static Configuration toConfiguration(Map<String, Object> map) {
        // check valid

        Map<String, String> configurationMap = new HashMap<>();
        map.forEach(
                (k, v) -> {
                    if (configOptionMap.containsKey(k)) {
                        configurationMap.put(configOptionMap.get(k).key(), String.valueOf(v));
                    } else if (k.equals("hadoopConfig")) {
                        Map<String, Object> hadoopConfig = (Map<String, Object>) v;
                        hadoopConfig.entrySet().stream()
                                .forEach(
                                        entry -> {
                                            if (entry.getValue() instanceof Map) {
                                                configurationMap.put(
                                                        "hadoop." + entry.getKey(),
                                                        JsonUtil.toJson(entry.getValue()));
                                            } else {
                                                configurationMap.put(
                                                        "hadoop." + entry.getKey(),
                                                        String.valueOf(entry.getValue()));
                                            }
                                        });
                    }
                });
        return Configuration.fromMap(configurationMap);
    }
}
