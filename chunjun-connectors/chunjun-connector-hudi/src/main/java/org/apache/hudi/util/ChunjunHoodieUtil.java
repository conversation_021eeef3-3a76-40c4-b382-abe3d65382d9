package org.apache.hudi.util;

import com.dtstack.chunjun.common.security.KerberosUtil;

import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.LogicalTypeRoot;
import org.apache.flink.table.types.logical.RowType;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.hudi.common.model.DefaultHoodieRecordPayload;
import org.apache.hudi.common.model.HoodieTableType;
import org.apache.hudi.common.table.HoodieTableConfig;
import org.apache.hudi.configuration.ChunjunHoodieConfig;
import org.apache.hudi.configuration.FlinkOptions;
import org.apache.hudi.configuration.HadoopConfigurations;
import org.apache.hudi.configuration.OptionsResolver;
import org.apache.hudi.exception.HoodieValidationException;
import org.apache.hudi.keygen.ComplexAvroKeyGenerator;
import org.apache.hudi.keygen.NonpartitionedAvroKeyGenerator;
import org.apache.hudi.keygen.TimestampBasedAvroKeyGenerator;
import org.apache.hudi.keygen.constant.KeyGeneratorOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.apache.hudi.keygen.constant.KeyGeneratorOptions.Config.TIMESTAMP_INPUT_DATE_FORMAT_PROP;
import static org.apache.hudi.keygen.constant.KeyGeneratorOptions.Config.TIMESTAMP_OUTPUT_DATE_FORMAT_PROP;
import static org.apache.hudi.keygen.constant.KeyGeneratorOptions.Config.TIMESTAMP_OUTPUT_TIMEZONE_FORMAT_PROP;
import static org.apache.hudi.keygen.constant.KeyGeneratorOptions.Config.TIMESTAMP_TYPE_FIELD_PROP;

public class ChunjunHoodieUtil {

    public static final Logger LOG = LoggerFactory.getLogger(ChunjunHoodieUtil.class);

    private static final String KEY_HADOOP_SECURITY_AUTHORIZATION = "hadoop.security.authorization";
    private static final String KEY_HADOOP_SECURITY_AUTHENTICATION =
            "hadoop.security.authentication";
    private static final String KEY_FS_HDFS_IMPL_DISABLE_CACHE = "fs.hdfs.impl.disable.cache";
    private static final String KEY_HADOOP_USER_NAME = "hadoop.user.name";
    private static final String KEY_HADOOP_PROXY_ENABLE = "hadoop.proxy.enable";
    private static final String KEY_HADOOP_PROXY_USERNAME = "hadoop.proxy.user.name";

    public static Map<String, Object> convertFlinkConfigurationHadoopConfiguration(
            org.apache.flink.configuration.Configuration conf) {
        Configuration hadoopConfigMap = HadoopConfigurations.getHadoopConf(conf);
        Map<String, Object> hadoopConfig =
                hadoopConfigMap.getValByRegex(".*").entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        return hadoopConfig;
    }

    public static UserGroupInformation getUgi(Map<String, Object> hadoopConfig) throws IOException {
        UserGroupInformation ugi;
        // Kerberos 方式
        if (isOpenKerberos(hadoopConfig)) {
            if (StringUtils.isBlank(MapUtils.getString(hadoopConfig, "principalFile"))) {
                return UserGroupInformation.getCurrentUser();
            }
            String keytabFileName = KerberosUtil.getPrincipalFileName(hadoopConfig);
            keytabFileName = KerberosUtil.loadFile(hadoopConfig, keytabFileName, null, null, null);
            String principal = KerberosUtil.getPrincipal(hadoopConfig, keytabFileName);
            KerberosUtil.loadKrb5Conf(hadoopConfig, null, null, null);
            KerberosUtil.refreshConfig();
            ugi =
                    KerberosUtil.loginAndReturnUgi(
                            getConfiguration(hadoopConfig), principal, keytabFileName);

            boolean hadoopProxyEnabled =
                    Boolean.parseBoolean(
                            String.valueOf(
                                    hadoopConfig.getOrDefault(KEY_HADOOP_PROXY_ENABLE, "false")));
            String proxyUser = (String) hadoopConfig.get(KEY_HADOOP_PROXY_USERNAME);
            // hadoop代理基于开启kerberos之上
            if (hadoopProxyEnabled && StringUtils.isNotBlank(proxyUser)) {
                ugi = UserGroupInformation.createProxyUser(proxyUser, ugi);
                LOG.info(
                        "userGroupInformation current user = {} ugi proxy user  = {} ",
                        UserGroupInformation.getCurrentUser(),
                        ugi.getUserName());
            }
        } else {
            // 非 Kerberos 方式
            String loginUserName = UserGroupInformation.getLoginUser().getUserName();
            String hadoopUserName = (String) hadoopConfig.get(KEY_HADOOP_USER_NAME);
            if (StringUtils.isBlank(hadoopUserName)) {
                hadoopUserName = loginUserName;
            }

            LOG.info(
                    "Hadoop user from '{}' switch to '{}' with SIMPLE auth",
                    loginUserName,
                    hadoopUserName);
            ugi = UserGroupInformation.createRemoteUser(hadoopUserName);
        }
        return ugi;
    }

    public static Configuration getConfiguration(Map<String, Object> confMap) {
        confMap.put(KEY_FS_HDFS_IMPL_DISABLE_CACHE, "true");

        Configuration conf = new Configuration();
        confMap.forEach(
                (key, val) -> {
                    if (val != null) {
                        conf.set(key, val.toString());
                    }
                });
        return conf;
    }

    public static boolean isOpenKerberos(Map<String, Object> hadoopConfig) {
        if (!MapUtils.getBoolean(hadoopConfig, KEY_HADOOP_SECURITY_AUTHORIZATION, false)) {
            return false;
        }
        return KerberosUtil.KRB_STR.equalsIgnoreCase(
                MapUtils.getString(hadoopConfig, KEY_HADOOP_SECURITY_AUTHENTICATION));
    }

    public static void setupTableOptions(
            org.apache.flink.configuration.Configuration conf,
            ChunjunHoodieConfig chunjunHoodieConfig) {
        org.apache.hadoop.conf.Configuration hadoopConfiguration =
                new org.apache.hadoop.conf.Configuration();
        chunjunHoodieConfig.getHadoopConfig().entrySet().stream()
                .filter(entry -> entry.getValue() instanceof String && !entry.getKey().equals(""))
                .forEach(
                        entry ->
                                hadoopConfiguration.set(
                                        entry.getKey(), String.valueOf(entry.getValue())));
        String basePath = conf.getString(FlinkOptions.PATH);
        StreamerUtil.getTableConfig(basePath, hadoopConfiguration)
                .ifPresent(
                        tableConfig -> {
                            if (tableConfig.contains(HoodieTableConfig.RECORDKEY_FIELDS)
                                    && !conf.contains(FlinkOptions.RECORD_KEY_FIELD)) {
                                conf.setString(
                                        FlinkOptions.RECORD_KEY_FIELD,
                                        tableConfig.getString(HoodieTableConfig.RECORDKEY_FIELDS));
                            }
                            if (tableConfig.contains(HoodieTableConfig.PRECOMBINE_FIELD)
                                    && !conf.contains(FlinkOptions.PRECOMBINE_FIELD)) {
                                conf.setString(
                                        FlinkOptions.PRECOMBINE_FIELD,
                                        tableConfig.getString(HoodieTableConfig.PRECOMBINE_FIELD));
                            }
                            if (tableConfig.contains(
                                            HoodieTableConfig.HIVE_STYLE_PARTITIONING_ENABLE)
                                    && !conf.contains(FlinkOptions.HIVE_STYLE_PARTITIONING)) {
                                conf.setBoolean(
                                        FlinkOptions.HIVE_STYLE_PARTITIONING,
                                        tableConfig.getBoolean(
                                                HoodieTableConfig.HIVE_STYLE_PARTITIONING_ENABLE));
                            }
                            if (tableConfig.contains(HoodieTableConfig.TYPE)
                                    && !conf.contains(FlinkOptions.TABLE_TYPE)) {
                                conf.setString(
                                        FlinkOptions.TABLE_TYPE,
                                        tableConfig.getString(HoodieTableConfig.TYPE));
                            }
                            if (tableConfig.contains(HoodieTableConfig.PAYLOAD_CLASS_NAME)
                                    && !conf.contains(FlinkOptions.PAYLOAD_CLASS_NAME)) {
                                conf.setString(
                                        FlinkOptions.PAYLOAD_CLASS_NAME,
                                        tableConfig.getString(
                                                HoodieTableConfig.PAYLOAD_CLASS_NAME));
                            }
                        });
    }

    public static void setupConfOptions(
            org.apache.flink.configuration.Configuration conf,
            RowType rowType,
            List<String> columnNameList) {
        // hoodie key about options
        setupHoodieKeyOptions(conf, columnNameList, rowType);
        // compaction options
        setupCompactionOptions(conf);
        // read options
        setupReadOptions(conf);
        // write options
        setupWriteOptions(conf);
        // infer avro schema from physical DDL schema
        inferAvroSchema(conf, rowType);
    }

    /**
     * Sets up the hoodie key options (e.g. record key and partition key) from the table definition.
     */
    private static void setupHoodieKeyOptions(
            org.apache.flink.configuration.Configuration conf,
            List<String> columnNameList,
            RowType rowType) {

        // tweak the key gen class if possible
        final String[] partitions = conf.getString(FlinkOptions.PARTITION_PATH_FIELD).split(",");
        final String[] pks = conf.getString(FlinkOptions.RECORD_KEY_FIELD).split(",");
        if (partitions.length == 1) {
            final String partitionField = partitions[0];
            if (partitionField.isEmpty()) {
                conf.setString(
                        FlinkOptions.KEYGEN_CLASS_NAME,
                        NonpartitionedAvroKeyGenerator.class.getName());
                LOG.info(
                        "Table option [{}] is reset to {} because this is a non-partitioned table",
                        FlinkOptions.KEYGEN_CLASS_NAME.key(),
                        NonpartitionedAvroKeyGenerator.class.getName());
                return;
            }
            int i = 0;
            for (; i < columnNameList.size(); i++) {
                if (columnNameList.get(i).equals(partitionField)) {
                    break;
                }
            }
            if (i >= columnNameList.size()) {
                throw new HoodieValidationException("Field " + partitionField + " does not exist");
            }
            LogicalType partitionFieldType = rowType.getTypeAt(i);
            if (pks.length <= 1 && isDatetimeType(partitionFieldType)) {
                // timestamp based key gen only supports simple primary key
                setupTimestampKeygenOptions(conf, partitionFieldType);
                return;
            }
        }
        boolean complexHoodieKey = pks.length > 1 || partitions.length > 1;
        if (complexHoodieKey
                && FlinkOptions.isDefaultValueDefined(conf, FlinkOptions.KEYGEN_CLASS_NAME)) {
            conf.setString(FlinkOptions.KEYGEN_CLASS_NAME, ComplexAvroKeyGenerator.class.getName());
            LOG.info(
                    "Table option [{}] is reset to {} because record key or partition path has two or more fields",
                    FlinkOptions.KEYGEN_CLASS_NAME.key(),
                    ComplexAvroKeyGenerator.class.getName());
        }
    }

    /**
     * Sets up the keygen options when the partition path is datetime type.
     *
     * <p>The UTC timezone is used as default.
     */
    private static void setupTimestampKeygenOptions(
            org.apache.flink.configuration.Configuration conf, LogicalType fieldType) {
        if (conf.contains(FlinkOptions.KEYGEN_CLASS_NAME)) {
            // the keygen clazz has been set up explicitly, skipping
            return;
        }

        conf.setString(
                FlinkOptions.KEYGEN_CLASS_NAME, TimestampBasedAvroKeyGenerator.class.getName());
        LOG.info(
                "Table option [{}] is reset to {} because datetime partitioning turns on",
                FlinkOptions.KEYGEN_CLASS_NAME.key(),
                TimestampBasedAvroKeyGenerator.class.getName());
        if (fieldType.getTypeRoot() == LogicalTypeRoot.TIMESTAMP_WITHOUT_TIME_ZONE) {
            int precision = DataTypeUtils.precision(fieldType);
            if (precision == 0) {
                // seconds
                conf.setString(
                        TIMESTAMP_TYPE_FIELD_PROP,
                        TimestampBasedAvroKeyGenerator.TimestampType.UNIX_TIMESTAMP.name());
            } else if (precision == 3) {
                // milliseconds
                conf.setString(
                        TIMESTAMP_TYPE_FIELD_PROP,
                        TimestampBasedAvroKeyGenerator.TimestampType.EPOCHMILLISECONDS.name());
            }
            String outputPartitionFormat =
                    conf.getOptional(FlinkOptions.PARTITION_FORMAT)
                            .orElse(FlinkOptions.PARTITION_FORMAT_HOUR);
            conf.setString(TIMESTAMP_OUTPUT_DATE_FORMAT_PROP, outputPartitionFormat);
        } else {
            conf.setString(
                    TIMESTAMP_TYPE_FIELD_PROP,
                    TimestampBasedAvroKeyGenerator.TimestampType.SCALAR.name());
            conf.setString(KeyGeneratorOptions.Config.INPUT_TIME_UNIT, TimeUnit.DAYS.toString());

            String outputPartitionFormat =
                    conf.getOptional(FlinkOptions.PARTITION_FORMAT)
                            .orElse(FlinkOptions.PARTITION_FORMAT_DAY);
            conf.setString(TIMESTAMP_OUTPUT_DATE_FORMAT_PROP, outputPartitionFormat);
            // the option is actually useless, it only works for validation
            conf.setString(TIMESTAMP_INPUT_DATE_FORMAT_PROP, FlinkOptions.PARTITION_FORMAT_DAY);
        }
        conf.setString(TIMESTAMP_OUTPUT_TIMEZONE_FORMAT_PROP, "UTC");
    }

    /** Sets up the compaction options from the table definition. */
    private static void setupCompactionOptions(org.apache.flink.configuration.Configuration conf) {
        int commitsToRetain = conf.getInteger(FlinkOptions.CLEAN_RETAIN_COMMITS);
        int minCommitsToKeep = conf.getInteger(FlinkOptions.ARCHIVE_MIN_COMMITS);
        if (commitsToRetain >= minCommitsToKeep) {
            LOG.info(
                    "Table option [{}] is reset to {} to be greater than {}={},\n"
                            + "to avoid risk of missing data from few instants in incremental pull",
                    FlinkOptions.ARCHIVE_MIN_COMMITS.key(),
                    commitsToRetain + 10,
                    FlinkOptions.CLEAN_RETAIN_COMMITS.key(),
                    commitsToRetain);
            conf.setInteger(FlinkOptions.ARCHIVE_MIN_COMMITS, commitsToRetain + 10);
            conf.setInteger(FlinkOptions.ARCHIVE_MAX_COMMITS, commitsToRetain + 20);
        }
    }

    /** Sets up the read options from the table definition. */
    private static void setupReadOptions(org.apache.flink.configuration.Configuration conf) {
        if (OptionsResolver.isIncrementalQuery(conf)) {
            conf.setString(FlinkOptions.QUERY_TYPE, FlinkOptions.QUERY_TYPE_INCREMENTAL);
        }
    }

    /** Sets up the write options from the table definition. */
    private static void setupWriteOptions(org.apache.flink.configuration.Configuration conf) {
        if (FlinkOptions.isDefaultValueDefined(conf, FlinkOptions.OPERATION)
                && OptionsResolver.isCowTable(conf)) {
            conf.setBoolean(FlinkOptions.PRE_COMBINE, true);
        }
    }

    /**
     * Inferences the deserialization Avro schema from the table schema (e.g. the DDL) if both
     * options {@link FlinkOptions#SOURCE_AVRO_SCHEMA_PATH} and {@link
     * FlinkOptions#SOURCE_AVRO_SCHEMA} are not specified.
     *
     * @param conf The configuration
     * @param rowType The specified table row type
     */
    private static void inferAvroSchema(
            org.apache.flink.configuration.Configuration conf, LogicalType rowType) {
        if (!conf.getOptional(FlinkOptions.SOURCE_AVRO_SCHEMA_PATH).isPresent()
                && !conf.getOptional(FlinkOptions.SOURCE_AVRO_SCHEMA).isPresent()) {
            String inferredSchema = AvroSchemaConverter.convertToSchema(rowType).toString();
            conf.setString(FlinkOptions.SOURCE_AVRO_SCHEMA, inferredSchema);
        }
    }

    private static boolean isDatetimeType(LogicalType logicalType) {
        LogicalTypeRoot typeRoot = logicalType.getTypeRoot();
        return typeRoot == LogicalTypeRoot.TIMESTAMP_WITHOUT_TIME_ZONE
                || typeRoot == LogicalTypeRoot.DATE;
    }

    /**
     * The sanity check.
     *
     * @param conf The table options
     * @param columnNameList The selected column name list
     */
    public static void sanityCheck(
            org.apache.flink.configuration.Configuration conf, List<String> columnNameList) {
        checkTableType(conf);

        if (!OptionsResolver.isAppendMode(conf)) {
            checkRecordKey(conf, columnNameList);
            checkPreCombineKey(conf, columnNameList);
        }
    }

    private static void checkTableType(org.apache.flink.configuration.Configuration conf) {
        String tableType = conf.get(FlinkOptions.TABLE_TYPE);
        if (org.apache.hudi.common.util.StringUtils.nonEmpty(tableType)) {
            try {
                HoodieTableType.valueOf(tableType);
            } catch (IllegalArgumentException e) {
                throw new HoodieValidationException(
                        "Invalid table type: "
                                + tableType
                                + ". Table type should be either "
                                + HoodieTableType.MERGE_ON_READ
                                + " or "
                                + HoodieTableType.COPY_ON_WRITE
                                + ".");
            }
        }
    }

    /** Validate the record key. */
    private static void checkRecordKey(
            org.apache.flink.configuration.Configuration conf, List<String> columnNameList) {
        String[] recordKeys = conf.get(FlinkOptions.RECORD_KEY_FIELD).split(",");
        if (recordKeys.length == 1
                && FlinkOptions.RECORD_KEY_FIELD.defaultValue().equals(recordKeys[0])
                && !columnNameList.contains(recordKeys[0])) {
            throw new HoodieValidationException(
                    "Primary key definition is required, the default primary key field "
                            + "'"
                            + FlinkOptions.RECORD_KEY_FIELD.defaultValue()
                            + "' does not exist in the table schema, "
                            + "use either PRIMARY KEY syntax or option '"
                            + FlinkOptions.RECORD_KEY_FIELD.key()
                            + "' to speciy.");
        }

        Arrays.stream(recordKeys)
                .filter(field -> !columnNameList.contains(field))
                .findAny()
                .ifPresent(
                        f -> {
                            throw new HoodieValidationException(
                                    "Field '"
                                            + f
                                            + "' specified in option "
                                            + "'"
                                            + FlinkOptions.RECORD_KEY_FIELD.key()
                                            + "' does not exist in the table schema.");
                        });
    }

    /** Validate pre_combine key. */
    private static void checkPreCombineKey(
            org.apache.flink.configuration.Configuration conf, List<String> columnNameList) {
        String preCombineField = conf.get(FlinkOptions.PRECOMBINE_FIELD);
        if (!columnNameList.contains(preCombineField)) {
            if (OptionsResolver.isDefaultHoodieRecordPayloadClazz(conf)) {
                throw new HoodieValidationException(
                        "Option '"
                                + FlinkOptions.PRECOMBINE_FIELD.key()
                                + "' is required for payload class: "
                                + DefaultHoodieRecordPayload.class.getName());
            }
            if (preCombineField.equals(FlinkOptions.PRECOMBINE_FIELD.defaultValue())) {
                conf.setString(FlinkOptions.PRECOMBINE_FIELD, FlinkOptions.NO_PRE_COMBINE);
            } else if (!preCombineField.equals(FlinkOptions.NO_PRE_COMBINE)) {
                throw new HoodieValidationException(
                        "Field "
                                + preCombineField
                                + " does not exist in the table schema."
                                + "Please check '"
                                + FlinkOptions.PRECOMBINE_FIELD.key()
                                + "' option.");
            }
        }
    }

    public static Map<String, Object> getDefaultHdpConfig() {
        Configuration defaultHadoopConf = FlinkClientUtil.getHadoopConf();
        Map<String, Object> confMap = new HashMap<>();
        for (Map.Entry<String, String> entry : defaultHadoopConf) {
            confMap.put(entry.getKey(), entry.getValue());
        }
        return confMap;
    }
}
