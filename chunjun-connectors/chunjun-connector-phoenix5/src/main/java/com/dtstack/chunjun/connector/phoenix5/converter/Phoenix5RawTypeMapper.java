/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.phoenix5.converter;

import com.dtstack.chunjun.conf.TypeConfig;
import com.dtstack.chunjun.throwable.UnsupportedTypeException;

import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.types.DataType;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/9 16:01 星期五
 * @email <EMAIL>
 * @company www.dtstack.com
 */
public class Phoenix5RawTypeMapper {

    /*
     * JDBC ResultSet implementation of Phoenix.
     * Currently only the following data types are supported:
     * - String
     * - Date
     * - Time
     * - Timestamp
     * - BigDecimal
     * - Double
     * - Float
     * - Int
     * - Short
     * - Long
     * - Binary
     * - Array - 1D
     */

    /**
     * Convert the type in the Phoenix5 database to the DataType type of flink. The conversion
     * relationship refers to the information in the {@link org.apache.phoenix.jdbc.PhoenixResultSet
     * } class.
     *
     * @param type
     * @return
     */
    public static DataType apply(TypeConfig type) {
        switch (type.getType()) {
            case "BOOLEAN":
            case "BIT":
                return DataTypes.BOOLEAN();
            case "TINYINT":
            case "UNSIGNED_TINYINT":
                return DataTypes.TINYINT();
            case "SMALLINT":
                return DataTypes.SMALLINT();
            case "MEDIUMINT":
            case "INT":
            case "INTEGER":
            case "INT24":
            case "UNSIGNED_INT":
                return DataTypes.INT();
            case "BIGINT":
            case "UNSIGNED_LONG":
                return DataTypes.BIGINT();
            case "REAL":
            case "FLOAT":
            case "UNSIGNED_FLOAT":
                return DataTypes.FLOAT();
            case "DECIMAL":
            case "NUMERIC":
                return DataTypes.DECIMAL(38, 18);
            case "DOUBLE":
            case "UNSIGNED_DOUBLE":
                return DataTypes.DOUBLE();
            case "CHAR":
            case "VARCHAR":
            case "STRING":
            case "TINYTEXT":
            case "TEXT":
            case "MEDIUMTEXT":
            case "LONGTEXT":
            case "JSON":
            case "ENUM":
            case "SET":
                return DataTypes.STRING();
            case "DATE":
            case "UNSIGNED_DATE":
                return DataTypes.DATE();
            case "TIME":
            case "UNSIGNED_TIME":
                return DataTypes.TIME();
            case "YEAR":
                return DataTypes.INTERVAL(DataTypes.YEAR());
            case "TIMESTAMP":
            case "UNSIGNED_TIMESTAMP":
                return DataTypes.TIMESTAMP();
            case "DATETIME":
                return DataTypes.TIMESTAMP(0);
            case "TINYBLOB":
            case "BLOB":
            case "MEDIUMBLOB":
            case "LONGBLOB":
            case "BINARY":
            case "VARBINARY":
            case "GEOMETRY":
                // BYTES 底层调用的是VARBINARY最大长度
                return DataTypes.BYTES();

            default:
                throw new UnsupportedTypeException(type);
        }
    }
}
