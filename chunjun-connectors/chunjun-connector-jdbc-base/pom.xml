<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>chunjun-connectors</artifactId>
		<groupId>com.dtstack.chunjun</groupId>
		<version>2.0-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>chunjun-connector-jdbc-base</artifactId>
	<name>ChunJun : Connectors : JDBC</name>

	<properties>

	</properties>

	<dependencies>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-connector-jdbc-mysql</artifactId>
			<version>3.3.0-2.0-dt-0.2</version>
		</dependency>
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<createDependencyReducedPom>false</createDependencyReducedPom>
							<transformers>
								<transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
									<mainClass>com.dtstack.chunjun.core.Main</mainClass>
								</transformer>
							</transformers>
							<artifactSet>
<!--								<includes>-->
<!--									<include>com.google.guava:*</include>-->
<!--									<include>com.google.code.gson:*</include>-->
<!--									<include>ch.qos.logback:*</include>-->
<!--									<include>org.slf4j:*</include>-->
<!--									<include>org.apache.httpcomponents:*</include>-->
<!--									<include>io.prometheus:*</include>-->
<!--									<include>org.apache.avro:*</include>-->
<!--									<include>com.fasterxml.jackson.core:*</include>-->
<!--									<include>commons-*:*</include>-->
<!--									<include>org.apache.iceberg:*</include>-->
<!--									<include>com.dtstack.flinkx:*</include>-->
<!--									<include>org.apache.flink:*</include>-->
<!--								</includes>-->
							</artifactSet>
							<relocations>

							</relocations>
						</configuration>
					</execution>
				</executions>
			</plugin>

		</plugins>
	</build>
</project>
