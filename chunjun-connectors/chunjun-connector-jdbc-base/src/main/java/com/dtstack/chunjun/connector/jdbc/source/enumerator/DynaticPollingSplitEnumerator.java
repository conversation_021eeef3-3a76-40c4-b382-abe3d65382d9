/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.jdbc.source.enumerator;

import com.dtstack.chunjun.connector.jdbc.source.split.PollingSqlInfo;

import org.apache.flink.annotation.VisibleForTesting;
import org.apache.flink.connector.jdbc.core.database.dialect.JdbcDialect;
import org.apache.flink.connector.jdbc.core.datastream.source.enumerator.JdbcSourceEnumerator;
import org.apache.flink.connector.jdbc.core.datastream.source.enumerator.JdbcSqlSplitEnumeratorBase;
import org.apache.flink.connector.jdbc.core.datastream.source.split.CheckpointedOffset;
import org.apache.flink.connector.jdbc.core.datastream.source.split.JdbcSourceSplit;
import org.apache.flink.connector.jdbc.split.JdbcParameterValuesProvider;
import org.apache.flink.util.Preconditions;
import org.apache.flink.util.StringUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.function.Supplier;

/** A split enumerator based on sql-parameters grains. */
public final class DynaticPollingSplitEnumerator
        extends JdbcSqlSplitEnumeratorBase<JdbcSourceSplit> {

    public static final Logger LOG = LoggerFactory.getLogger(DynaticPollingSplitEnumerator.class);

    private final PollingSqlInfo pollingSqlInfo;
    private final JdbcDialect jdbcDialect;
    private PollingSplitState pollingSplitState;
    private IncrementKeyConverter incrementKeyConverter;

    public DynaticPollingSplitEnumerator(
            Serializable userDefinedState,
            PollingSqlInfo sqlTemplate,
            JdbcDialect jdbcDialect,
            IncrementKeyConverter incrementKeyConverter) {
        super(userDefinedState);
        this.pollingSqlInfo = Preconditions.checkNotNull(sqlTemplate);
        this.jdbcDialect = Preconditions.checkNotNull(jdbcDialect);
        this.incrementKeyConverter = Preconditions.checkNotNull(incrementKeyConverter);
    }

    @Override
    @Nonnull
    public List<JdbcSourceSplit> enumerateSplits(@Nonnull Supplier<Boolean> splitGettable)
            throws RuntimeException {

        if (!splitGettable.get()) {
            LOG.info(
                    "The current split is over max splits capacity of {}.",
                    JdbcSourceEnumerator.class.getSimpleName());
            return Collections.emptyList();
        }

        if (pollingSplitState == null
                || pollingSplitState.optionalSqlSplitEnumeratorState == null) {
            return Collections.singletonList(
                    new JdbcSourceSplit(
                            getNextId(), getSqlTemplate(), null, new CheckpointedOffset()));
        }

        if (pollingSplitState.isHasCreateSplit()) {
            LOG.info(
                    "The polling position [{}] has create split {}.",
                    pollingSplitState.optionalSqlSplitEnumeratorState,
                    JdbcSourceEnumerator.class.getSimpleName());
            return Collections.emptyList();
        }

        Serializable[] paramArr = new Serializable[1];
        paramArr[0] =
                incrementKeyConverter.convertToLocationValue(
                        pollingSplitState.optionalSqlSplitEnumeratorState);
        updateHasCreateSplit();
        return Collections.singletonList(
                new JdbcSourceSplit(
                        getNextId(), getSqlTemplate(), paramArr, new CheckpointedOffset()));
    }

    public String getSqlTemplate() {
        String orderName =
                incrementKeyConverter.convertColumnOrderName(
                        pollingSqlInfo.getPollingColumn(), jdbcDialect);
        if (optionalSqlSplitEnumeratorState != null) {
            if (pollingSqlInfo.isHasWhere()) {
                return pollingSqlInfo.getSqlTemplate()
                        + " AND "
                        + jdbcDialect.quoteIdentifier(pollingSqlInfo.getPollingColumn())
                        + " > ?"
                        + " order by "
                        + orderName
                        + " ASC";
            } else {
                return pollingSqlInfo.getSqlTemplate()
                        + " WHERE "
                        + jdbcDialect.quoteIdentifier(pollingSqlInfo.getPollingColumn())
                        + " > ?"
                        + " order by "
                        + orderName
                        + " ASC";
            }
        }
        return pollingSqlInfo.getSqlTemplate() + " order by " + orderName + " ASC";
    }

    @VisibleForTesting
    public JdbcParameterValuesProvider getParameterValuesProvider() {
        return null;
    }

    public void updateState(Serializable optionalSqlSplitEnumeratorState) {
        this.pollingSplitState = new PollingSplitState(optionalSqlSplitEnumeratorState, false);
        this.optionalSqlSplitEnumeratorState = pollingSplitState;
    }

    public void updateHasCreateSplit() {
        this.pollingSplitState =
                new PollingSplitState(pollingSplitState.optionalSqlSplitEnumeratorState, true);
        this.optionalSqlSplitEnumeratorState = pollingSplitState;
    }

    public Serializable optionalSqlSplitEnumeratorState() {
        return optionalSqlSplitEnumeratorState;
    }

    public static class PollingSplitState implements Serializable {
        private final Serializable optionalSqlSplitEnumeratorState;
        private final boolean hasCreateSplit;

        public PollingSplitState(
                Serializable optionalSqlSplitEnumeratorState, boolean hasCreateSplit) {
            this.optionalSqlSplitEnumeratorState = optionalSqlSplitEnumeratorState;
            this.hasCreateSplit = hasCreateSplit;
        }

        public Serializable getOptionalSqlSplitEnumeratorState() {
            return optionalSqlSplitEnumeratorState;
        }

        public boolean isHasCreateSplit() {
            return hasCreateSplit;
        }
    }

    /**
     * The {@link DynaticPollingSplitEnumeratorProvider} for {@link DynaticPollingSplitEnumerator}.
     */
    public static class DynaticPollingSplitEnumeratorProvider implements Provider<JdbcSourceSplit> {

        private PollingSqlInfo pollingSqlInfo;

        private @Nullable Serializable optionalSqlSplitEnumeratorState;

        private JdbcDialect jdbcDialect;
        private IncrementKeyConverter incrementKeyConverter;

        public DynaticPollingSplitEnumeratorProvider setSqlInfo(PollingSqlInfo pollingSqlInfo) {
            Preconditions.checkArgument(
                    !StringUtils.isNullOrWhitespaceOnly(pollingSqlInfo.getSqlTemplate()),
                    "sqlTemplate must not be empty.");
            this.pollingSqlInfo = pollingSqlInfo;
            return this;
        }

        public DynaticPollingSplitEnumeratorProvider setOptionalSqlSplitEnumeratorState(
                Serializable optionalSqlSplitEnumeratorState) {
            this.optionalSqlSplitEnumeratorState = optionalSqlSplitEnumeratorState;
            return this;
        }

        public DynaticPollingSplitEnumeratorProvider setJdbcDialect(JdbcDialect jdbcDialect) {
            this.jdbcDialect = jdbcDialect;
            return this;
        }

        public DynaticPollingSplitEnumeratorProvider setIncrementKeyConverter(
                IncrementKeyConverter incrementKeyConverter) {
            this.incrementKeyConverter = incrementKeyConverter;
            return this;
        }

        @Override
        public JdbcSqlSplitEnumeratorBase<JdbcSourceSplit> create() {
            return new DynaticPollingSplitEnumerator(
                    this.optionalSqlSplitEnumeratorState,
                    pollingSqlInfo,
                    jdbcDialect,
                    incrementKeyConverter);
        }

        @Override
        public JdbcSqlSplitEnumeratorBase<JdbcSourceSplit> restore(
                @Nullable Serializable optionalSqlSplitEnumeratorState) {
            return new DynaticPollingSplitEnumerator(
                    optionalSqlSplitEnumeratorState,
                    pollingSqlInfo,
                    jdbcDialect,
                    incrementKeyConverter);
        }
    }
}
