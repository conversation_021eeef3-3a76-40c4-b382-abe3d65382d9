package com.dtstack.chunjun.connector.jdbc.source.reader.extra;

import org.apache.flink.connector.jdbc.core.datastream.source.reader.extractor.ResultExtractor;

import java.sql.ResultSet;
import java.sql.SQLException;

public class PollingSplitResultExtractor<T> implements ResultExtractor<T> {
    private final ResultExtractor<T> resultExtractor;
    private final int pollingColumnIndex;

    public PollingSplitResultExtractor(ResultExtractor<T> resultExtractor, int pollingColumnIndex) {
        this.resultExtractor = resultExtractor;
        this.pollingColumnIndex = pollingColumnIndex;
    }

    @Override
    public T extract(ResultSet resultSet) throws SQLException {
        return resultExtractor.extract(resultSet);
    }

    public Object getPollingOffset(ResultSet resultSet) throws SQLException {
        return resultSet.getObject(pollingColumnIndex);
    }
}
