/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.jdbc.source;

import org.apache.flink.cdc.common.annotation.Experimental;
import org.apache.flink.cdc.common.annotation.PublicEvolving;
import org.apache.flink.cdc.common.configuration.ConfigOption;
import org.apache.flink.cdc.common.configuration.ConfigOptions;

import java.time.Duration;

@PublicEvolving
public class JdbcDataSourceOptions {

    public static final ConfigOption<String> TABLE =
            ConfigOptions.key("table")
                    .stringType()
                    .noDefaultValue()
            ;
    public static final ConfigOption<String> SCHEMA =
            ConfigOptions.key("schema")
                    .stringType()
                    .noDefaultValue()
            ;

    public static final ConfigOption<String> URL =
            ConfigOptions.key("jdbcUrl")
                    .stringType()
                    .noDefaultValue()
            ;

    public static final ConfigOption<String> USERNAME =
            ConfigOptions.key("username")
                    .stringType()
                    .noDefaultValue()
            ;


    public static final ConfigOption<String> PASSWORD =
            ConfigOptions.key("password")
                    .stringType()
                    .noDefaultValue()
            ;

    public static final ConfigOption<String> WHERE =
            ConfigOptions.key("where")
                    .stringType()
                    .noDefaultValue()
            ;


    public static final ConfigOption<String> STARLOCATION =
            ConfigOptions.key("startLocation")
                    .stringType()
                    .noDefaultValue()
            ;
    public static final ConfigOption<String> INCRECOLUMNNAME =
            ConfigOptions.key("increColumn")
                    .stringType()
                    .noDefaultValue()
            ;


    public static final ConfigOption<Boolean> SPLIT_UPDATE =
            ConfigOptions.key("split")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription(
                            "The update type data is divided into update_before and update_after");
}
