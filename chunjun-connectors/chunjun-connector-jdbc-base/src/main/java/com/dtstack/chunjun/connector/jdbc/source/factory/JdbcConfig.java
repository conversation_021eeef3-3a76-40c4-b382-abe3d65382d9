package com.dtstack.chunjun.connector.jdbc.source.factory;

import com.dtstack.chunjun.conf.FieldConf;

import java.util.List;

public class JdbcConfig {
    List<FieldConf> fieldConfs;
    private String increColumn;
    private String where;
    private String url;
    private String schame;
    private String table;
    private String username;
    private String password;
    private String driverName;
    protected long pollingInterval = 5000;

    public JdbcConfig(
            List<FieldConf> fieldConfs,
            String increColumn,
            String where,
            String url,
            String schame,
            String table,
            String username,
            String password,
            String driverName,
            long pollingInterval) {
        this.fieldConfs = fieldConfs;
        this.increColumn = increColumn;
        this.where = where;
        this.url = url;
        this.schame = schame;
        this.table = table;
        this.username = username;
        this.password = password;
        this.driverName = driverName;
        this.pollingInterval = pollingInterval;
    }

    public List<FieldConf> getFieldConfs() {
        return fieldConfs;
    }

    public String getIncreColumn() {
        return increColumn;
    }

    public String getWhere() {
        return where;
    }

    public String getUrl() {
        return url;
    }

    public String getSchame() {
        return schame;
    }

    public String getTable() {
        return table;
    }

    public String getUsername() {
        return username;
    }

    public String getPassword() {
        return password;
    }

    public String getDriverName() {
        return driverName;
    }

    public long getPollingInterval() {
        return pollingInterval;
    }
}
