package com.dtstack.chunjun.connector.jdbc.source.reader;

import com.dtstack.chunjun.cdc.CdcRawTypeMapper;
import com.dtstack.chunjun.conf.FieldConf;

import com.dtstack.chunjun.converter.RawTypeMapper;

import org.apache.flink.api.connector.source.SourceOutput;
import org.apache.flink.cdc.common.event.CreateTableEvent;
import org.apache.flink.cdc.common.event.TableId;
import org.apache.flink.cdc.common.schema.Schema;
import org.apache.flink.cdc.common.types.DataType;
import org.apache.flink.connector.base.source.reader.RecordEmitter;
import org.apache.flink.connector.jdbc.core.datastream.source.split.JdbcSourceSplit;
import org.apache.flink.connector.jdbc.core.datastream.source.split.JdbcSourceSplitState;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class JdbcEventEmitter<T, SplitT extends JdbcSourceSplit>
        implements RecordEmitter<RecordAndPollingSplitOffset<T>, T, JdbcSourceSplitState<SplitT>> {


    private CreateTableEvent event;

    public JdbcEventEmitter(CreateTableEvent createTableEvent) {
        this.event = createTableEvent;
    }


    @Override
    public void emitRecord(
            RecordAndPollingSplitOffset<T> element,
            SourceOutput<T> output,
            JdbcSourceSplitState<SplitT> splitState)
            throws Exception {
        if (event != null) {
            output.collect((T) event);
            event = null;
        }

        output.collect(element.getRecord());
        splitState.setPosition(
                element.getOffset(), element.getRecordSkipCount(), element.getPollingSplitOffset());
    }
}
