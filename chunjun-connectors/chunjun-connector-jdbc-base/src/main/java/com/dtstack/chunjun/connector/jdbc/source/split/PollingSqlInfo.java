package com.dtstack.chunjun.connector.jdbc.source.split;

public class PollingSqlInfo {
    private final String sqlTemplate;
    private final boolean hasWhere;
    private final String pollingColumn;
    private final String pollingColumnType;

    public PollingSqlInfo(
            String sqlTemplate, boolean hasWhere, String pollingColumn, String pollingColumnType) {
        this.sqlTemplate = sqlTemplate;
        this.hasWhere = hasWhere;
        this.pollingColumn = pollingColumn;
        this.pollingColumnType = pollingColumnType;
    }

    public String getSqlTemplate() {
        return sqlTemplate;
    }

    public boolean isHasWhere() {
        return hasWhere;
    }

    public String getPollingColumn() {
        return pollingColumn;
    }

    public String getPollingColumnType() {
        return pollingColumnType;
    }
}
