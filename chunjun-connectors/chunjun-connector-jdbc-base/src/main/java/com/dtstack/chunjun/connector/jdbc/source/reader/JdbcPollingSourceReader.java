/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.connector.jdbc.source.reader;

import com.dtstack.chunjun.cdc.CdcRawTypeMapper;
import com.dtstack.chunjun.connector.jdbc.source.MySqlDataSourceOptions;
import com.dtstack.chunjun.connector.jdbc.source.event.PollingSplitLocationEvent;

import org.apache.flink.api.connector.source.SourceReaderContext;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.connector.base.source.reader.SingleThreadMultiplexSourceReaderBase;
import org.apache.flink.connector.base.source.reader.splitreader.SplitReader;
import org.apache.flink.connector.jdbc.core.datastream.source.split.JdbcSourceSplit;
import org.apache.flink.connector.jdbc.core.datastream.source.split.JdbcSourceSplitState;

import java.util.Map;
import java.util.function.Supplier;

/**
 * The JDBC source reader.
 *
 * @param <OUT> The type of the record readed from the source.
 */
public class JdbcPollingSourceReader<OUT>
        extends SingleThreadMultiplexSourceReaderBase<
                RecordAndPollingSplitOffset<OUT>,
                OUT,
                JdbcSourceSplit,
                JdbcSourceSplitState<JdbcSourceSplit>> {

    public JdbcPollingSourceReader(
            Supplier<SplitReader<RecordAndPollingSplitOffset<OUT>, JdbcSourceSplit>>
                    splitReaderSupplier,
            Configuration config,
            SourceReaderContext context,
            CdcRawTypeMapper cdcRawTypeMapper
            ) {
        super(splitReaderSupplier, new JdbcEventEmitter<>(config.get(MySqlDataSourceOptions.SCHEMA),
                config.getValue(MySqlDataSourceOptions.TABLE),
                null,cdcRawTypeMapper), config, context);
    }

    @Override
    protected void onSplitFinished(
            Map<String, JdbcSourceSplitState<JdbcSourceSplit>> finishedSplitIds) {
        JdbcSourceSplitState<JdbcSourceSplit> next = finishedSplitIds.values().iterator().next();
        if (next.getExtraInfo() != null) {
            context.sendSourceEventToCoordinator(
                    new PollingSplitLocationEvent(next.getExtraInfo()));
        } else {
            Object[] parameters = next.toJdbcSourceSplit().getParameters();
            if (parameters != null && parameters.length == 1 && parameters[0] != null) {
                context.sendSourceEventToCoordinator(new PollingSplitLocationEvent(parameters[0]));
            }
        }
        context.sendSplitRequest();
    }

    @Override
    public void start() {
        // we request a split only if we did not get splits during the checkpoint restore
        if (getNumberOfCurrentlyAssignedSplits() == 0) {
            context.sendSplitRequest();
        }
    }

    @Override
    protected JdbcSourceSplitState<JdbcSourceSplit> initializedState(JdbcSourceSplit split) {
        return new JdbcSourceSplitState<>(split);
    }

    @Override
    protected JdbcSourceSplit toSplitType(
            String splitId, JdbcSourceSplitState<JdbcSourceSplit> splitState) {
        return splitState.toJdbcSourceSplit();
    }
}
