package com.dtstack.chunjun.connector.jdbc.source.enumerator;

import java.io.Serializable;
import java.math.BigInteger;
import java.sql.Date;

public class DateIncrementKeyConverter implements IncrementKeyConverter {
    @Override
    public Serializable convertToLocationValue(Serializable value) {
        if (value == null) {
            return null;
        }
        return BigInteger.valueOf(((Date) value).getTime());
    }
}
