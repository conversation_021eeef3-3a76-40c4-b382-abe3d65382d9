package com.dtstack.chunjun.connector.jdbc.source.enumerator;

import java.io.Serializable;
import java.math.BigInteger;

// varchar类型作为间隔轮询字段的前提是 字段值为数字类型
public class VarcharIncrementKeyConverter implements IncrementKeyConverter {
    @Override
    public Serializable convertToLocationValue(Serializable value) {
        if (value == null) {
            return null;
        }
        return BigInteger.valueOf(Long.parseLong(String.valueOf(value)));
    }
}
