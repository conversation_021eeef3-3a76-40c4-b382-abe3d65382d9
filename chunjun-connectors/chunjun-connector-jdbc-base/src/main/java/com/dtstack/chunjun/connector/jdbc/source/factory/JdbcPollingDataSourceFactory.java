package com.dtstack.chunjun.connector.jdbc.source.factory;

import com.dtstack.chunjun.cdc.CdcRawTypeMapper;

import com.dtstack.chunjun.connector.jdbc.source.JdbcDataSourceOptions;

import org.apache.flink.cdc.common.configuration.ConfigOption;
import org.apache.flink.cdc.common.configuration.Configuration;
import org.apache.flink.cdc.common.factories.DataSourceFactory;
import org.apache.flink.cdc.common.factories.FactoryHelper;
import org.apache.flink.cdc.common.source.DataSource;
import org.apache.flink.connector.jdbc.core.database.dialect.JdbcDialect;

import java.util.Set;

import static com.dtstack.chunjun.connector.jdbc.source.JdbcDataSourceOptions.HOSTNAME;
import static com.dtstack.chunjun.connector.jdbc.source.JdbcDataSourceOptions.PORT;

public abstract class JdbcPollingDataSourceFactory implements DataSourceFactory {
    public static final String PROPERTIES_PREFIX = "jdbc.properties.";

    @Override
    public DataSource createDataSource(Context context) {
        //todo参数类型的转换即可
        FactoryHelper.createFactoryHelper(this, context)
                .validateExcept(PROPERTIES_PREFIX);

        final Configuration config = context.getFactoryConfiguration();
        String url = config.get(JdbcDataSourceOptions.URL);
        int port = config.get(PORT);

        String username = config.get(USERNAME);
        String password = config.get(PASSWORD);
        String tables = config.get(TABLES);
        String tablesExclude = config.get(TABLES_EXCLUDE);

        JdbcConfig jdbcConfig = new JdbcConfig();

        return new JdbcSource(jdbcConfig, jdbcDialect(), cdcRawTypeMapper());
    }

    public abstract JdbcDialect jdbcDialect();

    public abstract CdcRawTypeMapper cdcRawTypeMapper();

    @Override
    public Set<ConfigOption<?>> requiredOptions() {
        return Set.of();
    }

    @Override
    public Set<ConfigOption<?>> optionalOptions() {
        return Set.of();
    }
}
