package com.dtstack.chunjun.connector.jdbc.source.factory;

import org.apache.flink.cdc.common.configuration.ConfigOption;
import org.apache.flink.cdc.common.factories.DataSourceFactory;
import org.apache.flink.cdc.common.source.DataSource;

import java.util.Set;

public abstract class JdbcPollingDataSourceFactory implements DataSourceFactory {

    @Override
    public DataSource createDataSource(Context context) {

        return null;
    }

    @Override
    public Set<ConfigOption<?>> requiredOptions() {
        return Set.of();
    }

    @Override
    public Set<ConfigOption<?>> optionalOptions() {
        return Set.of();
    }
}
