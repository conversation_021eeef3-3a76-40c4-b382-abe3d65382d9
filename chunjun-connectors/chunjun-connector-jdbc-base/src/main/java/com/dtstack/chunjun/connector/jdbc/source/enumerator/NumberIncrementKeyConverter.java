package com.dtstack.chunjun.connector.jdbc.source.enumerator;

import java.io.Serializable;
import java.math.BigDecimal;

public class NumberIncrementKeyConverter implements IncrementKeyConverter {
    @Override
    public Serializable convertToLocationValue(Serializable value) {
        if (value == null) {
            return null;
        }
        return new BigDecimal(String.valueOf(value)).toBigInteger();
    }
}
