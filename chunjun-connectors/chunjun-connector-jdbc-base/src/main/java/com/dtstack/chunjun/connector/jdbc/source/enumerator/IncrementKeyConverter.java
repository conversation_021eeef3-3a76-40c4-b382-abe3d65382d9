package com.dtstack.chunjun.connector.jdbc.source.enumerator;

import org.apache.flink.connector.jdbc.core.database.dialect.JdbcDialect;

import java.io.Serializable;

public interface IncrementKeyConverter {
    Serializable convertToLocationValue(Serializable value);

    default String convertColumnOrderName(String pollingName, JdbcDialect jdbcDialect) {
        return jdbcDialect.quoteIdentifier(pollingName);
    }
}
