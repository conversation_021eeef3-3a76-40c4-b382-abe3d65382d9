package com.dtstack.chunjun.connector.jdbc.source.factory;

import com.dtstack.chunjun.cdc.CdcRawTypeMapper;
import com.dtstack.chunjun.conf.FieldConf;
import com.dtstack.chunjun.connector.jdbc.source.JdbcPollingSource;
import com.dtstack.chunjun.connector.jdbc.source.enumerator.DateIncrementKeyConverter;
import com.dtstack.chunjun.connector.jdbc.source.enumerator.IncrementKeyConverter;
import com.dtstack.chunjun.connector.jdbc.source.enumerator.NumberIncrementKeyConverter;
import com.dtstack.chunjun.connector.jdbc.source.enumerator.TimeStampIncrementKeyConverter;
import com.dtstack.chunjun.connector.jdbc.source.reader.extra.EventResultExtractor;
import com.dtstack.chunjun.connector.jdbc.source.reader.extra.PollingSplitResultExtractor;
import com.dtstack.chunjun.enums.ColumnType;
import com.dtstack.chunjun.throwable.FlinkxRuntimeException;

import org.apache.flink.cdc.common.event.Event;
import org.apache.flink.cdc.common.source.DataSource;
import org.apache.flink.cdc.common.source.EventSourceProvider;
import org.apache.flink.cdc.common.source.FlinkSourceProvider;
import org.apache.flink.cdc.common.source.MetadataAccessor;
import org.apache.flink.cdc.runtime.typeutils.EventTypeInfo;
import org.apache.flink.connector.jdbc.core.database.dialect.JdbcDialect;
import org.apache.flink.connector.jdbc.utils.ContinuousUnBoundingSettings;

import org.apache.commons.lang3.StringUtils;

import java.time.Duration;
import java.util.stream.Collectors;

public class JdbcSource implements DataSource {

    private JdbcConfig jdbcConfig;
    private JdbcDialect jdbcDialect;
    private CdcRawTypeMapper cdcRawTypeMapper;

    public JdbcSource(JdbcConfig jdbcConfig, JdbcDialect jdbcDialect, CdcRawTypeMapper cdcRawTypeMapper) {
        this.jdbcConfig = jdbcConfig;
        this.jdbcDialect = jdbcDialect;
        this.cdcRawTypeMapper = cdcRawTypeMapper;
    }

    @Override
    public EventSourceProvider getEventSourceProvider() {
        String increColumn = jdbcConfig.getIncreColumn();
        int index = -1;
        String type = null;

        // 使用循环查找匹配的字段
        for (FieldConf fieldConf : jdbcConfig.getFieldConfs()) {
            if (fieldConf.getName().equals(increColumn)) {
                index = fieldConf.getIndex();
                type = fieldConf.getType();
                break;
            }
        }

        // 如果没有找到匹配的字段，抛出异常
        if (index == -1 || type == null) {
            throw new IllegalArgumentException(
                    "Increment column '" + increColumn + "' not found in field configurations");
        }


        JdbcPollingSource<Event> source =
                JdbcPollingSource.<Event>builder()
                        .setSqlInfo(
                                buildSql(jdbcConfig, jdbcDialect),
                                StringUtils.isNoneBlank(jdbcConfig.getWhere()),
                                increColumn,
                                type)
                        .setResultExtractor(
                                new PollingSplitResultExtractor<Event>(
                                        new EventResultExtractor(), index))
                        .setDBUrl(jdbcConfig.getUrl())
                        .setDriverName(
                                StringUtils.isNotBlank(jdbcConfig.getDriverName())
                                        ? jdbcConfig.getDriverName()
                                        : jdbcDialect.defaultDriverName().get())
                        .setPassword(jdbcConfig.getPassword())
                        .setUsername(jdbcConfig.getUsername())
                        .setTypeInformation(new EventTypeInfo())
                        .setContinuousUnBoundingSettings(
                                new ContinuousUnBoundingSettings(
                                        Duration.ofSeconds(1),
                                        Duration.ofMillis(jdbcConfig.pollingInterval)))
                        .setIncrementKeyConverter(incrementKeyConverter(increColumn, type))
                        .setCdcRawTypeMapper(cdcRawTypeMapper)
                        .build();
        return FlinkSourceProvider.of(source);
    }

    @Override
    public MetadataAccessor getMetadataAccessor() {
        return null;
    }

    public IncrementKeyConverter incrementKeyConverter(String incrementName, String incrementType) {
        switch (ColumnType.getType(incrementType)) {
            case TIMESTAMP:
            case DATETIME:
                return new TimeStampIncrementKeyConverter();
            case DATE:
                return new DateIncrementKeyConverter();
            default:
                if (ColumnType.isNumberType(incrementType)) {
                    return new NumberIncrementKeyConverter();
                } else {
                    throw new FlinkxRuntimeException(
                            String.format(
                                    "Unsupported incrementColumnType [%s], incrementColumnName [%s]",
                                    incrementType, incrementName));
                }
        }
    }

    private String buildSql(JdbcConfig jdbcConfig, JdbcDialect jdbcDialect) {
        StringBuilder sqlBuilder = new StringBuilder("SELECT");
        sqlBuilder.append(
                jdbcConfig.getFieldConfs().stream()
                        .map(i -> jdbcDialect.quoteIdentifier(i.getName()))
                        .collect(Collectors.joining(",")));
        sqlBuilder.append(" FROM ");
        sqlBuilder.append(jdbcDialect.quoteIdentifier(jdbcConfig.getSchame()));
        sqlBuilder.append(".");
        sqlBuilder.append(jdbcDialect.quoteIdentifier(jdbcConfig.getTable()));
        if (StringUtils.isNotBlank(jdbcConfig.getWhere())) {
            sqlBuilder.append(" WHERE ");
            sqlBuilder.append(jdbcConfig.getWhere());
        }

        return sqlBuilder.toString();
    }
}
