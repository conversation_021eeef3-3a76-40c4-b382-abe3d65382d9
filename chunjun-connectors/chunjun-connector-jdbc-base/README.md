## 关于sharding-proxy
http://zenpms.dtstack.cn/zentao/task-view-10146.html
目前只做过mysql的sharding-proxy
### 关于autoCommit
JdbcInputFormat默认把autoCommit设置成false的原因是：autoCommit=false是某些驱动使用游标读取的前置条件，而mysql开启游标读取是不需要这个设置的</br>
对于sharding-proxy，在autoCommit=false时会报: Failed to switch schema, please terminate current transaction.
所以将mysql的autoCommit设置成true
### 获取元数据方式
标准获取方式是： dbConn.getMetaData().getTables(tableIdentify.getCatalog(), tableIdentify.getSchema(), tableIdentify.getTable(), null);</br>
例如：
我们获取chunjun库下的test表，mysql驱动实际上会执行show full tables from 'chunjun' like 'test'</br>
这个语句在标准mysql库中去执行，会正确地获取到chunjun库下的test表， 而相同情况下在sharding-proxy执行这个语句是无法正确获取到结果的.</br>
目前采取的方法是在table后面加一个百分号：show full tables from 'chunjun' like 'test%'</br>


