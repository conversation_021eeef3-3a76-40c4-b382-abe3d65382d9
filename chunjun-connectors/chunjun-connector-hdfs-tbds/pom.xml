<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>chunjun-connectors</artifactId>
		<groupId>com.dtstack.chunjun</groupId>
		<version>2.0-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>chunjun-connector-hdfs-tbds</artifactId>
	<name>ChunJun : Connectors : HDFS TBDS</name>

	<properties>
		<hive.version>1.1.1</hive.version>
		<hadoop.tbds.version>2.7.2-TBDS-5.1.3.0</hadoop.tbds.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.apache.parquet</groupId>
			<artifactId>parquet-hadoop</artifactId>
			<version>1.11.1</version>
			<scope>compile</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.hive</groupId>
			<artifactId>hive-exec</artifactId>
			<version>${hive.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>calcite-core</artifactId>
					<groupId>org.apache.calcite</groupId>
				</exclusion>
				<exclusion>
					<artifactId>calcite-avatica</artifactId>
					<groupId>org.apache.calcite</groupId>
				</exclusion>
				<exclusion>
					<artifactId>derby</artifactId>
					<groupId>org.apache.derby</groupId>
				</exclusion>
				<exclusion>
					<groupId>org.xerial.snappy</groupId>
					<artifactId>snappy-java</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-logging</artifactId>
					<groupId>commons-logging</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-lang</artifactId>
					<groupId>commons-lang</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-cli</artifactId>
					<groupId>commons-cli</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-io</artifactId>
					<groupId>commons-io</groupId>
				</exclusion>
				<exclusion>
					<artifactId>junit</artifactId>
					<groupId>junit</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-codec</artifactId>
					<groupId>commons-codec</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-httpclient</artifactId>
					<groupId>commons-httpclient</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hive</groupId>
			<artifactId>hive-serde</artifactId>
			<version>${hive.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.hadoop</groupId>
					<artifactId>hadoop-yarn-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.xerial.snappy</groupId>
					<artifactId>snappy-java</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpcore</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-lang</artifactId>
					<groupId>commons-lang</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-cli</artifactId>
					<groupId>commons-cli</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-logging</artifactId>
					<groupId>commons-logging</groupId>
				</exclusion>
				<exclusion>
					<artifactId>apache-log4j-extras</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-codec</artifactId>
					<groupId>commons-codec</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jackson-core-asl</artifactId>
					<groupId>org.codehaus.jackson</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jackson-mapper-asl</artifactId>
					<groupId>org.codehaus.jackson</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jsr305</artifactId>
					<groupId>com.google.code.findbugs</groupId>
				</exclusion>
				<exclusion>
					<artifactId>parquet-hadoop-bundle</artifactId>
					<groupId>com.twitter</groupId>
				</exclusion>
			</exclusions>
		</dependency>


		<!--for PARQUET-136 https://issues.apache.org/jira/browse/PARQUET-136 -->
		<dependency>
			<groupId>com.twitter</groupId>
			<artifactId>parquet-hadoop-bundle</artifactId>
			<version>1.6.0</version>
		</dependency>

		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-parquet</artifactId>
			<version>${flink.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-orc</artifactId>
			<version>${flink.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-json</artifactId>
			<version>${flink.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-common</artifactId>
			<version>${hadoop.tbds.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.hadoop</groupId>
					<artifactId>hadoop-yarn-api</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-slf4j-impl</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-web</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-core</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-1.2-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>netty-all</artifactId>
					<groupId>io.netty</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hive-common</artifactId>
					<groupId>org.apache.hive</groupId>
				</exclusion>
				<exclusion>
					<artifactId>parquet-hadoop-bundle</artifactId>
					<groupId>org.apache.parquet</groupId>
				</exclusion>
				<exclusion>
					<groupId>xerces</groupId>
					<artifactId>xercesImpl</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-client</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>curator-framework</artifactId>
					<groupId>org.apache.curator</groupId>
				</exclusion>
				<exclusion>
					<artifactId>zookeeper</artifactId>
					<groupId>org.apache.zookeeper</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpcore</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-cli</artifactId>
					<groupId>commons-cli</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-compress</artifactId>
					<groupId>org.apache.commons</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-lang</artifactId>
					<groupId>commons-lang</groupId>
				</exclusion>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
				<exclusion>
					<artifactId>gson</artifactId>
					<groupId>com.google.code.gson</groupId>
				</exclusion>
				<exclusion>
					<artifactId>avro</artifactId>
					<groupId>org.apache.avro</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-common</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-hadoop2-compat</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-server</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>tephra-hbase-compat-1.0</artifactId>
					<groupId>co.cask.tephra</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-hadoop-compat</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hive-metastore</artifactId>
					<groupId>org.apache.hive</groupId>
				</exclusion>
			</exclusions>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-hdfs</artifactId>
			<version>${hadoop.tbds.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.hadoop</groupId>
					<artifactId>hadoop-yarn-api</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-slf4j-impl</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-web</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-core</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-1.2-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>netty-all</artifactId>
					<groupId>io.netty</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hive-common</artifactId>
					<groupId>org.apache.hive</groupId>
				</exclusion>
				<exclusion>
					<artifactId>parquet-hadoop-bundle</artifactId>
					<groupId>org.apache.parquet</groupId>
				</exclusion>
				<exclusion>
					<groupId>xerces</groupId>
					<artifactId>xercesImpl</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-client</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>curator-framework</artifactId>
					<groupId>org.apache.curator</groupId>
				</exclusion>
				<exclusion>
					<artifactId>zookeeper</artifactId>
					<groupId>org.apache.zookeeper</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpcore</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-cli</artifactId>
					<groupId>commons-cli</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-compress</artifactId>
					<groupId>org.apache.commons</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-lang</artifactId>
					<groupId>commons-lang</groupId>
				</exclusion>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
				<exclusion>
					<artifactId>gson</artifactId>
					<groupId>com.google.code.gson</groupId>
				</exclusion>
				<exclusion>
					<artifactId>avro</artifactId>
					<groupId>org.apache.avro</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-common</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-hadoop2-compat</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-server</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>tephra-hbase-compat-1.0</artifactId>
					<groupId>co.cask.tephra</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hbase-hadoop-compat</artifactId>
					<groupId>org.apache.hbase</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>hive-metastore</artifactId>
					<groupId>org.apache.hive</groupId>
				</exclusion>
			</exclusions>
			<scope>provided</scope>
		</dependency>
	</dependencies>


	<repositories>
		<repository>
			<id>tbds_public</id>
			<name>tbds_public_repository</name>
			<url>https://tbdsrepo.cloud.tencent.com/repository/tbds/</url>
			<layout>default</layout>
		</repository>
	</repositories>


	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<filters>
								<filter>
									<artifact>org.apache.hive:hive-exec</artifact>
									<excludes>
										<exclude>parquet/**</exclude>
									</excludes>
								</filter>
							</filters>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-resources</id>
						<!-- here the phase you need -->
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<tasks>
								<copy todir="${basedir}/../../${dist.dir}/connector/tbdshdfs"
									  file="${basedir}/target/${project.artifactId}-${project.version}.jar"/>
								<!--suppress UnresolvedMavenProperty -->
								<move file="${basedir}/../../${dist.dir}/connector/tbdshdfs/${project.artifactId}-${project.version}.jar"
									  tofile="${basedir}/../../${dist.dir}/connector/tbdshdfs/${project.artifactId}-${git.branch}.jar"/>
								<delete>
									<!--suppress UnresolvedMavenProperty -->
									<fileset dir="${basedir}/../../${dist.dir}/connector/tbdshdfs/"
											 includes="${project.artifactId}-*.jar"
											 excludes="${project.artifactId}-${git.branch}.jar"/>
								</delete>
							</tasks>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
