<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>chunjun-connectors</artifactId>
		<groupId>com.dtstack.chunjun</groupId>
		<version>2.0-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>


	<inceptionYear>2021</inceptionYear>

	<name>ChunJun : Connectors : HBase2</name>
	<artifactId>chunjun-connector-hbase-2</artifactId>

	<properties>
		<hbase.version>2.2.3</hbase.version>
		<connector.dir>hbase2</connector.dir>
		<license.version>4.1</license.version>
		<hbase.dep.zookeeper.version>3.4.12</hbase.dep.zookeeper.version>
		<commons-crypto.version>1.0.0</commons-crypto.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.dtstack.chunjun</groupId>
			<artifactId>chunjun-connector-hbase-base</artifactId>
			<version>2.0-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<artifactId>log4j-1.2-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>


		<dependency>
			<groupId>org.apache.zookeeper</groupId>
			<artifactId>zookeeper</artifactId>
			<version>${hbase.dep.zookeeper.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.hbase</groupId>
			<artifactId>hbase-client</artifactId>
			<version>${hbase.version}</version>
			<exclusions>
				<!-- Remove unneeded dependency, which is conflicting with our jetty-util version. -->
				<exclusion>
					<groupId>org.mortbay.jetty</groupId>
					<artifactId>jetty-util</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.mortbay.jetty</groupId>
					<artifactId>jetty</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.mortbay.jetty</groupId>
					<artifactId>jetty-sslengine</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.mortbay.jetty</groupId>
					<artifactId>jsp-2.1</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.mortbay.jetty</groupId>
					<artifactId>jsp-api-2.1</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.mortbay.jetty</groupId>
					<artifactId>servlet-api-2.5</artifactId>
				</exclusion>
				<!-- Bug in hbase annotations, can be removed when fixed. See FLINK-2153. -->
				<exclusion>
					<groupId>org.apache.hbase</groupId>
					<artifactId>hbase-annotations</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sun.jersey</groupId>
					<artifactId>jersey-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.hadoop</groupId>
					<artifactId>hadoop-common</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.hadoop</groupId>
					<artifactId>hadoop-auth</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.hadoop</groupId>
					<artifactId>hadoop-annotations</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.hadoop</groupId>
					<artifactId>hadoop-mapreduce-client-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.hadoop</groupId>
					<artifactId>hadoop-client</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.hadoop</groupId>
					<artifactId>hadoop-hdfs</artifactId>
				</exclusion>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>zookeeper</artifactId>
					<groupId>org.apache.zookeeper</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-crypto</artifactId>
			<version>${commons-crypto.version}</version>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>com.mycila</groupId>
				<artifactId>license-maven-plugin</artifactId>
				<version>${license.version}</version>
				<configuration>
					<header>${project.basedir}/license.txt</header>
					<excludes>
						<exclude>**/*.ctrl</exclude>
						<exclude>**/*.dat</exclude>
						<exclude>ICLA</exclude>
						<exclude>KEYS</exclude>
						<exclude>NOTICE</exclude>
					</excludes>
					<mapping>
						<xml.vm>XML_STYLE</xml.vm>
					</mapping>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>com.mycila</groupId>
						<artifactId>license-maven-plugin-git</artifactId>
						<version>${license.version}</version>
					</dependency>
				</dependencies>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<createDependencyReducedPom>false</createDependencyReducedPom>
							<artifactSet>
								<excludes>
									<exclude>org.slf4j:slf4j-api</exclude>
									<exclude>log4j:log4j</exclude>
									<exclude>ch.qos.logback:*</exclude>
								</excludes>
							</artifactSet>
							<filters>
								<filter>
									<artifact>*:*</artifact>
									<excludes>
										<exclude>META-INF/*.SF</exclude>
										<exclude>META-INF/*.DSA</exclude>
										<exclude>META-INF/*.RSA</exclude>
									</excludes>
								</filter>
							</filters>
							<relocations>
								<relocation>
									<pattern>org.apache.hadoop.security.authentication.util</pattern>
									<shadedPattern>shade.hbase.org.apache.hadoop.security.authentication.util</shadedPattern>
								</relocation>
								<relocation>
									<pattern>org.jboss.netty</pattern>
									<shadedPattern>com.dtstack.chunjun.connector.hbase2.org.jboss.netty</shadedPattern>
								</relocation>
								<relocation>
									<pattern>org.apache.http</pattern>
									<shadedPattern>com.dtstack.chunjun.connector.hbase2.org.apache.http</shadedPattern>
								</relocation>
								<relocation>
									<pattern>com.google.protobuf</pattern>
									<shadedPattern>com.dtstack.chunjun.connector.hbase2.com.google.protobuf</shadedPattern>
								</relocation>
							</relocations>
						</configuration>
					</execution>
				</executions>
				<configuration>
					<outputFile>
						${basedir}/target/hbase-2/original-${project.artifactId}-${project.version}.jar
					</outputFile>
				</configuration>
			</plugin>

			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-resources</id>
						<!-- here the phase you need -->
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<tasks>
								<copy todir="${basedir}/../../${dist.dir}/connector/hbase2/"
									  file="${basedir}/target/hbase-2/original-${project.artifactId}-${project.version}.jar"/>
								<move file="${basedir}/../../${dist.dir}/connector/hbase2/original-${project.artifactId}-${project.version}.jar"
									  tofile="${basedir}/../../${dist.dir}/connector/hbase2/${project.artifactId}-${git.branch}.jar"/>
								<delete>
									<!--suppress UnresolvedMavenProperty -->
									<fileset dir="${basedir}/../../${dist.dir}/connector/hbase2/"
											 includes="${project.artifactId}-*.jar"
											 excludes="${project.artifactId}-${git.branch}.jar"/>
								</delete>
							</tasks>
						</configuration>
					</execution>
				</executions>
			</plugin>

		</plugins>
	</build>
</project>
