{"columnList": [{"format": "yyyy-MM-dd HH:mm:ss", "isCustomFormat": false, "data": "<PERSON><PERSON><PERSON>", "byteSize": 6, "type": "STRING"}, {"format": "yyyy-MM-dd HH:mm:ss", "isCustomFormat": false, "data": "<PERSON><PERSON><PERSON>", "byteSize": 6, "type": "STRING"}, {"format": "yyyy-MM-dd HH:mm:ss", "isCustomFormat": false, "data": "test_one", "byteSize": 8, "type": "STRING"}, {"data": 6956892869059809280, "byteSize": 19, "type": "BIGDECIMAL"}, {"precision": 6, "data": "2022-07-24 16:48:32", "byteSize": 8, "type": "TIMESTAMP"}, {"format": "yyyy-MM-dd HH:mm:ss", "isCustomFormat": false, "data": "INSERT", "byteSize": 6, "type": "STRING"}, {"data": 3, "byteSize": 1, "type": "BIGDECIMAL"}, {"format": "yyyy-MM-dd HH:mm:ss", "isCustomFormat": false, "data": "dd", "byteSize": 2, "type": "STRING"}, {"data": 3, "byteSize": 1, "type": "BIGDECIMAL"}], "header": {"database": 0, "schema": 1, "table": 2, "ts": 3, "opTime": 4, "type": 5, "id": 6, "name": 7, "column_3": 8}, "extHeader": ["schema", "database", "opTime", "type", "table", "ts"], "byteSize": 148, "kind": "INSERT"}