CREATE TABLE source
(
    id        int,
    name      STRING,
    PROCTIME AS PROCTIME()
) WITH (
      'connector' = 'clickhouse-x',
      'url' = '****************************************',
      'table-name' = 'student',
      'username' = 'default',
      'password' = 'dtstack123'
      );


CREATE TABLE lookup
(
    id            int,
    name   string
) with (
      'connector' = 'clickhouse-x',
      'url' = '****************************************',
      'table-name' = 'student_lookup',
      'username' = 'default',
      'password' = 'dtstack123',
      'lookup.cache-type' = 'lru'
      );


CREATE TABLE sink
(
    id            int,
    name   string
) with (
      'connector' = 'print'
      );

insert into sink
select a.id, b.name
from source a INNER JOIN lookup FOR SYSTEM_TIME AS OF a.PROCTIME AS b ON a.id = b.id;;
