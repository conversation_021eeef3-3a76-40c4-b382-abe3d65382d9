CREATE TABLE source (
    name String
) WITH (
      'connector' = 's3-x',
      'path' = 's3a://daishu-csp-test01/test/qianyi/',
      's3.access-key' ='xxxxxxxxxx',
      's3.secret-key' = 'xxxxxxxxxx',
      's3.endpoint'= 'https://xxxxxxx',
      'format' = 'csv'
      );

CREATE TABLE sink (
    name String
) WITH (
      'connector' = 's3-x',
      'path' = 's3a://daishu-csp-test01/qianyi/',
      'format' = 'json',
      'sink.rolling-policy.rollover-interval' = '10s',
      'sink.rolling-policy.rollover-interval' = '30min',
      'auto-compaction' = 'true',
      's3.access-key' ='xxxxxxxxxx',
      's3.secret-key' = 'xxxxxxxxxx',
      's3.endpoint'= 'https://xxxxxxx'
      );

insert into sink select name from source
