CREATE TABLE source
(
    catalogueType             varchar,
    nodePid             int,
    isGetFile           varchar
) WITH (
      'connector' = 'datagen',
      'number-of-rows' = '1'
      );

CREATE TABLE sink
(
    catalogueType             varchar,
    nodePid             int,
    isGetFile           varchar
) WITH (
      'connector' = 'http-x'
      ,'url' = 'http://127.0.0.1:33245/test/write/json'
      ,'method'='post'
      ,'dataSubject'='$.a.b.c'
      ,'sink.buffer-flush.interval'='12000'
      ,'sink.buffer-flush.max-rows'='10000'
      ,'rowKind'='true'
      ,'body'='{
                "c1": "1",
                "c2": "32dehwqyeuqrg"
      }'
      );

insert into sink
select *
from source u;
