-- {"id":100,"name":"lb james阿道夫","money":293.899778,"dateone":"2020-07-30 10:08:22","age":"33","datethree":"2020-07-30 10:08:22.123","datesix":"2020-07-30 10:08:22.123456","datenigth":"2020-07-30 10:08:22.123456789","dtdate":"2020-07-30","dttime":"10:08:22"}
CREATE TABLE source_mongo
(
    val_date      DATE,
    val_double    DOUBLE,
    val_int       INT,
    val_long      BIGINT,
    val_timestamp TIMESTAMP,
    PROCTIME AS PROCTIME()
) WITH (
      'connector' = 'mongodb-x',
      'uri' = 'mongodb://172.16.101.246:27017',
      'database' = 'flink_dev',
      'collection' = 'dim_int1'
      );

CREATE TABLE look_up
(
    val_date      DATE,
    val_double    DOUBLE,
    val_int       INT,
    val_long      BIGINT,
    val_timestamp TIMESTAMP
) WITH (
      'connector' = 'mongodb-x',
      'uri' = 'mongodb://172.16.101.246:27017',
      'database' = 'flink_dev',
      'collection' = 'dim_int1'
      );

CREATE TABLE sink
(
    val_date      DATE,
    val_double    DOUBLE,
    val_int       INT,
    val_long      BIGINT,
    val_timestamp TIMESTAMP
) WITH (
      'connector' = 'print'
      );
create
TEMPORARY view view_out
  as
select
      u.val_date
     , u.val_double
     , u.val_int
     , u.val_long
     , u.val_timestamp
from source_mongo u
         join look_up FOR SYSTEM_TIME AS OF u.PROCTIME AS s
              on u.val_int = s.val_int;

insert into sink
select *
from view_out;


