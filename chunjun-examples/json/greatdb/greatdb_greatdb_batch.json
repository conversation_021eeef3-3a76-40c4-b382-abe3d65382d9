{"job": {"content": [{"reader": {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameter": {"column": [{"name": "id", "type": "int"}, {"name": "name", "type": "string"}, {"name": "money", "type": "decimal"}, {"name": "aphone", "type": "bigint"}, {"name": "aqq", "type": "<PERSON><PERSON><PERSON>"}, {"name": "awechat", "type": "<PERSON><PERSON><PERSON>"}, {"name": "aincome", "type": "decimal"}, {"name": "abirthday", "type": "datetime"}, {"name": "atoday", "type": "date"}, {"name": "atimecurrent", "type": "time"}, {"name": "bphone", "type": "bigint"}, {"name": "bqq", "type": "<PERSON><PERSON><PERSON>"}, {"name": "bwe<PERSON>t", "type": "<PERSON><PERSON><PERSON>"}, {"name": "bincome", "type": "decimal"}, {"name": "bbirthday", "type": "datetime"}, {"name": "btoday", "type": "date"}, {"name": "btimecurrent", "type": "time"}, {"name": "raw_date", "type": "string", "value": "2014-12-12 14:24:16"}, {"name": "aboolean", "type": "boolean"}, {"name": "adouble", "type": "double"}, {"name": "afloat", "type": "float"}, {"name": "achar", "type": "char"}, {"name": "atinyint", "type": "tinyint"}], "username": "root", "password": "root", "connection": [{"jdbcUrl": ["*********************************************"], "table": ["baserow"]}]}}, "writer": {"name": "greatdbwriter", "parameter": {"username": "root", "password": "root", "connection": [{"jdbcUrl": "*********************************************", "table": ["baserow_sync"]}], "writeMode": "insert", "column": [{"name": "id", "type": "int"}, {"name": "name", "type": "string"}, {"name": "money", "type": "decimal"}, {"name": "aphone", "type": "bigint"}, {"name": "aqq", "type": "<PERSON><PERSON><PERSON>"}, {"name": "awechat", "type": "<PERSON><PERSON><PERSON>"}, {"name": "aincome", "type": "decimal"}, {"name": "abirthday", "type": "datetime"}, {"name": "atoday", "type": "date"}, {"name": "atimecurrent", "type": "time"}, {"name": "bphone", "type": "bigint"}, {"name": "bqq", "type": "<PERSON><PERSON><PERSON>"}, {"name": "bwe<PERSON>t", "type": "<PERSON><PERSON><PERSON>"}, {"name": "bincome", "type": "decimal"}, {"name": "bbirthday", "type": "datetime"}, {"name": "btoday", "type": "date"}, {"name": "btimecurrent", "type": "time"}, {"name": "raw_date", "type": "string"}, {"name": "aboolean", "type": "boolean"}, {"name": "adouble", "type": "double"}, {"name": "afloat", "type": "float"}, {"name": "achar", "type": "char"}, {"name": "atinyint", "type": "tinyint"}]}}}], "setting": {"speed": {"channel": 1, "bytes": 0}}}}