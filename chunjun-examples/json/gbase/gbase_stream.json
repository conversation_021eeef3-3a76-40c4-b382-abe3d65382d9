{"job": {"content": [{"reader": {"parameter": {"username": "dev", "password": "dev123", "connection": [{"jdbcUrl": ["******************************"], "schema": "dev_db", "table": ["sink"]}], "column": [{"name": "id", "type": "INT"}, {"name": "name", "type": "VARCHAR"}, {"name": "message", "type": "TEXT"}, {"name": "age", "type": "TINYINT"}, {"name": "money", "type": "DOUBLE"}, {"name": "price", "type": "DECIMAL"}, {"name": "todayTimestamp", "type": "TIMESTAMP"}, {"name": "todayDate", "type": "DATE"}, {"name": "todayTime", "type": "TIME"}]}, "name": "gbasereader"}, "writer": {"parameter": {"print": true}, "name": "streamwriter"}}], "setting": {"speed": {"channel": 1, "bytes": 0}}}}