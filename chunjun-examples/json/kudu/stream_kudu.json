{"job": {"content": [{"reader": {"parameter": {"sliceRecordCount": ["1"], "column": [{"name": "id", "type": "int"}, {"name": "name", "type": "string"}, {"name": "name_value", "type": "string"}]}, "name": "streamreader"}, "writer": {"parameter": {"flushMode": "manual_flush", "masterAddresses": "master:7051,tools:7051,worker:7051", "workerCount": 2, "bossCount": 1, "column": [{"name": "id", "type": "int"}, {"name": "name", "type": "string"}, {"name": "name_value", "type": "string"}], "batchInterval": 10000, "hadoopConfig": {"principal": "kudu/<EMAIL>", "krb5Conf": "krb5.conf", "hadoop.security.authentication": "<PERSON><PERSON><PERSON>", "remoteDir": "/home/<USER>/sftp/xiaohe_dir/kudu_krb", "ipc.client.fallback-to-simple-auth-allowed": true, "hadoop.security.authorization": true, "keytab": "kudu.keytab", "useLocalFile": false, "sftpConf": {"maxWaitMillis": "3600000", "minIdle": "16", "auth": "1", "isUsePool": "true", "timeout": "3000", "path": "/home/<USER>/sftp", "password": "password", "maxIdle": "16", "port": "22", "maxTotal": "16", "host": "************", "fileTimeout": "300000", "username": "admin"}}, "writeMode": "insert", "table": "flinkx_xiaohe_result"}, "name": "kudu<PERSON>"}}], "setting": {"restore": {"isRestore": false}, "errorLimit": {"record": 0}, "speed": {"channel": 1}}}}