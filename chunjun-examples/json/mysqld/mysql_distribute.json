{"job": {"content": [{"reader": {"parameter": {"column": [{"name": "id", "type": "BIGINT"}, {"name": "user_id", "type": "BIGINT"}, {"name": "name", "type": "VARCHAR"}], "connection": [{"password": "password", "jdbcUrl": ["***************************"], "table": ["table", "table1"], "username": "username"}, {"password": "password1", "jdbcUrl": ["***************************"], "table": ["table", "table1"], "username": "username1"}]}, "name": "mysqld<PERSON>er"}, "writer": {"parameter": {"print": true}, "name": "streamwriter"}}], "setting": {"speed": {"readerChannel": 1, "writerChannel": 1, "channel": 1}}}}