{"job": {"content": [{"reader": {"parameter": {"sliceRecordCount": [10], "column": [{"index": 0, "name": "id", "type": "int"}, {"index": 1, "name": "name", "type": "String"}, {"index": 2, "name": "age", "type": "int"}, {"index": 3, "name": "date", "type": "date"}, {"index": 4, "name": "timestamp", "type": "timestamp"}]}, "name": "streamreader"}, "writer": {"name": "hive3cdpwriter", "parameter": {"table": "liuliu_transaction_test", "schema": "liuliu", "hiveTransactionTable": true, "path": "hdfs://nameservice1/warehouse/tablespace/managed/hive/liuliu.db/liuliu_transaction_test", "defaultFS": "hdfs://nameservice1", "fieldDelimiter": ",", "encoding": "utf-8", "fileType": "ORC", "maxFileSize": 10485760, "nextCheckRows": 20000, "writeMode": "overwrite", "hadoopConfig": {"dfs.nameservices": "nameservice1", "fs.defaultFS": "hdfs://nameservice1", "dfs.ha.namenodes.nameservice1": "namenode33,namenode49", "dfs.namenode.rpc-address.nameservice1.namenode33": "node01:8020", "dfs.namenode.rpc-address.nameservice1.namenode49": "node02:8020", "dfs.client.failover.proxy.provider.nameservice1": "org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider", "fs.hdfs.impl.disable.cache": "true", "fs.hdfs.impl": "org.apache.hadoop.hdfs.DistributedFileSystem", "hadoop.security.authorization": "true", "hadoop.security.authentication": "kerber<PERSON>", "useLocalFile": "true", "principalFile": "hive.keytab", "principal": "hive/<EMAIL>", "java.security.krb5.conf": "krb5.conf", "yarn.mapreduce.framework.name": "yarn", "yarn.resourcemanager.ha.enabled": "true", "yarn.resourcemanager.principal": "yarn/<EMAIL>", "yarn.resourcemanager.ha.rm-ids": "rm42,rm55", "yarn.resourcemanager.address.rm42": "node01:8032", "yarn.resourcemanager.address.rm55": "node02:8032", "hadoop.rpc.protection": "privacy", "dfs.data.transfer.protection": "privacy", "hive.metastore.sasl.enabled": "true", "hive.metastore.uris": "thrift://node01:9083", "hive.metastore.kerberos.principal": "hive/<EMAIL>", "hive.metastore.warehouse.dir": "/warehouse/tablespace/managed/hive", "hive.metastore.warehouse.external.dir": "/warehouse/tablespace/external/hive", "hive.metastore.execute.setugi": "true", "hive.metastore.dml.events": "true", "hive.metastore.transactional.event.listeners": "org.apache.hive.hcatalog.listener.DbNotificationListener"}, "column": [{"index": 0, "name": "id", "type": "int"}, {"index": 1, "name": "name", "type": "String"}, {"index": 2, "name": "age", "type": "int"}, {"index": 3, "name": "date", "type": "date"}, {"index": 4, "name": "timestamp", "type": "timestamp"}]}}}], "setting": {"speed": {"channel": 1, "bytes": 0}}}}