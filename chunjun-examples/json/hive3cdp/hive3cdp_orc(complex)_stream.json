{"job": {"content": [{"reader": {"parameter": {"defaultFS": "hdfs://ns1", "encoding": "utf-8", "fileType": "orc", "path": "hdfs://ns1/dtInsight/hive/warehouse/wujuan.db/wujuan_complex_type", "fileName": "", "hadoopConfig": {"dfs.nameservices": "nameservice1", "fs.defaultFS": "hdfs://nameservice1", "dfs.ha.namenodes.nameservice1": "namenode33,namenode49", "dfs.namenode.rpc-address.nameservice1.namenode33": "node01:8020", "dfs.namenode.rpc-address.nameservice1.namenode49": "node02:8020", "dfs.client.failover.proxy.provider.nameservice1": "org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider", "fs.hdfs.impl.disable.cache": "true", "fs.hdfs.impl": "org.apache.hadoop.hdfs.DistributedFileSystem", "hadoop.security.authorization": "true", "hadoop.security.authentication": "kerber<PERSON>", "useLocalFile": "true", "principalFile": "hdfs.keytab", "principal": "hdfs/<EMAIL>", "java.security.krb5.conf": "krb5.conf", "yarn.mapreduce.framework.name": "yarn", "yarn.resourcemanager.ha.enabled": "true", "yarn.resourcemanager.principal": "yarn/<EMAIL>", "yarn.resourcemanager.ha.rm-ids": "rm42,rm55", "yarn.resourcemanager.address.rm42": "node01:8032", "yarn.resourcemanager.address.rm55": "node02:8032", "hadoop.rpc.protection": "privacy", "dfs.data.transfer.protection": "privacy"}, "column": [{"name": "id", "type": "int"}, {"name": "name_struct", "type": "struct<name:string,age:int>"}, {"name": "name_map", "type": "map<string,string>"}, {"name": "name_array", "type": "array<string>"}]}, "name": "hive3cdpreader"}, "writer": {"name": "streamwriter", "parameter": {"print": true}}}], "setting": {"speed": {"channel": 1, "bytes": 0}}}}