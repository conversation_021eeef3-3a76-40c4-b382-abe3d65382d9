{"job": {"content": [{"reader": {"parameter": {"databaseName": "db_test", "tableList": ["test.shifang2"], "username": "test", "password": "Abc12345", "cat": "insert,delete,update", "url": "****************************************************", "splitUpdate": false, "pavingData": true, "pollInterval": 1000, "column": [{"name": "id", "type": "int"}, {"name": "name", "type": "<PERSON><PERSON><PERSON>"}, {"name": "day1", "type": "date"}, {"name": "day2", "type": "timestamp"}]}, "table": {"tableName": "sourceTable"}, "name": "sqlservercdcreader"}, "transformer": {"transformSql": "select id,name,day1,day2 from sourceTable where id >10"}, "writer": {"parameter": {"print": true, "column": [{"name": "id", "type": "int"}, {"name": "name", "type": "<PERSON><PERSON><PERSON>"}, {"name": "day1", "type": "date"}, {"name": "day2", "type": "timestamp"}]}, "table": {"tableName": "sinkTable"}, "name": "streamwriter"}}], "setting": {"speed": {"bytes": 0, "channel": 1}}}}