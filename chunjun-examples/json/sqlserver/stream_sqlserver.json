{"job": {"content": [{"reader": {"parameter": {"sliceRecordCount": ["100"], "column": [{"name": "id", "type": "id"}, {"name": "name", "type": "string"}]}, "name": "streamreader"}, "writer": {"name": "sqlserverwriter", "parameter": {"connection": [{"jdbcUrl": "*********************************************************", "table": ["test"], "schema": "simple"}], "username": "username", "password": "password", "column": [{"name": "id", "type": "int"}, {"name": "name", "type": "<PERSON><PERSON><PERSON>"}], "mode": "insert", "batchSize": 1024, "preSql": [], "postSql": [], "updateKey": []}}}], "setting": {"speed": {"channel": 1, "bytes": 0}, "errorLimit": {"record": 100}}}}