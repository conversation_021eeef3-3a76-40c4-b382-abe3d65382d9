{"job": {"content": [{"reader": {"parameter": {"username": "SYSTEM", "password": "Abc!@#579", "connection": [{"jdbcUrl": ["**************************"], "?table": "表名区分大小写", "table": ["T_CHARACTER"]}], "column": [{"name": "t_varchar", "type": "decimal"}, {"name": "t_nvarchar", "type": "decimal"}, {"name": "t_alphanum", "type": "string"}, {"name": "t_shorttext", "type": "string"}]}, "name": "sapha<PERSON><PERSON><PERSON>"}, "writer": {"name": "saphana<PERSON>", "parameter": {"mode": "update", "updateKey": ["t_varchar"], "allReplace": false, "username": "SYSTEM", "password": "Abc!@#579", "connection": [{"jdbcUrl": "**************************", "table": ["T_CHARACTER_SINK1"]}], "column": [{"name": "t_varchar", "type": "decimal"}, {"name": "t_nvarchar", "type": "decimal"}, {"name": "t_alphanum", "type": "string"}, {"name": "t_shorttext", "type": "string"}]}}}], "setting": {"speed": {"channel": 1, "bytes": 0}, "errorLimit": {"record": 1}, "restore": {"maxRowNumForCheckpoint": 0, "isRestore": false, "restoreColumnName": "", "restoreColumnIndex": 0}, "log": {"isLogger": false, "level": "debug", "path": "", "pattern": ""}}}}