{"job": {"content": [{"reader": {"parameter": {"sliceRecordCount": ["10"], "column": [{"name": "uuid", "type": "string"}, {"name": "name", "type": "string"}, {"name": "age", "type": "int"}, {"name": "ts", "type": "timestamp"}, {"name": "partition", "type": "string", "value": "20230101"}]}, "name": "streamreader"}, "writer": {"parameter": {"hoodieTableName": "xiaohe_test_0062_noright", "path": "/xiaohe_db/xiaohe_test_0062_no3", "tableType": "COPY_ON_WRITE", "hadoopConfig": {"fs.defaultFS": "hdfs://ns1", "hadoop.proxy.enable": "true", "yarn.resourcemanager.ha.rm-ids": "rm1,rm2", "dfs.client.failover.proxy.provider.ns1": "org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider", "dfs.client.block.write.replace-datanode-on-failure.enable": true, "dfs.nameservices": "ns1", "sftpConf": {"maxWaitMillis": "3600000", "minIdle": "16", "auth": "1", "isUsePool": "true", "timeout": "3000", "path": "/home/<USER>/sftp", "password": "password", "maxIdle": "16", "port": "22", "maxTotal": "16", "host": "************", "fileTimeout": "300000", "username": "admin"}, "fs.hdfs.impl.disable.cache": "true", "yarn.mapreduce.framework.name": "yarn", "dfs.ha.namenodes.ns1": "nn1,nn2", "principal": "hive/<EMAIL>", "dfs.client.block.write.replace-datanode-on-failure.policy": "NEVER", "yarn.resourcemanager.address.rm1": "devops-hadoop3-krb-node1:8032", "hadoop.proxy.enabled": "true", "dfs.namenode.kerberos.principal": "hdfs/<EMAIL>", "yarn.resourcemanager.ha.enabled": "true", "yarn.resourcemanager.address.rm2": "devops-hadoop3-krb-node2:8032", "java.security.krb5.realm": "DTSTACK.COM", "hadoop.proxy.user.name": "hive", "hadoop.security.authorization": true, "dfs.namenode.rpc-address.ns1.nn2": "devops-hadoop3-krb-node2:9000", "dfs.namenode.rpc-address.ns1.nn1": "devops-hadoop3-krb-node1:9000", "yarn.resourcemanager.principal": "yarn/<EMAIL>", "hadoop.user.name": "dev", "hadoop.security.authentication": "<PERSON><PERSON><PERSON>", "java.security.krb5.kdc": "*************:50088", "ipc.client.fallback-to-simple-auth-allowed": true, "java.security.krb5.conf": "krb5.conf", "fs.hdfs.impl": "org.apache.hadoop.hdfs.DistributedFileSystem", "dfs.data.transfer.protection": "integrity", "remoteDir": "/home/<USER>/sftp/hadoop_3_df", "principalFile": "hive.keytab", "useLocalFile": false}, "column": [{"name": "uuid", "type": "VARCHAR(20)"}, {"name": "name", "type": "VARCHAR(10)"}, {"name": "age", "type": "INT"}, {"name": "ts", "type": "TIMESTAMP"}, {"name": "partition", "type": "VARCHAR(20)"}], "hoodieDatabaseName": "hudi_test", "partitionPathFiled": "partition"}, "name": "hudiwriter"}}], "setting": {"speed": {"bytes": 0, "channel": 1}}}}