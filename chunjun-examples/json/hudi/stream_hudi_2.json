{"job": {"content": [{"reader": {"parameter": {"sliceRecordCount": ["10"], "column": [{"name": "uuid", "type": "string"}, {"name": "name", "type": "string"}, {"name": "age", "type": "int"}, {"name": "ts", "type": "timestamp"}, {"name": "partition", "type": "string"}]}, "name": "streamreader"}, "writer": {"parameter": {"path": "hdfs://ns1/dtInsight/hive/warehouse/wujuan/t1/", "hoodieDatabaseName": "wu<PERSON><PERSON>", "hoodieTableName": "t1", "tableType": "COPY_ON_WRITE", "partitionPathFiled": "partition", "hadoopConfig": {"dfs.nameservices": "ns1", "fs.defaultFS": "hdfs://ns1", "dfs.ha.namenodes.ns1": "nn1,nn2", "dfs.namenode.rpc-address.ns1.nn1": "*************:9000", "dfs.namenode.rpc-address.ns1.nn2": "*************:9000", "dfs.client.failover.proxy.provider.ns1": "org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider", "fs.hdfs.impl.disable.cache": "true", "fs.hdfs.impl": "org.apache.hadoop.hdfs.DistributedFileSystem", "yarn.mapreduce.framework.name": "yarn", "yarn.resourcemanager.ha.enabled": "true", "yarn.resourcemanager.ha.rm-ids": "rm1,rm2", "yarn.resourcemanager.address.rm1": "*************:8032", "yarn.resourcemanager.address.rm2": "*************:8032", "hadoop.user.name": "root", "HADOOP_USER_NAME": "root", "hadoop.security.authorization": "false", "hadoop.security.authentication": "false", "dfs.client.block.write.replace-datanode-on-failure.policy": "NEVER", "principalFile": "hudi.keytab", "java.security.krb5.conf": "krb5.conf", "useLocalFile": "true", "remoteDir": "", "sftpConf": {"host": "flink01", "port": "22", "username": "root", "password": "Abc!@#135"}}, "column": [{"name": "uuid", "type": "VARCHAR(20)"}, {"name": "name", "type": "VARCHAR(10)"}, {"name": "age", "type": "INT"}, {"name": "ts", "type": "TIMESTAMP"}, {"name": "partition", "type": "VARCHAR(20)"}]}, "name": "hudiwriter"}}], "setting": {"speed": {"bytes": 0, "channel": 1}}}}