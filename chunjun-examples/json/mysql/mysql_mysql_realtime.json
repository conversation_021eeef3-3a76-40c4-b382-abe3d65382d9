{"job": {"content": [{"reader": {"name": "mysqlreader", "parameter": {"column": [{"name": "id", "type": "int"}, {"name": "name", "type": "string"}, {"name": "money", "type": "string"}, {"name": "aphone", "type": "bigint"}, {"name": "aqq", "type": "<PERSON><PERSON><PERSON>"}, {"name": "awechat", "type": "<PERSON><PERSON><PERSON>"}, {"name": "aincome", "type": "decimal"}, {"name": "abirthday", "type": "datetime"}, {"name": "atoday", "type": "date"}, {"name": "atimecurrent", "type": "time"}, {"name": "bphone", "type": "bigint"}, {"name": "bqq", "type": "<PERSON><PERSON><PERSON>"}, {"name": "bwe<PERSON>t", "type": "<PERSON><PERSON><PERSON>"}, {"name": "bincome", "type": "decimal"}, {"name": "bbirthday", "type": "datetime"}, {"name": "btoday", "type": "date"}, {"name": "btimecurrent", "type": "time"}, {"name": "raw_date", "type": "string"}, {"name": "aboolean", "type": "boolean"}, {"name": "adouble", "type": "double"}, {"name": "afloat", "type": "float"}, {"name": "achar", "type": "char"}, {"name": "abinary", "type": "binary"}, {"name": "atinyint", "type": "tinyint"}, {"name": "areal", "type": "real"}, {"name": "atext", "type": "text"}], "customSql": "", "where": "id < 1000", "splitPk": "id", "increColumn": "id", "startLocation": "2", "polling": true, "pollingInterval": 3000, "queryTimeOut": 1000, "username": "root", "password": "root", "connection": [{"jdbcUrl": ["*********************************************"], "table": ["baserow"]}]}}, "writer": {"name": "mysqlwriter", "parameter": {"username": "root", "password": "root", "connection": [{"jdbcUrl": "*********************************************", "table": ["baserow_sync"]}], "writeMode": "insert", "flushIntervalMills": "3000", "column": [{"name": "id", "type": "int"}, {"name": "name", "type": "string"}, {"name": "money", "type": "string"}, {"name": "aphone", "type": "bigint"}, {"name": "aqq", "type": "<PERSON><PERSON><PERSON>"}, {"name": "awechat", "type": "<PERSON><PERSON><PERSON>"}, {"name": "aincome", "type": "decimal"}, {"name": "abirthday", "type": "datetime"}, {"name": "atoday", "type": "date"}, {"name": "atimecurrent", "type": "time"}, {"name": "bphone", "type": "bigint"}, {"name": "bqq", "type": "<PERSON><PERSON><PERSON>"}, {"name": "bwe<PERSON>t", "type": "<PERSON><PERSON><PERSON>"}, {"name": "bincome", "type": "decimal"}, {"name": "bbirthday", "type": "datetime"}, {"name": "btoday", "type": "date"}, {"name": "btimecurrent", "type": "time"}, {"name": "raw_date", "type": "string"}, {"name": "aboolean", "type": "boolean"}, {"name": "adouble", "type": "double"}, {"name": "afloat", "type": "float"}, {"name": "achar", "type": "char"}, {"name": "abinary", "type": "binary"}, {"name": "atinyint", "type": "tinyint"}, {"name": "areal", "type": "real"}, {"name": "atext", "type": "text"}]}}}], "setting": {"restore": {"restoreColumnName": "id"}, "speed": {"channel": 1, "bytes": 0}}}}