{"job": {"content": [{"reader": {"parameter": {"column": [{"name": "catalogueType", "type": "string", "value": "TaskDevelop"}, {"name": "nodePid", "type": "int", "value": 2265}, {"name": "isGetFile", "type": "boolean", "value": true}], "sliceRecordCount": ["1"]}, "name": "streamreader"}, "writer": {"parameter": {"url": "http://dev.insight.dtstack.cn/api/streamapp/service/streamCatalogue/getCatalogue", "header": {"Content-Type": "text/plain;charset=UTF-8", "Cookie": "experimentation_subject_id=IjY0NDJmMTZmLTIxOGUtNDcyNC05YTE1LWM3MzI3MmIxN2VlMSI%3D--df7c7580285ac936e771505ba7faa8de896de6dd; dt_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************.6cuG5Zuo0GzBtnfEPWDyFqAac1Umh3HbxycbEN5fGsg; dt_tenant_id=1; dt_tenant_name=DTStack%E7%A7%9F%E6%88%B7; dt_product_code=RDOS; SESSION=ODA3OTcyZDktNjYwZi00NjE4LThlOWQtYTZkMTM5NzA0YjE2; dt_is_tenant_admin=true; dt_is_tenant_creator=false; JSESSIONID=C3C5CB005F442FF79F295DE3437FDE21; DT_SESSION_ID=ad2980ab-a7e6-4a01-b709-d984a504d45e"}, "body": {}, "method": "post", "params": {}, "column": [{"name": "catalogueType", "value": "TaskDevelop"}, {"name": "nodePid", "value": 2265}, {"name": "isGetFile", "value": "true"}]}, "name": "restapiwriter"}}], "setting": {"speed": {"bytes": 0, "channel": 1}}}}