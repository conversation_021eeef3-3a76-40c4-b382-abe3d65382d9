{"job": {"content": [{"reader": {"parameter": {"password": "DT@Stack#123", "split": true, "port": 3306, "ddlSkip": false, "cat": "insert,update,delete", "host": "**************", "jdbcUrl": "********************************************************", "start": {}, "pavingData": false, "table": ["automation.kungen3"], "initialTableStructure": false, "username": "drpeco", "enableFetchAll": true}, "name": "binlogreader"}, "writer": {"parameter": {"uri": "thrift://172-16-23-238:9083", "warehouse": "hdfs:///dtInsight/hive/warehouse", "database": "kungen", "table": "kk8", "batchSize": 5000, "hadoopConfig": {"fs.defaultFS": "hdfs://ns1", "dfs.nameservices": "ns1", "dfs.ha.namenodes.ns1": "nn1,nn2", "dfs.namenode.rpc-address.ns1.nn1": "*************:9000", "dfs.namenode.rpc-address.ns1.nn2": "*************:9000", "dfs.client.failover.proxy.provider.ns1": "org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider"}, "partition": "year(date)", "executeDdlAble": true}, "name": "icebergwriter"}, "restoration": {"cache": {"cacheTimeout": 60000, "cacheSize": 1000, "maxBytes": 102400, "type": "mysql", "properties": {"database": "automation", "password": "DT@Stack#123", "table": "transaction_data_cdc_test_new", "url": "*******************************************", "username": "drpeco"}}, "workerMax": 3, "workerSize": 3, "workerNum": 2, "ddl": {"fetchInterval": 3000, "type": "mysql", "properties": {"database": "automation", "password": "DT@Stack#123", "table": "ddl_change_cdc_test_new", "url": "*******************************************", "username": "drpeco"}}, "stateRecover": {"type": "mysql", "properties": {"database": "test", "password": "DT@Stack#123", "table": "ddl_huigui_task_0117_state_recover", "url": "*************************************", "username": "drpeco"}}}}], "setting": {"restore": {"maxRowNumForCheckpoint": 0, "isRestore": false, "restoreColumnName": "", "restoreColumnIndex": 0}, "errorLimit": {"record": 100}, "speed": {"bytes": 0, "channel": 1}}}}