{"job": {"content": [{"reader": {"parameter": {"column": [{"name": "id", "type": "id"}, {"name": "name", "type": "int"}, {"name": "age", "type": "int"}], "sliceRecordCount": [3]}, "name": "streamreader"}, "writer": {"parameter": {"hbaseConfig": {"hbase.zookeeper.quorum": "tdh02,tdh03,tdh01", "hbase.zookeeper.property.clientPort": "2291", "hbase.master.kerberos.principal": "hbase/_HOST@TDH", "hbase.regionserver.kerberos.principal": "hbase/_HOST@TDH", "zookeeper.znode.parent": "/hyperbase1", "hbase.security.authentication": "kerber<PERSON>", "hadoop.security.authentication": "kerber<PERSON>", "useLocalFile": "false", "remoteDir": "/home/<USER>/test/script/sql/hyperbase", "principalFile": "hyperbase.keytab", "sftpConf": {"host": "flikx01", "port": "22", "username": "root", "password": "Abc!@#135"}, "principal": "hbase/tdh02@TDH", "java.security.krb5.conf": "krb5.conf", "isAddSecurityModule": "true", "jaasSectionName": "Client"}, "rowKeyExpress": {"liuliu.test_1": "$(id)"}, "connection": [{"jdbcUrl": "****************************************************************", "username": "", "password": "", "table": ["liuliu.test_1"]}], "column": [{"name": "id", "type": "id"}, {"name": "name", "type": "int"}, {"name": "age", "type": "int"}], "encoding": "utf-8", "isBinaryRowkey": true}, "name": "inceptorwriter"}}], "setting": {"restore": {"isRestore": false, "isStream": false}, "errorLimit": {"record": 0}, "speed": {"readerChannel": 1, "writerChannel": 1, "bytes": -1048576, "channel": 1}}}}