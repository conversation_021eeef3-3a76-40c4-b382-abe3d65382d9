/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.common.security;

import org.apache.commons.collections.MapUtils;

import java.util.Map;

public class SftpUtil {
    private static final String KEY_SFTP_CONF = "sftpConf";

    private static final String KEY_USERNAME = "username";
    private static final String KEY_PASSWORD = "password";
    private static final String KEY_HOST = "host";
    private static final String KEY_PORT = "port";
    private static final String KEY_TIMEOUT = "timeout";

    public static Map<String, Object> getSftpConf(Map<String, Object> config) {
        return MapUtils.getMap(config, KEY_SFTP_CONF);
    }

    public static String getHost(Map<String, Object> config) {
        return MapUtils.getString(config, KEY_HOST);
    }

    public static int getPort(Map<String, Object> config) {
        return MapUtils.getIntValue(config, KEY_PORT, 22);
    }

    public static String getUserName(Map<String, Object> config) {
        return MapUtils.getString(config, KEY_USERNAME);
    }

    public static String getPassword(Map<String, Object> config) {
        return MapUtils.getString(config, KEY_PASSWORD);
    }

    public static int getTimeOut(Map<String, Object> config) {
        return MapUtils.getIntValue(config, KEY_TIMEOUT, 0);
    }
}
