/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.chunjun.common.security;

import com.dtstack.chunjun.common.constant.ConstantValue;
import com.dtstack.chunjun.common.util.FileSystemUtil;
import com.dtstack.chunjun.common.util.JsonUtil;
import com.dtstack.chunjun.common.util.Md5Util;

import org.apache.flink.annotation.VisibleForTesting;
import org.apache.flink.api.common.cache.DistributedCache;
import org.apache.flink.runtime.security.modules.SecurityModule;
import org.apache.flink.util.concurrent.ExecutorThreadFactory;

import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.hadoop.security.authentication.util.KerberosName;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sun.security.krb5.Config;
import sun.security.krb5.KrbException;
import sun.security.krb5.internal.ktab.KeyTab;
import sun.security.krb5.internal.ktab.KeyTabEntry;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2019/8/20
 */
public class KerberosUtil {

    public static Logger LOG = LoggerFactory.getLogger(KerberosUtil.class);

    public static final String SP = "/";

    public static final String KEY_SFTP_CONF = "sftpConf";
    public static final String KEY_PRINCIPAL = "principal";
    public static final String KEY_REMOTE_DIR = "remoteDir";
    public static final String KEY_USE_LOCAL_FILE = "useLocalFile";
    public static final String KEY_PRINCIPAL_FILE = "principalFile";
    public static final String KEY_JAVA_SECURITY_KRB5_CONF = "java.security.krb5.conf";
    public static final String KRB_STR = "Kerberos";
    public static final String HADOOP_AUTH_KEY = "hadoop.security.authentication";
    public static final String KRB5_CONF_KEY = "java.security.krb5.conf";
    public static final String KEY_ADD_SECURITY_MODULE = "isAddSecurityModule";
    public static final String KEY_JAAS_SECTION_NAME = "jaasSectionName";

    private static final String LOCAL_CACHE_DIR;

    private static final String KEY_HADOOP_PROXY_ENABLE = "hadoop.proxy.enable";
    private static final String KEY_HADOOP_PROXY_USERNAME = "hadoop.proxy.user.name";

    private static final String KEY_HADOOP_USER_NAME = "hadoop.user.name";

    static {
        String systemInfo = System.getProperty(ConstantValue.SYSTEM_PROPERTIES_KEY_OS);
        if (systemInfo.toLowerCase().startsWith(ConstantValue.OS_WINDOWS)) {
            LOCAL_CACHE_DIR = System.getProperty(ConstantValue.SYSTEM_PROPERTIES_KEY_USER_DIR);
        } else {
            String userName = System.getProperty("user.name");
            LOCAL_CACHE_DIR = "/tmp/" + userName + "/flinkx/keytab";
        }

        createDir(LOCAL_CACHE_DIR);
    }

    public static UserGroupInformation loginAndReturnUgi(
            Map<String, Object> hadoopConfig,
            DistributedCache distributedCache,
            String jobId,
            String taskNumber) {
        String keytabFileName = KerberosUtil.getPrincipalFileName(hadoopConfig);
        keytabFileName =
                KerberosUtil.loadFile(
                        hadoopConfig, keytabFileName, distributedCache, jobId, taskNumber);

        String principal = KerberosUtil.getPrincipal(hadoopConfig, keytabFileName);
        KerberosUtil.loadKrb5Conf(hadoopConfig, distributedCache, jobId, taskNumber);

        Configuration conf = FileSystemUtil.getConfiguration(hadoopConfig, null);

        UserGroupInformation ugi;
        try {
            ugi = KerberosUtil.loginAndReturnUgi(conf, principal, keytabFileName);
        } catch (Exception e) {
            throw new RuntimeException("Login kerberos error:", e);
        }

        LOG.info("current ugi:{}", ugi);

        return ugi;
    }

    public static UserGroupInformation loginAndReturnUgi(KerberosConfig kerberosConfig)
            throws IOException {
        String principal = kerberosConfig.getPrincipal();
        String keytabPath = kerberosConfig.getKeytab();
        String krb5confPath = kerberosConfig.getKrb5conf();
        LOG.info("Kerberos login with principal: {} and keytab: {}", principal, keytabPath);
        return loginAndReturnUgi(principal, keytabPath, krb5confPath);
    }

    public static UserGroupInformation loginAndReturnUgi(
            Configuration conf, String principal, String keytab) throws IOException {
        if (conf == null) {
            throw new IllegalArgumentException("kerberos conf can not be null");
        }

        if (StringUtils.isEmpty(principal)) {
            throw new IllegalArgumentException("principal can not be null");
        }

        if (StringUtils.isEmpty(keytab)) {
            throw new IllegalArgumentException("keytab can not be null");
        }
        conf.set(HADOOP_AUTH_KEY, KRB_STR);
        UserGroupInformation.setConfiguration(conf);

        LOG.info("login user:{} with keytab:{}", principal, keytab);
        return UserGroupInformation.loginUserFromKeytabAndReturnUGI(principal, keytab);
    }

    public static UserGroupInformation loginAndReturnUgi(
            String principal, String keytab, String krb5Conf) throws IOException {
        if (StringUtils.isEmpty(principal)) {
            throw new IllegalArgumentException("principal can not be null");
        }

        if (StringUtils.isEmpty(keytab)) {
            throw new IllegalArgumentException("keytab can not be null");
        }

        if (StringUtils.isNotEmpty(krb5Conf)) {
            reloadKrb5conf(krb5Conf);
        }
        Configuration conf = new Configuration();
        conf.set(HADOOP_AUTH_KEY, KRB_STR);
        UserGroupInformation.setConfiguration(conf);
        LOG.info("login user:{} with keytab:{}", principal, keytab);
        return UserGroupInformation.loginUserFromKeytabAndReturnUGI(principal, keytab);
    }

    public static String getKrb5Conf(Map<String, Object> config) {
        return MapUtils.getString(config, KEY_JAVA_SECURITY_KRB5_CONF);
    }

    public static String getPrincipal(Map<String, Object> configMap) {
        return MapUtils.getString(configMap, KEY_PRINCIPAL);
    }

    public static String getPrincipal(Map<String, Object> configMap, String keytabPath) {
        String principal = getPrincipal(configMap);
        if (StringUtils.isEmpty(principal)) {
            principal = findPrincipalFromKeytab(keytabPath);
        }

        return principal;
    }

    public static String getHadoopProxyUserName(Map<String, Object> config) {
        boolean hadoopProxyEnabled =
                Boolean.parseBoolean(
                        String.valueOf(config.getOrDefault(KEY_HADOOP_PROXY_ENABLE, "false")));
        if (hadoopProxyEnabled) {
            return MapUtils.getString(config, KEY_HADOOP_PROXY_USERNAME);
        }
        return null;
    }

    public static String getHadoopUserName(Map<String, Object> config) {
        return MapUtils.getString(config, KEY_HADOOP_USER_NAME);
    }

    public static synchronized void reloadKrb5conf(String krb5confPath) {
        System.setProperty(KRB5_CONF_KEY, krb5confPath);
        LOG.info("set krb5 file:{}", krb5confPath);
        // 不刷新会读/etc/krb5.conf
        try {
            Config.refresh();
            KerberosName.resetDefaultRealm();
        } catch (KrbException e) {
            LOG.warn(
                    "resetting default realm failed, current default realm will still be used.", e);
        }
    }

    public static void loadKrb5Conf(
            Map<String, Object> kerberosConfig,
            DistributedCache distributedCache,
            String jobId,
            String taskNumber) {
        String krb5FilePath = MapUtils.getString(kerberosConfig, KEY_JAVA_SECURITY_KRB5_CONF);
        if (StringUtils.isEmpty(krb5FilePath)) {
            LOG.info("krb5 file is empty,will use default file");
            return;
        }

        krb5FilePath = loadFile(kerberosConfig, krb5FilePath, distributedCache, jobId, taskNumber);
        kerberosConfig.put(KEY_JAVA_SECURITY_KRB5_CONF, krb5FilePath);
        System.setProperty(KEY_JAVA_SECURITY_KRB5_CONF, krb5FilePath);
    }

    /**
     * kerberosConfig { "principalFile":"keytab.keytab", "remoteDir":"/home/<USER>", "sftpConf":{
     * "path" : "/home/<USER>", "password" : "******", "port" : "22", "auth" : "1", "host" :
     * "127.0.0.1", "username" : "admin" } }
     */
    public static String loadFile(
            Map<String, Object> kerberosConfig,
            String filePath,
            DistributedCache distributedCache,
            String jobId,
            String taskNumber) {
        boolean useLocalFile = MapUtils.getBooleanValue(kerberosConfig, KEY_USE_LOCAL_FILE);
        if (useLocalFile) {
            LOG.info("will use local file:{}", filePath);
            checkFileExists(filePath);
            return filePath;
        } else {
            String fileName = new File(filePath).getName();
            if (StringUtils.startsWith(fileName, "blob_")) {
                // already downloaded from blobServer
                LOG.info("file [{}] already downloaded from blobServer", filePath);
                return filePath;
            }
            if (distributedCache != null) {
                try {
                    File file = distributedCache.getFile(fileName);
                    String absolutePath = file.getAbsolutePath();
                    LOG.info(
                            "load file [{}] from Flink BlobServer, download file path = {}",
                            fileName,
                            absolutePath);
                    return absolutePath;
                } catch (Exception e) {
                    LOG.warn(
                            "failed to get [{}] from Flink BlobServer, try to get from sftp. e = {}",
                            fileName,
                            e.getMessage());
                }
            }

            fileName = loadFromSftp(kerberosConfig, fileName, jobId, taskNumber);
            return fileName;
        }
    }

    public static void checkFileExists(String filePath) {
        File file = new File(filePath);
        if (file.exists()) {
            if (file.isDirectory()) {
                throw new RuntimeException("keytab is a directory:" + filePath);
            }
        } else {
            throw new RuntimeException("keytab file not exists:" + filePath);
        }
    }

    private static String loadFromSftp(
            Map<String, Object> config, String fileName, String jobId, String taskNumber) {
        String remoteDir = MapUtils.getString(config, KEY_REMOTE_DIR);
        if (StringUtils.isBlank(remoteDir)) {
            throw new RuntimeException(
                    "can't find [remoteDir] in config: \n" + JsonUtil.toPrintJson(config));
        }
        String filePathOnSftp = remoteDir + "/" + fileName;

        if (StringUtils.isBlank(jobId)) {
            // 创建分片在 JobManager， 此时还没有 JobId，随机生成UUID
            jobId = UUID.randomUUID().toString();
            LOG.warn("jobId is null, jobId will be replaced with [UUID], jobId(UUID) = {}.", jobId);
        }

        if (StringUtils.isBlank(taskNumber)) {
            taskNumber = UUID.randomUUID().toString();
            LOG.warn(
                    "taskNumber is null, taskNumber will be replaced with [UUID], taskNumber(UUID) = {}.",
                    taskNumber);
        }

        String localDirName = Md5Util.getMd5(remoteDir);
        String localDir = LOCAL_CACHE_DIR + SP + jobId + SP + taskNumber + SP + localDirName;
        // 创建 TM 节点的本地目录 /tmp/flinkx/keytab/${jobId}/${taskNumber}/${md5(remoteDir)}/*.keytab
        // ② ${jobId} 解决多个任务在同一个 TM 上面，keytab 覆盖问题。
        // ④ ${taskNumber} subTask编号
        // ③ ${md5(remoteDir)} 解决 reader writer 在同一个 TM 上面，keytab 覆盖问题。
        createDir(localDir);
        String fileLocalPath = localDir + SP + fileName;
        // 更新sftp文件对应的local文件
        if (fileExists(fileLocalPath)) {
            detectFile(fileLocalPath);
        }
        SftpHandler handler = null;
        try {
            Map sFTPConfMap;
            Object sftpConf = config.get(KerberosUtil.KEY_SFTP_CONF);
            if (sftpConf instanceof String) {
                sFTPConfMap =
                        JsonUtil.toObject(
                                (String) sftpConf, new TypeReference<Map<String, String>>() {});
            } else {
                sFTPConfMap = MapUtils.getMap(config, KEY_SFTP_CONF);
            }
            SftpHandler.checkConfig(sFTPConfMap);
            handler = SftpHandler.getInstanceWithRetry(sFTPConfMap);
            if (handler.isFileExist(filePathOnSftp)) {
                handler.downloadFileWithRetry(filePathOnSftp, fileLocalPath);
                LOG.info("download file:{} to local:{}", filePathOnSftp, fileLocalPath);
                return fileLocalPath;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (handler != null) {
                handler.close();
            }
        }

        throw new RuntimeException("File[" + filePathOnSftp + "] not exist on sftp");
    }

    @VisibleForTesting
    protected static String findPrincipalFromKeytab(String keytabFile) {
        KeyTab keyTab = KeyTab.getInstance(keytabFile);
        for (KeyTabEntry entry : keyTab.getEntries()) {
            String principal = entry.getService().getName();

            LOG.info("parse principal:{} from keytab:{}", principal, keytabFile);
            return principal;
        }

        return null;
    }

    private static void detectFile(String filePath) {
        if (fileExists(filePath)) {
            File file = new File(filePath);
            if (file.delete()) {
                LOG.info(file.getName() + " is deleted！");
            } else {
                LOG.error("deleted " + file.getName() + " failed！");
            }
        }
    }

    @VisibleForTesting
    protected static boolean fileExists(String filePath) {
        File file = new File(filePath);
        return file.exists() && file.isFile();
    }

    @VisibleForTesting
    protected static void createDir(String dir) {
        File file = new File(dir);
        if (file.exists()) {
            return;
        }

        boolean result = file.mkdirs();
        if (!result) {
            LOG.warn("Create dir failure:{}", dir);
        }

        LOG.info("create local dir:{}", dir);
    }

    public static String getPrincipalFileName(Map<String, Object> config) {
        return MapUtils.getString(config, KEY_PRINCIPAL_FILE);
    }

    public static String getSectionName(Map<String, Object> config) {
        String sectionName = MapUtils.getString(config, KEY_JAAS_SECTION_NAME);
        if (StringUtils.isEmpty(sectionName)) {
            LOG.warn("sectionName is not specified, so not add SecurityModule");
            return "Client";
        }
        return sectionName;
    }

    public static boolean addJaasModule(Map<String, Object> config) {
        return MapUtils.getBooleanValue(config, KEY_ADD_SECURITY_MODULE);
    }

    /** 刷新krb内容信息 */
    public static void refreshConfig() {
        try {
            Config.refresh();
            Field defaultRealmField = KerberosName.class.getDeclaredField("defaultRealm");
            defaultRealmField.setAccessible(true);
            defaultRealmField.set(
                    null,
                    org.apache.hadoop.security.authentication.util.KerberosUtil.getDefaultRealm());
            // reload java.security.auth.login.config
            javax.security.auth.login.Configuration.setConfiguration(null);
        } catch (Exception e) {
            LOG.warn(
                    "resetting default realm failed, current default realm will still be used.", e);
        }
    }

    public static ExecutorService scheduleRefreshTGT(UserGroupInformation ugi) {
        ScheduledExecutorService executor =
                Executors.newSingleThreadScheduledExecutor(
                        new ExecutorThreadFactory("UserGroupInformation-Relogin"));

        executor.scheduleWithFixedDelay(
                () -> {
                    try {
                        ugi.checkTGTAndReloginFromKeytab();
                    } catch (Exception e) {
                        LOG.error("Refresh TGT failed", e);
                    }
                },
                0,
                20,
                TimeUnit.MINUTES);
        return executor;
    }

    public static CustomModule installCustomModule(
            Map<String, Object> kerberosConfig,
            DistributedCache distributedCache,
            String jobId,
            String taskNumber)
            throws SecurityModule.SecurityInstallException {
        boolean isAddSecurityModule =
                MapUtils.getBooleanValue(kerberosConfig, KEY_ADD_SECURITY_MODULE);
        if (!isAddSecurityModule) {
            return null;
        }
        String keytabFileName = KerberosUtil.getPrincipalFileName(kerberosConfig);
        keytabFileName =
                KerberosUtil.loadFile(
                        kerberosConfig, keytabFileName, distributedCache, jobId, taskNumber);
        String principal = KerberosUtil.getPrincipal(kerberosConfig, keytabFileName);
        String jaasSectionName = KerberosUtil.getSectionName(kerberosConfig);
        LOG.info("CustomModule Installing...");
        return installCustomModule(keytabFileName, principal, jaasSectionName, true);
    }

    public static CustomModule installCustomModule(
            String keytab, String principal, String jaasSectionName, boolean useTicketCache)
            throws SecurityModule.SecurityInstallException {
        CustomModule customModule =
                new CustomModule(keytab, principal, jaasSectionName, useTicketCache);
        customModule.install();
        LOG.info("CustomModule Installed.");
        return customModule;
    }

    public static void unInstallCustomModule(CustomModule customModule)
            throws SecurityModule.SecurityInstallException {
        if (customModule != null) {
            customModule.uninstall();
            LOG.info("CustomModule Uninstalled");
        }
    }
}
